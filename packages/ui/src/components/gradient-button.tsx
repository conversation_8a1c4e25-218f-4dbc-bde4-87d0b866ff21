"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { ArrowUpRight } from "lucide-react";

import { cn } from "@chromify/ui/lib/utils";

const gradientButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "relative overflow-hidden bg-background text-foreground hover:bg-background/90",
        glow: "relative bg-background/90 text-foreground shadow-lg hover:bg-background/70 border border-border",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface GradientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof gradientButtonVariants> {
  asChild?: boolean;
  gradientClassName?: string;
  icon?: React.ReactNode;
  label?: string;
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      asChild = false,
      gradientClassName,
      icon = <ArrowUpRight className="w-4 h-4" />,
      label,
      children,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";
    const content = children || label || "Click me";

    // Default gradient class if not provided
    const defaultGradientClass =
      "bg-gradient-to-r from-[var(--chart-1)] via-[var(--chart-2)] to-[var(--chart-3)]";

    // Use custom gradient or default
    const gradientClass = gradientClassName || defaultGradientClass;

    if (variant === "glow") {
      return (
        <div className="relative inline-flex group p-0.5 overflow-visible">
          {/* Gradient border container */}
          <div
            className={cn(
              "absolute -inset-0.5 rounded-md opacity-75 blur-sm transition-all",
              gradientClass,
              "group-hover:opacity-100 group-hover:blur-md"
            )}
          />

          <Comp
            ref={ref}
            className={cn(gradientButtonVariants({ variant, size, className }))}
            {...props}
          >
            {asChild ? (
              // When using Slot (asChild=true), only pass the content as a single child
              content
            ) : (
              // For regular button, we can have multiple children
              <>
                {content}
                {icon}
              </>
            )}
          </Comp>
        </div>
      );
    }

    // Default variant
    return (
      <Comp
        ref={ref}
        className={cn(gradientButtonVariants({ variant, size, className }))}
        {...props}
      >
        {asChild ? (
          // When using Slot (asChild=true), only pass the content as a single child
          content
        ) : (
          // For regular button, we can have multiple children
          <>
            {/* Gradient background effect */}
            <div
              className={cn(
                "absolute inset-0 -z-10",
                gradientClass,
                "opacity-40 hover:opacity-80",
                "transition-opacity duration-300"
              )}
            />
            {content}
            {icon}
          </>
        )}
      </Comp>
    );
  }
);

GradientButton.displayName = "GradientButton";

export { GradientButton, gradientButtonVariants };
