"use client";

import { memo, useEffect, useRef } from "react";
import { animate } from "motion/react";

import { cn } from "@chromify/ui/lib/utils";

interface AnimatedBorderProps {
  blur?: number;
  spread?: number;
  variant?: "default" | "white";
  glow?: boolean;
  className?: string;
  animationDuration?: number;
  borderWidth?: number;
  paused?: boolean;
}

const AnimatedBorder = memo(
  ({
    blur = 0,
    spread = 60,
    variant = "default",
    glow = true,
    className,
    animationDuration = 8,
    borderWidth = 1,
    paused = false,
  }: AnimatedBorderProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const animationRef = useRef<{ stop: () => void } | null>(null);

    useEffect(() => {
      const element = containerRef.current;
      if (!element || paused) return;

      // Set initial angle
      element.style.setProperty("--active", "1");
      element.style.setProperty("--start", "0");

      // Start continuous rotation animation
      const controls = animate(0, 360, {
        duration: animationDuration,
        repeat: Infinity,
        ease: "linear",
        onUpdate: (value) => {
          if (element) {
            element.style.setProperty("--start", String(value));
          }
        },
      });

      animationRef.current = controls;

      return () => {
        if (animationRef.current) {
          animationRef.current.stop();
        }
      };
    }, [animationDuration, paused]);

    return (
      <>
        <div
          className={cn(
            "pointer-events-none absolute -inset-px rounded-[inherit] border opacity-0 transition-opacity",
            glow && "opacity-100",
            variant === "white" && "border-white"
          )}
        />
        <div
          ref={containerRef}
          style={
            {
              "--blur": `${blur}px`,
              "--spread": spread,
              "--start": "0",
              "--active": "1",
              "--glowingeffect-border-width": `${borderWidth}px`,
              "--repeating-conic-gradient-times": "5",
              "--gradient":
                variant === "white"
                  ? `repeating-conic-gradient(
                  from 236.84deg at 50% 50%,
                  var(--black),
                  var(--black) calc(25% / var(--repeating-conic-gradient-times))
                )`
                  : `radial-gradient(circle, var(--chart-1) 10%, #dd7bbb00 20%),
                radial-gradient(circle at 40% 40%, var(--chart-2) 5%, #d79f1e00 15%),
                radial-gradient(circle at 60% 60%, var(--chart-3) 10%, #5a922c00 20%), 
                radial-gradient(circle at 40% 60%, var(--chart-4) 10%, #4c789400 20%),
                repeating-conic-gradient(
                  from 236.84deg at 50% 50%,
                  var(--chart-1) 0%,
                  var(--chart-2) calc(25% / var(--repeating-conic-gradient-times)),
                  var(--chart-3) calc(50% / var(--repeating-conic-gradient-times)), 
                  var(--chart-4) calc(75% / var(--repeating-conic-gradient-times)),
                  var(--chart-1) calc(100% / var(--repeating-conic-gradient-times))
                )`,
            } as React.CSSProperties
          }
          className={cn(
            "pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity",
            glow && "opacity-100",
            blur > 0 && "blur-[var(--blur)] ",
            className
          )}
        >
          <div
            className={cn(
              "glow",
              "rounded-[inherit]",
              'after:content-[""] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',
              "after:[border:var(--glowingeffect-border-width)_solid_transparent]",
              "after:[background:var(--gradient)] after:[background-attachment:fixed]",
              "after:opacity-[var(--active)] after:transition-opacity after:duration-300",
              "after:[mask-clip:padding-box,border-box]",
              "after:[mask-composite:intersect]",
              "after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]"
            )}
          />
        </div>
      </>
    );
  }
);

AnimatedBorder.displayName = "AnimatedBorder";

export { AnimatedBorder };
