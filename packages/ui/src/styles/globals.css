@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Base light theme */
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* Primary teal from logo */
  --primary: oklch(0.7 0.12 178);
  --primary-foreground: oklch(0.985 0 0);

  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.85 0.08 180);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.7 0.12 178);

  /* Chart colors derived from teal logo */
  --chart-1: oklch(0.7 0.12 178); /* Main teal */
  --chart-2: oklch(0.6 0.14 160); /* Green-teal */
  --chart-3: oklch(0.65 0.1 195); /* Blue-teal */
  --chart-4: oklch(0.75 0.15 145); /* Complementary green */
  --chart-5: oklch(0.68 0.13 210); /* Complementary blue */

  /* Sidebar colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.7 0.12 178);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.85 0.08 180);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.7 0.12 178);

  /* Syntax highlighting */
  --sh-class: oklch(0.63 0.11 218);
  --sh-identifier: oklch(0.48 0.07 210);
  --sh-sign: oklch(0.55 0.06 203);
  --sh-property: oklch(0.65 0.15 183);
  --sh-entity: oklch(0.71 0.16 155);
  --sh-jsxliterals: oklch(0.61 0.12 227);
  --sh-string: oklch(0.63 0.16 138);
  --sh-keyword: oklch(0.63 0.14 239);
  --sh-comment: oklch(0.65 0.03 210);
  --sh-function: oklch(0.62 0.16 254);
  --sh-number: oklch(0.65 0.15 170);
  --sh-boolean: oklch(0.63 0.14 239);
  --sh-variable: oklch(0.48 0.07 210);
  --sh-constant: oklch(0.63 0.11 218);
  --sh-operator: oklch(0.55 0.06 203);
  --sh-regex: oklch(0.55 0.18 27);
}

/* Apply dark theme based on system preference if no JavaScript has run yet */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    /* Dark theme variables - same as .dark class below */
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.75 0.14 178);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.65 0.1 180);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.75 0.14 178);
    --chart-1: oklch(0.75 0.16 178);
    --chart-2: oklch(0.7 0.17 150);
    --chart-3: oklch(0.72 0.13 200);
    --chart-4: oklch(0.8 0.18 140);
    --chart-5: oklch(0.75 0.15 215);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.75 0.14 178);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.65 0.1 180);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.75 0.14 178);
    --sh-class: oklch(0.79 0.12 214);
    --sh-identifier: oklch(0.85 0.02 209);
    --sh-sign: oklch(0.71 0.02 208);
    --sh-property: oklch(0.77 0.14 183);
    --sh-entity: oklch(0.77 0.15 156);
    --sh-jsxliterals: oklch(0.74 0.13 235);
    --sh-string: oklch(0.77 0.15 139);
    --sh-keyword: oklch(0.74 0.13 235);
    --sh-comment: oklch(0.67 0.02 208);
    --sh-function: oklch(0.74 0.15 254);
    --sh-number: oklch(0.77 0.15 169);
    --sh-boolean: oklch(0.74 0.13 235);
    --sh-variable: oklch(0.83 0.02 209);
    --sh-constant: oklch(0.79 0.12 214);
    --sh-operator: oklch(0.78 0.02 208);
    --sh-regex: oklch(0.67 0.15 27);
  }
}

/* Dark theme when .dark class is explicitly applied */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);

  /* Primary teal - brighter for dark mode */
  --primary: oklch(0.75 0.14 178);
  --primary-foreground: oklch(0.205 0 0);

  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.65 0.1 180);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.75 0.14 178);

  /* Chart colors - adjusted for dark mode */
  --chart-1: oklch(0.75 0.16 178); /* Brighter teal */
  --chart-2: oklch(0.7 0.17 150); /* Brighter green-teal */
  --chart-3: oklch(0.72 0.13 200); /* Brighter blue-teal */
  --chart-4: oklch(0.8 0.18 140); /* Brighter green */
  --chart-5: oklch(0.75 0.15 215); /* Brighter blue */

  /* Sidebar dark mode */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.75 0.14 178);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.65 0.1 180);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.75 0.14 178);

  /* Syntax highlighting */
  --sh-class: oklch(0.79 0.12 214);
  --sh-identifier: oklch(0.85 0.02 209);
  --sh-sign: oklch(0.71 0.02 208);
  --sh-property: oklch(0.77 0.14 183);
  --sh-entity: oklch(0.77 0.15 156);
  --sh-jsxliterals: oklch(0.74 0.13 235);
  --sh-string: oklch(0.77 0.15 139);
  --sh-keyword: oklch(0.74 0.13 235);
  --sh-comment: oklch(0.67 0.02 208);
  --sh-function: oklch(0.74 0.15 254);
  --sh-number: oklch(0.77 0.15 169);
  --sh-boolean: oklch(0.74 0.13 235);
  --sh-variable: oklch(0.83 0.02 209);
  --sh-constant: oklch(0.79 0.12 214);
  --sh-operator: oklch(0.78 0.02 208);
  --sh-regex: oklch(0.67 0.15 27);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  button {
    @apply cursor-pointer;
  }

  [contenteditable]:empty:before {
    content: attr(data-placeholder);
    color: var(--muted-foreground);
    cursor: text;
    position: absolute;
    pointer-events: none;
  }

  [contenteditable]:focus {
    outline: none;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  pre {
    padding: 1rem;
    border-radius: var(--radius);
    overflow-x: auto;
    border: 1px solid hsl(var(--border));
  }

  pre code {
    counter-reset: sh-line-number;
  }

  .sh__line::before {
    counter-increment: sh-line-number 1;
    content: counter(sh-line-number);
    margin-right: 24px;
    text-align: right;
    color: #a4a4a4;
  }

  /* Line styling */
  .sh__line {
    display: inline-block;
    font-family: var(--font-mono);
  }
}

@theme {
  --animate-shimmer: shimmer 2.8s linear infinite;

  @keyframes shimmer {
    from {
      background-position: -100% 0;
    }
    to {
      background-position: 100% 0;
    }
  }
}

@utility container {
  padding-inline: 1rem;

  @variant sm {
    padding-inline: 1.5rem;
  }

  @variant md {
    padding-inline: 2rem;
  }

  @variant lg {
    padding-inline: 2.5rem;
  }

  @variant xl {
    padding-inline: 3rem;
  }
}
