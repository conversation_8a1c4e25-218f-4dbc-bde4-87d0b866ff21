{"name": "@chromify/eslint-config", "version": "0.0.0", "private": true, "type": "module", "exports": {"./next-js": "./next-js.js", "./react-internal": "./react-internal.js", "./base": "./base.js"}, "files": ["next-js.js", "react-internal.js", "base.js"], "dependencies": {"@eslint/js": "^9.0.0", "@next/eslint-plugin-next": "^15.2.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-turbo": "^1.12.5", "typescript-eslint": "^7.0.0"}, "peerDependencies": {"eslint": "^9.0.0", "typescript": ">=5.0.0"}}