# Chromify Documentation

Chromify is an AI-powered color system generator for modern web development. It helps developers, AI-assisted coders, and designers create sophisticated, accessible color schemes with the power of AI and expert color theory.

## Table of Contents

- [Overview](#overview)
- [Key Features](#key-features)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Theme Generation](#theme-generation)
- [UI Components](#ui-components)
- [Vibe Coding Integration](#vibe-coding-integration)
- [Credit System](#credit-system)
- [Customization](#customization)
- [Roadmap](#roadmap)
- [Pricing](#pricing)

## Overview

As vibe coding and AI-assisted development transform how we build applications, maintaining visual quality and consistency becomes both more important and more challenging. Chromify bridges this gap by bringing AI-powered color system generation to both traditional and AI-assisted development workflows.

Chromify is built as a monorepo using Next.js, React, and Tailwind CSS, with a focus on providing a seamless experience for generating sophisticated color systems that enhance the default components used in modern web development.

## Key Features

### AI-Powered Generation

Describe your vision in plain language and let our AI create the perfect color scheme for your project. The AI understands both color theory and development contexts, delivering sophisticated, accessible, and visually harmonious color systems that complement frameworks like shadcn/ui.

### Modern Format Support

Generate color themes in the format that matches your tech stack:

- HSL format for Tailwind CSS v3 projects
- OKLCH format for Tailwind CSS v4 projects with enhanced color perception

### Complete System Generation

Every color scheme includes all the tokens you need:

- Core UI colors (background, foreground, card, primary, secondary, etc.)
- Optional sidebar colors for applications with navigation panels
- Optional chart colors for data visualization
- All with properly balanced light and dark mode variants

### Light & Dark Modes

Every color scheme comes with perfectly balanced light and dark variants that maintain your brand identity while ensuring proper contrast and readability in both modes.

### Accessibility Tools

Ensure your color schemes meet WCAG accessibility guidelines with built-in contrast checking. All generated themes prioritize readability and usability.

### Instant Implementation

Copy your generated CSS variables with one click and paste them directly into your project's global CSS file. No complex configuration required.

### Theme Preview & Management

- Preview your generated themes in real UI components before implementing
- Save favorites and manage your theme history
- Share themes via public links
- Browse a marketplace of public themes created by other users

### Image-Based Generation

Upload images or provide URLs to extract color schemes that match your brand assets or design inspiration.

## Technology Stack

- **Frontend**: Next.js, React 19
- **Styling**: Tailwind CSS v4 with OKLCH color format support
- **UI Components**: shadcn/ui with custom components
- **State Management**: Zustand
- **Authentication**: Clerk with Supabase integration
- **AI Integration**: Multiple AI model support (deepseek-chat, anthropic, google)
- **Database**: Supabase with type-safe interactions
- **Monorepo Management**: Turborepo with pnpm

## Project Structure

The project is organized as a monorepo with the following structure:

```
Chromify-Mono/
├── apps/
│   ├── web/           # Main web application
│   └── doc/           # Documentation site
├── packages/
│   ├── ui/            # Shared UI components
│   ├── eslint-config/ # Shared ESLint configuration
│   └── typescript-config/ # Shared TypeScript configuration
```

## Getting Started

To start using Chromify:

1. Sign up for a free account (15 credits included)
2. Navigate to the Theme Generator
3. Describe your desired color scheme (e.g., "A professional fintech theme with navy blue as the primary color")
4. Customize options (format, dark mode, sidebar, chart colors)
5. Generate and preview your theme
6. Copy the CSS variables and implement them in your project

## Theme Generation

### How It Works

1. **Describe Your Vision**: Tell our AI what you're looking for in plain language. Mention colors, moods, industries, or styles that inspire you.
2. **Select Options**: Choose between HSL or OKLCH format, enable dark mode, and add optional sidebar or chart colors.
3. **Preview in Context**: See your colors applied to real UI components. Test accessibility and make sure everything looks great.
4. **Export & Use**: Copy your CSS variables and implement them in your project.

### Theme Options

- **Format**: Choose between HSL (Tailwind v3) or OKLCH (Tailwind v4) color formats
- **Dark Mode**: Generate a matching dark mode variant of your theme
- **Sidebar**: Include specialized sidebar colors for applications with sidebars
- **Chart Colors**: Generate a set of chart colors for data visualization
- **Border Radius**: Customize the roundness of UI elements

### Theme Structure

Each theme includes the following color tokens:

- Background/Foreground
- Card/Card Foreground
- Popover/Popover Foreground
- Primary/Primary Foreground
- Secondary/Secondary Foreground
- Muted/Muted Foreground
- Accent/Accent Foreground
- Destructive/Destructive Foreground
- Border, Input, Ring, Radius

Optional tokens include:

- Sidebar colors
- Chart colors

## UI Components

Chromify uses a custom UI component library based on shadcn/ui. These components are designed to be accessible, customizable, and work seamlessly with the generated themes.

Key components include:

- Button
- Card
- Dialog
- Dropdown Menu
- Input
- Separator
- Sheet
- Sidebar
- Skeleton
- Textarea
- and more...

All components support theme customization through CSS variables and are built with accessibility in mind.

## Vibe Coding Integration

Chromify is specifically designed to enhance vibe coding workflows, where developers use AI tools to generate significant portions of their codebase through natural language prompts.

### For Vibe Coding

1. Generate your UI components using tools like Cursor, Lovable, or other AI coding assistants
2. Use Chromify to create a matching color scheme by describing what you want in natural language
3. Implement the color system to add visual polish to your AI-generated components
4. Maintain visual quality even with rapidly developed AI-generated code

### For Traditional Development

1. Design your UI components and structure
2. Use Chromify to generate a cohesive color system
3. Implement the color variables in your project
4. Enjoy a professional, accessible visual experience

### Compatibility with Popular AI Coding Tools

Chromify integrates perfectly with the vibe coding ecosystem:

- **Cursor**: Enhance AI-generated code with professional color systems
- **Lovable**: Add polished themes to full-stack applications built with AI
- **GitHub Copilot**: Complement AI-suggested code with cohesive visual styling
- **Replit**: Quickly theme your AI-assisted projects

## Credit System

Chromify uses a credit-based system to manage theme generation:

- **New users**: 15 free credits upon signup
- **Basic theme**: 1 credit (Background/Foreground, Card, Primary, etc.)
- **Premium theme**: 2-3 credits (Basic + Sidebar, Chart colors)

Future plans include:

- Subscription tiers with monthly credits
- Credit purchase options
- Team credit sharing

## Customization

### Theme Customization

After generating a theme, you can:

- Adjust individual color values
- Toggle between light and dark mode
- Preview in different UI components
- Copy to your clipboard for implementation
- Save as a favorite
- Share via public link

### Application Settings

The application remembers your preferences, including:

- Light/dark/system mode preference
- Format preference (HSL or OKLCH)
- Optional token preferences (sidebar, charts)

## Roadmap

Chromify is actively developing new features to enhance your color system workflow:

### Coming Soon

- Favoriting other users' themes
- Color/hue-based theme grouping and filtering
- Theme tagging and categorization
- Theme comparison tool
- Theme analytics (views, copies, favorites)

### Future Plans

- Theme collections/folders
- Export themes to various formats (CSS, SCSS, JSON)
- Community features (profiles, following, activity feed)
- Premium subscription tiers
- API for third-party integrations

## Pricing

Chromify offers different pricing tiers to suit your needs:

- **Free**: 15 initial credits, basic theme generation
- **Pro (Coming Soon)**: 30 credits/month ($15/month)
- **Team (Coming Soon)**: 100 shared credits/month ($39/month)
- **Enterprise (Coming Soon)**: Custom solutions for teams and organizations

Visit the pricing page for more details on each plan.
