import { client } from "@/lib/supabase";
import { Database } from "@/lib/types/supabase";

// Type for theme data from database
export type Theme = Database["public"]["Tables"]["themes"]["Row"] & {
  theme_requests?: {
    user_prompt: string;
    status: Database["public"]["Enums"]["theme_status"];
    model: Database["public"]["Enums"]["theme_model"];
    include_dark_mode: boolean;
    include_sidebar: boolean;
    include_chart: boolean;
    border_radius: string;
  };
  user_profiles?: {
    name: string | null;
    subscription_tier: string | null;
  };
};

// Type for theme filter options
export interface ThemeFilterOptions {
  query?: string;
  format?: "hsl" | "oklch" | "all";
  status?: "all" | "public" | "private";
  hasDarkMode?: boolean;
}

// Type for theme sort options
export type ThemeSortOption = "newest" | "oldest" | "name-asc" | "name-desc";

/**
 * Get all themes (basic function for counting)
 */
export const getThemes = async () => {
  const { data, error } = await client
    .from("themes")
    .select("*", { count: "exact" });

  if (error) {
    console.error("Error fetching themes:", error);
    return [];
  }

  return data;
};

/**
 * Get themes with pagination, sorting, and filtering
 */
export const getThemesWithFilters = async (
  page: number = 1,
  pageSize: number = 10,
  sortOption: ThemeSortOption = "newest",
  filterOptions: ThemeFilterOptions = {}
): Promise<{ themes: Theme[]; totalThemes: number; totalPages: number }> => {
  try {
    // Calculate offset based on page number and page size
    const offset = (page - 1) * pageSize;

    // Start building the query
    let query = client.from("themes").select(
      `
        *,
        theme_requests!inner(user_prompt, status, model, include_dark_mode, include_sidebar, include_chart, border_radius),
        user_profiles!inner(name, subscription_tier)
      `,
      { count: "exact" }
    );

    // Apply filters
    if (filterOptions.query) {
      query = query.or(
        `name.ilike.%${filterOptions.query}%,theme_description.ilike.%${filterOptions.query}%,theme_requests.user_prompt.ilike.%${filterOptions.query}%`
      );
    }

    if (filterOptions.format && filterOptions.format !== "all") {
      query = query.eq("format", filterOptions.format);
    }

    if (filterOptions.status === "public") {
      query = query.eq("is_public", true);
    } else if (filterOptions.status === "private") {
      query = query.eq("is_public", false);
    }

    if (filterOptions.hasDarkMode) {
      query = query.not("dark_colors", "is", null);
    }

    // Apply sorting
    switch (sortOption) {
      case "newest":
        query = query.order("created_at", { ascending: false });
        break;
      case "oldest":
        query = query.order("created_at", { ascending: true });
        break;
      case "name-asc":
        query = query.order("name", { ascending: true });
        break;
      case "name-desc":
        query = query.order("name", { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + pageSize - 1);

    // Execute the query
    const { data: themes, error, count } = await query;

    if (error) {
      console.error("Error fetching themes:", error);
      return { themes: [], totalThemes: 0, totalPages: 0 };
    }

    // Calculate total pages
    const totalThemes = count || 0;
    const totalPages = Math.ceil(totalThemes / pageSize);

    return { themes: themes as Theme[], totalThemes, totalPages };
  } catch (error) {
    console.error("Error in getThemesWithFilters:", error);
    return { themes: [], totalThemes: 0, totalPages: 0 };
  }
};

/**
 * Get a single theme by ID
 */
export const getThemeById = async (id: string): Promise<Theme | null> => {
  try {
    const { data, error } = await client
      .from("themes")
      .select(
        `
        *,
        theme_requests!inner(user_prompt, status, model, include_dark_mode, include_sidebar, include_chart, border_radius),
        user_profiles!inner(name, subscription_tier)
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching theme:", error);
      return null;
    }

    return data as Theme;
  } catch (error) {
    console.error("Error in getThemeById:", error);
    return null;
  }
};
