export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      theme_requests: {
        Row: {
          border_radius: string
          created_at: string
          error_message: string | null
          format: Database["public"]["Enums"]["theme_format"]
          id: string
          image_path: string | null
          include_chart: boolean
          include_dark_mode: boolean
          include_sidebar: boolean
          model: Database["public"]["Enums"]["theme_model"]
          processing_time_ms: number | null
          status: Database["public"]["Enums"]["theme_status"]
          updated_at: string
          user_id: string
          user_prompt: string
        }
        Insert: {
          border_radius?: string
          created_at?: string
          error_message?: string | null
          format?: Database["public"]["Enums"]["theme_format"]
          id?: string
          image_path?: string | null
          include_chart?: boolean
          include_dark_mode?: boolean
          include_sidebar?: boolean
          model?: Database["public"]["Enums"]["theme_model"]
          processing_time_ms?: number | null
          status?: Database["public"]["Enums"]["theme_status"]
          updated_at?: string
          user_id: string
          user_prompt: string
        }
        Update: {
          border_radius?: string
          created_at?: string
          error_message?: string | null
          format?: Database["public"]["Enums"]["theme_format"]
          id?: string
          image_path?: string | null
          include_chart?: boolean
          include_dark_mode?: boolean
          include_sidebar?: boolean
          model?: Database["public"]["Enums"]["theme_model"]
          processing_time_ms?: number | null
          status?: Database["public"]["Enums"]["theme_status"]
          updated_at?: string
          user_id?: string
          user_prompt?: string
        }
        Relationships: [
          {
            foreignKeyName: "theme_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      theme_versions: {
        Row: {
          change_description: string | null
          colors: Json
          created_at: string
          created_by: string
          dark_colors: Json | null
          id: string
          theme_id: string
          version_number: number
        }
        Insert: {
          change_description?: string | null
          colors: Json
          created_at?: string
          created_by: string
          dark_colors?: Json | null
          id?: string
          theme_id: string
          version_number: number
        }
        Update: {
          change_description?: string | null
          colors?: Json
          created_at?: string
          created_by?: string
          dark_colors?: Json | null
          id?: string
          theme_id?: string
          version_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "theme_versions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_versions_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "theme_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_versions_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      themes: {
        Row: {
          analysis: Json | null
          colors: Json
          created_at: string
          dark_colors: Json | null
          format: Database["public"]["Enums"]["theme_format"]
          id: string
          is_favorite: boolean
          is_public: boolean
          name: string
          primary_hue: string | null
          request_id: string
          shared_id: string | null
          theme_description: string | null
          updated_at: string
          user_id: string
          version: number
        }
        Insert: {
          analysis?: Json | null
          colors: Json
          created_at?: string
          dark_colors?: Json | null
          format?: Database["public"]["Enums"]["theme_format"]
          id?: string
          is_favorite?: boolean
          is_public?: boolean
          name: string
          primary_hue?: string | null
          request_id: string
          shared_id?: string | null
          theme_description?: string | null
          updated_at?: string
          user_id: string
          version?: number
        }
        Update: {
          analysis?: Json | null
          colors?: Json
          created_at?: string
          dark_colors?: Json | null
          format?: Database["public"]["Enums"]["theme_format"]
          id?: string
          is_favorite?: boolean
          is_public?: boolean
          name?: string
          primary_hue?: string | null
          request_id?: string
          shared_id?: string | null
          theme_description?: string | null
          updated_at?: string
          user_id?: string
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "themes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "theme_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "themes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_profiles: {
        Row: {
          created_at: string
          id: string
          name: string | null
          subscription_tier: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          id: string
          name?: string | null
          subscription_tier?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string | null
          subscription_tier?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      theme_details: {
        Row: {
          analysis: Json | null
          border_radius: string | null
          colors: Json | null
          created_at: string | null
          dark_colors: Json | null
          format: Database["public"]["Enums"]["theme_format"] | null
          id: string | null
          image_path: string | null
          include_chart: boolean | null
          include_dark_mode: boolean | null
          include_sidebar: boolean | null
          is_favorite: boolean | null
          is_public: boolean | null
          model: Database["public"]["Enums"]["theme_model"] | null
          name: string | null
          primary_hue: string | null
          request_id: string | null
          shared_id: string | null
          status: Database["public"]["Enums"]["theme_status"] | null
          theme_description: string | null
          updated_at: string | null
          user_id: string | null
          user_prompt: string | null
          version: number | null
        }
        Relationships: [
          {
            foreignKeyName: "themes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "theme_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "themes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      complete_theme: {
        Args: {
          p_request_id: string
          p_name: string
          p_theme_description: string
          p_colors: Json
          p_dark_colors: Json
          p_analysis: Json
          p_primary_hue: string
          p_processing_time_ms: number
        }
        Returns: string
      }
      create_theme: {
        Args: {
          p_user_id: string
          p_user_prompt: string
          p_format: Database["public"]["Enums"]["theme_format"]
          p_include_dark_mode: boolean
          p_include_sidebar: boolean
          p_include_chart: boolean
          p_image_path: string
          p_model: Database["public"]["Enums"]["theme_model"]
          p_border_radius?: string
        }
        Returns: string
      }
      create_theme_version: {
        Args: {
          p_theme_id: string
          p_colors: Json
          p_dark_colors: Json
          p_change_description: string
        }
        Returns: string
      }
      create_user_profile: {
        Args: {
          p_user_id: string
          p_name: string
          p_subscription_tier?: string
        }
        Returns: undefined
      }
      delete_theme: {
        Args: { p_theme_id: string }
        Returns: undefined
      }
      fail_theme: {
        Args: { p_request_id: string; p_error_message: string }
        Returns: undefined
      }
      regenerate_theme_shared_id: {
        Args: { p_theme_id: string }
        Returns: string
      }
      requesting_user_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      toggle_theme_favorite: {
        Args: { p_theme_id: string }
        Returns: boolean
      }
      toggle_theme_public: {
        Args: { p_theme_id: string }
        Returns: boolean
      }
    }
    Enums: {
      theme_format: "hsl" | "oklch"
      theme_model: "deepseek-chat" | "anthropic" | "google"
      theme_status: "pending" | "completed" | "failed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      theme_format: ["hsl", "oklch"],
      theme_model: ["deepseek-chat", "anthropic", "google"],
      theme_status: ["pending", "completed", "failed"],
    },
  },
} as const
