import type { <PERSON>ada<PERSON> } from "next";
import { cookies } from "next/headers";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@chromify/ui/components/sidebar";
import { AdminSidebar } from "@/components/sidebar/admin-sidebar";

export const metadata: Metadata = {
  title: "Admin Dashboard - Manage Chromify",
  description:
    "Access the Chromify admin dashboard to manage users, themes, and system settings.",
};

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";
  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <AdminSidebar />
      <SidebarInset>
        <main>
          <SidebarTrigger />
          <div className="container mx-auto">{children}</div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
