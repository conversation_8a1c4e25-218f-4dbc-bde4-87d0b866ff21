import { Suspense } from "react";
import { Metadata } from "next";

import { ThemeFilters } from "@/app/dashboard/themes/_components/theme-filters";
import ThemesTable from "@/app/dashboard/themes/_components/themes-table";
import ThemesTableSkeleton from "@/app/dashboard/themes/_components/themes-table-skeleton";

export const metadata: Metadata = {
  title: "Themes Management - Admin Dashboard",
  description: "Manage and monitor all themes created by users.",
};

export default async function ThemesPage({
  searchParams,
}: {
  searchParams: { format?: string; status?: string };
}) {
  const format = searchParams.format;
  const status = searchParams.status;

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Themes Management</h1>
        <p className="text-muted-foreground">
          View and manage all themes created by users.
        </p>
      </div>

      <div className="rounded-md border p-4 bg-muted/10">
        <ThemeFilters />
      </div>

      <Suspense fallback={<ThemesTableSkeleton />}>
        <ThemesTable format={format} status={status} />
      </Suspense>
    </div>
  );
}
