import { Metada<PERSON> } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";
import { ArrowLeft, ExternalLink } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { Separator } from "@chromify/ui/components/separator";
import { getThemeById } from "@/services/themes";

export const metadata: Metadata = {
  title: "Theme Details - Admin Dashboard",
  description: "View detailed information about a specific theme.",
};

export default async function ThemeDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const theme = await getThemeById(params.id);

  if (!theme) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/themes">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">{theme.name}</h1>
        </div>
        {theme.is_public && theme.shared_id && (
          <Button variant="outline" asChild>
            <a
              href={`${process.env.NEXT_PUBLIC_WEB_URL}/theme/shared/${theme.shared_id}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View Public Page
            </a>
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Theme Information</CardTitle>
            <CardDescription>Basic information about this theme</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">ID</dt>
                <dd className="text-sm">{theme.id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Format
                </dt>
                <dd className="text-sm">{theme.format}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Created
                </dt>
                <dd className="text-sm">
                  {new Date(theme.created_at).toLocaleString()}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Status
                </dt>
                <dd className="text-sm">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      theme.is_public
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {theme.is_public ? "Public" : "Private"}
                  </span>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Favorite
                </dt>
                <dd className="text-sm">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      theme.is_favorite
                        ? "bg-amber-100 text-amber-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {theme.is_favorite ? "Yes" : "No"}
                  </span>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Has Dark Mode
                </dt>
                <dd className="text-sm">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      theme.dark_colors
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {theme.dark_colors ? "Yes" : "No"}
                  </span>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>Information about the theme creator</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  User ID
                </dt>
                <dd className="text-sm">{theme.user_id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Name
                </dt>
                <dd className="text-sm">
                  {theme.user_profiles?.name || "Unknown"}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Subscription
                </dt>
                <dd className="text-sm">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      theme.user_profiles?.subscription_tier === "pro"
                        ? "bg-purple-100 text-purple-800"
                        : theme.user_profiles?.subscription_tier === "enterprise"
                        ? "bg-indigo-100 text-indigo-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {theme.user_profiles?.subscription_tier || "Free"}
                  </span>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Theme Request</CardTitle>
            <CardDescription>Original request information</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  Request ID
                </dt>
                <dd className="text-sm">{theme.request_id}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  User Prompt
                </dt>
                <dd className="text-sm whitespace-pre-wrap rounded-md bg-muted p-4">
                  {theme.theme_requests?.user_prompt || "No prompt available"}
                </dd>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Model
                  </dt>
                  <dd className="text-sm">
                    {theme.theme_requests?.model || "Unknown"}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Status
                  </dt>
                  <dd className="text-sm">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        theme.theme_requests?.status === "completed"
                          ? "bg-green-100 text-green-800"
                          : theme.theme_requests?.status === "failed"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {theme.theme_requests?.status || "Unknown"}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    Border Radius
                  </dt>
                  <dd className="text-sm">
                    {theme.theme_requests?.border_radius || "Default"}
                  </dd>
                </div>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
