import Link from "next/link";

import { But<PERSON> } from "@chromify/ui/components/button";

export default function ThemeNotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
      <h1 className="text-4xl font-bold mb-4">Theme Not Found</h1>
      <p className="text-muted-foreground mb-8">
        The theme you are looking for does not exist or has been deleted.
      </p>
      <Button asChild>
        <Link href="/dashboard/themes">Return to Themes</Link>
      </Button>
    </div>
  );
}
