"use client";

import { useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Check, X } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import { Separator } from "@chromify/ui/components/separator";

export function ThemeFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get current filter values
  const format = searchParams.get("format");
  const status = searchParams.get("status");

  // Create a new URLSearchParams instance for manipulation
  const createQueryString = useCallback(
    (name: string, value: string | null) => {
      const params = new URLSearchParams(searchParams.toString());

      if (value === null) {
        params.delete(name);
      } else {
        params.set(name, value);
      }

      return params.toString();
    },
    [searchParams]
  );

  // Handle format filter
  const handleFormatFilter = (value: string) => {
    const newValue = format === value ? null : value;
    router.push(`${pathname}?${createQueryString("format", newValue)}`);
  };

  // Handle status filter
  const handleStatusFilter = (value: string) => {
    const newValue = status === value ? null : value;
    router.push(`${pathname}?${createQueryString("status", newValue)}`);
  };

  // Clear all filters
  const clearFilters = () => {
    router.push(pathname);
  };

  const hasFilters = format || status;

  return (
    <div className="flex flex-wrap items-center gap-2">
      <div className="flex flex-wrap items-center gap-2">
        <div className="font-medium text-sm">Format:</div>
        <Button
          variant={format === "hsl" ? "default" : "outline"}
          size="sm"
          onClick={() => handleFormatFilter("hsl")}
          className="h-8"
        >
          {format === "hsl" && <Check className="mr-1 h-4 w-4" />}
          HSL
        </Button>
        <Button
          variant={format === "oklch" ? "default" : "outline"}
          size="sm"
          onClick={() => handleFormatFilter("oklch")}
          className="h-8"
        >
          {format === "oklch" && <Check className="mr-1 h-4 w-4" />}
          OKLCH
        </Button>
      </div>

      <Separator orientation="vertical" className="h-8 mx-2" />

      <div className="flex flex-wrap items-center gap-2">
        <div className="font-medium text-sm">Status:</div>
        <Button
          variant={status === "public" ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusFilter("public")}
          className="h-8"
        >
          {status === "public" && <Check className="mr-1 h-4 w-4" />}
          Public
        </Button>
        <Button
          variant={status === "private" ? "default" : "outline"}
          size="sm"
          onClick={() => handleStatusFilter("private")}
          className="h-8"
        >
          {status === "private" && <Check className="mr-1 h-4 w-4" />}
          Private
        </Button>
      </div>

      {hasFilters && (
        <>
          <Separator orientation="vertical" className="h-8 mx-2" />
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8"
          >
            <X className="mr-1 h-4 w-4" />
            Clear Filters
          </Button>
        </>
      )}

      {hasFilters && (
        <div className="ml-2 flex items-center">
          <Badge variant="outline" className="rounded-sm px-1 font-normal">
            {format && status ? "2 filters" : "1 filter"} applied
          </Badge>
        </div>
      )}
    </div>
  );
}
