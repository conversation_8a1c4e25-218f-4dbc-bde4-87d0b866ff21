"use client";

import { useMemo } from "react";
import Link from "next/link";
import { ColumnDef } from "@tanstack/react-table";
import { ExternalLink, Eye } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import { Theme } from "@/services/themes";

interface ThemesDataTableProps {
  data: Theme[];
  totalCount: number;
}

export function ThemesDataTable({ data }: ThemesDataTableProps) {
  const columns = useMemo<ColumnDef<Theme>[]>(
    () => [
      {
        accessorKey: "name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Name" />
        ),
        cell: ({ row }) => (
          <div className="font-medium">{row.getValue("name")}</div>
        ),
        enableSorting: true,
        enableHiding: false,
      },
      {
        accessorKey: "user_profiles.name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="User" />
        ),
        cell: ({ row }) => {
          const theme = row.original;
          return (
            <div className="flex items-center">
              <span>{theme.user_profiles?.name || "Unknown User"}</span>
              {theme.user_profiles?.subscription_tier && (
                <Badge className="ml-2 bg-primary/10 text-primary">
                  {theme.user_profiles.subscription_tier}
                </Badge>
              )}
            </div>
          );
        },
        enableSorting: true,
      },
      {
        id: "format",
        accessorKey: "format",
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title="Format"
            showSorting={false}
          />
        ),
        cell: ({ row }) => <div>{row.getValue("format")}</div>,
        enableSorting: false,
        filterFn: (row, id, value: string[]) => {
          return value.includes(row.getValue(id));
        },
      },
      {
        accessorKey: "created_at",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Created" />
        ),
        cell: ({ row }) => (
          <div>{new Date(row.getValue("created_at")).toLocaleDateString()}</div>
        ),
        enableSorting: true,
      },
      {
        id: "is_public",
        accessorKey: "is_public",
        header: ({ column }) => (
          <DataTableColumnHeader
            column={column}
            title="Status"
            showSorting={false}
          />
        ),
        cell: ({ row }) => {
          const isPublic = row.getValue("is_public");
          return (
            <Badge
              className={
                isPublic
                  ? "bg-green-100 text-green-800"
                  : "bg-yellow-100 text-yellow-800"
              }
            >
              {isPublic ? "Public" : "Private"}
            </Badge>
          );
        },
        enableSorting: false,
        filterFn: (row, id, value: string[]) => {
          const cellValue = String(row.getValue(id));
          return value.includes(cellValue);
        },
      },
      {
        id: "actions",
        enableHiding: false,
        enableSorting: false,
        header: () => <div className="text-right">Actions</div>,
        cell: ({ row }) => {
          const theme = row.original;
          return (
            <div className="flex justify-end gap-2">
              <Button variant="outline" size="icon" asChild className="h-8 w-8">
                <Link href={`/dashboard/themes/${theme.id}`}>
                  <Eye className="h-4 w-4" />
                  <span className="sr-only">View</span>
                </Link>
              </Button>
              {theme.is_public && theme.shared_id && (
                <Button
                  variant="outline"
                  size="icon"
                  asChild
                  className="h-8 w-8"
                >
                  <a
                    href={`${process.env.NEXT_PUBLIC_WEB_URL}/theme/shared/${theme.shared_id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span className="sr-only">Open</span>
                  </a>
                </Button>
              )}
            </div>
          );
        },
      },
    ],
    []
  );

  return (
    <DataTable
      columns={columns}
      data={data}
      searchColumn="name"
      searchPlaceholder="Search themes..."
    />
  );
}
