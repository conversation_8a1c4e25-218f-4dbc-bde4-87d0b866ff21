import { getThemesWithFilters, ThemeFilterOptions } from "@/services/themes";
import { ThemesDataTable } from "./themes-data-table";

interface ThemesTableProps {
  format?: string;
  status?: string;
}

export default async function ThemesTable({
  format,
  status,
}: ThemesTableProps = {}) {
  // Build filter options from URL params
  const filterOptions: ThemeFilterOptions = {};

  if (format) {
    filterOptions.format = format as "hsl" | "oklch";
  }

  if (status === "public") {
    filterOptions.status = "public";
  } else if (status === "private") {
    filterOptions.status = "private";
  }

  // Fetch themes with pagination, sorting, and filtering
  const { themes, totalThemes } = await getThemesWithFilters(
    1, // page
    50, // pageSize - fetch more for client-side pagination
    "newest", // sortOption
    filterOptions
  );

  return <ThemesDataTable data={themes} totalCount={totalThemes} />;
}
