import { Metadata } from "next";

import { getThemes } from "@/services/themes";
import { getTotoalUsers } from "@/services/users";

export const metadata: Metadata = {
  title: "Admin Dashboard",
  description: "Overview of Chromify system statistics and metrics.",
};

export default async function DashboardPage() {
  const totalUsers = await getTotoalUsers();
  const totalThemes = await getThemes();

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of Chromify system statistics and metrics.
        </p>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Dashboard cards will go here */}
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">
            Total Users
          </h3>
          <div className="mt-2 text-2xl font-bold">{totalUsers.length}</div>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">
            Total Themes
          </h3>
          <div className="mt-2 text-2xl font-bold">{totalThemes.length}</div>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">
            Active Users (30d)
          </h3>
          <div className="mt-2 text-2xl font-bold">0</div>
        </div>
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">
            Themes Created (30d)
          </h3>
          <div className="mt-2 text-2xl font-bold">0</div>
        </div>
      </div>
    </div>
  );
}
