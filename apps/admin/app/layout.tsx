import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";

import "@chromify/ui/globals.css";

import { Toaster } from "@chromify/ui/components/sonner";
import { ThemeProvider } from "@/components/theme-provider";

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

export const metadata: Metadata = {
  title: {
    template: "%s | Chromify Admin",
    default: "Chromify Admin - Dashboard for Chromify Management",
  },
  description:
    "Admin dashboard for managing Chromify users, themes, and system settings.",
  keywords: ["admin", "dashboard", "chromify", "management", "themes", "users"],
  authors: [{ name: "<PERSON> Xin" }],
  creator: "Patrick Xin",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased`}
      >
        <ThemeProvider>{children}</ThemeProvider>
        <Toaster richColors />
      </body>
    </html>
  );
}
