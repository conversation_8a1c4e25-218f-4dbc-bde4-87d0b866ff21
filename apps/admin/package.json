{"name": "admin", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@chromify/ui": "workspace:*", "@clerk/nextjs": "^6.13.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.3", "lucide-react": "^0.475.0", "next": "^15.3.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@chromify/eslint-config": "workspace:*", "@chromify/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.24.0", "supabase": "^2.20.12", "typescript": "^5.7.3"}}