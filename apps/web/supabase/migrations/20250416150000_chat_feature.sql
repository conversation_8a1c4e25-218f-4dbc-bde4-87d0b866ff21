-- Migration file: 20250416150000_chat_feature.sql
-- Description: Adds database schema for the chat feature
-- This migration creates:
-- 1. Tables for storing chat sessions and messages
-- 2. Functions for managing chat data
-- 3. RLS policies for secure access to chat data
-- 4. Optional table for linking chats to themes

-- ------------------------------------------------------
-- Custom Enum Types
-- ------------------------------------------------------
-- Define the roles for chat messages
create type message_role as enum ('user', 'assistant', 'system', 'data');

comment on type message_role is 'Roles for chat messages (user, assistant, system, data)';

-- ------------------------------------------------------
-- Chat Sessions Table
-- ------------------------------------------------------
-- Table to store chat conversations
create table chat_sessions (
  id uuid primary key default gen_random_uuid(),
  user_id text not null references user_profiles(id) on delete cascade,
  title text not null default 'New Chat',
  is_pinned boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  last_message_at timestamptz not null default now()
);

comment on table chat_sessions is 'Chat conversations between users and the AI assistant';
comment on column chat_sessions.id is 'Unique identifier for the chat session';
comment on column chat_sessions.user_id is 'Clerk user ID who owns the chat session';
comment on column chat_sessions.title is 'Display title for the chat session';
comment on column chat_sessions.is_pinned is 'Whether the user has pinned this chat session';
comment on column chat_sessions.last_message_at is 'Timestamp of the last message in the session';

-- Create indexes for frequently queried columns
create index idx_chat_sessions_user_id on chat_sessions(user_id);
create index idx_chat_sessions_user_pinned on chat_sessions(user_id, is_pinned) where is_pinned = true;
create index idx_chat_sessions_last_activity on chat_sessions(user_id, last_message_at desc);

-- ------------------------------------------------------
-- Chat Messages Table
-- ------------------------------------------------------
-- Table to store individual messages in chat sessions
create table chat_messages (
  id text primary key, -- Using the message ID generated by the AI SDK
  session_id uuid not null references chat_sessions(id) on delete cascade,
  role message_role not null,
  content text,
  parts jsonb, -- Store the structured message parts
  created_at timestamptz not null default now(), -- Server-side timestamp
  created_at_client timestamptz, -- Client-side timestamp from AI SDK
  sequence_number integer not null -- For maintaining message order
);

comment on table chat_messages is 'Individual messages in chat conversations';
comment on column chat_messages.id is 'Unique identifier for the message (from AI SDK)';
comment on column chat_messages.session_id is 'Reference to the chat session this message belongs to';
comment on column chat_messages.role is 'Role of the message sender (user, assistant, system, data)';
comment on column chat_messages.content is 'Text content of the message';
comment on column chat_messages.parts is 'Structured parts of the message (text, tool calls, etc.)';
comment on column chat_messages.sequence_number is 'Order of the message in the conversation';
comment on column chat_messages.created_at is 'Server-side timestamp when the message was saved';
comment on column chat_messages.created_at_client is 'Client-side timestamp from the AI SDK message';

-- Create indexes for frequently queried columns
create index idx_chat_messages_session on chat_messages(session_id, sequence_number);

-- ------------------------------------------------------
-- Chat Streams Table
-- ------------------------------------------------------
-- Table to track stream IDs for resumable streams
create table chat_streams (
  id uuid primary key default gen_random_uuid(),
  session_id uuid not null references chat_sessions(id) on delete cascade,
  stream_id text not null,
  is_active boolean not null default true,
  created_at timestamptz not null default now(),
  completed_at timestamptz
);

comment on table chat_streams is 'Tracks stream IDs for resumable chat streams';
comment on column chat_streams.id is 'Unique identifier for the stream record';
comment on column chat_streams.session_id is 'Reference to the chat session this stream belongs to';
comment on column chat_streams.stream_id is 'Stream ID generated by the AI SDK';
comment on column chat_streams.is_active is 'Whether the stream is still active';
comment on column chat_streams.completed_at is 'Timestamp when the stream was completed';

-- Create indexes for frequently queried columns
create index idx_chat_streams_session on chat_streams(session_id);
create index idx_chat_streams_active on chat_streams(session_id, is_active) where is_active = true;

-- ------------------------------------------------------
-- Chat Themes Table
-- ------------------------------------------------------
-- Table to link chat sessions to themes created during the conversation
create table chat_themes (
  id uuid primary key default gen_random_uuid(),
  session_id uuid not null references chat_sessions(id) on delete cascade,
  theme_id uuid not null references themes(id) on delete cascade,
  created_at timestamptz not null default now(),
  unique(session_id, theme_id)
);

comment on table chat_themes is 'Links between chat sessions and themes created during conversations';
comment on column chat_themes.id is 'Unique identifier for the chat-theme link';
comment on column chat_themes.session_id is 'Reference to the chat session';
comment on column chat_themes.theme_id is 'Reference to the theme created during the chat';

-- Create indexes for frequently queried columns
create index idx_chat_themes_session on chat_themes(session_id);
create index idx_chat_themes_theme on chat_themes(theme_id);

-- ------------------------------------------------------
-- Chat Session Functions
-- ------------------------------------------------------
-- Function to create a new chat session
create or replace function create_chat_session(
  p_title text default 'New Chat'
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_session_id uuid;
begin
  -- Create new chat session
  insert into chat_sessions (
    user_id,
    title
  ) values (
    v_user_id,
    p_title
  ) returning id into v_session_id;

  return v_session_id;
end;
$$;

comment on function create_chat_session(text) is
  'Creates a new chat session for the current user and returns the session ID';

-- Function to save a chat message
create or replace function save_chat_message(
  p_session_id uuid,
  p_message_id text,
  p_role message_role,
  p_content text,
  p_parts jsonb,
  p_created_at_client timestamptz default null
)
returns text
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_sequence_number integer;
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Get next sequence number
  select coalesce(max(sequence_number), 0) + 1 into v_sequence_number
  from chat_messages
  where session_id = p_session_id;

  -- Insert message
  insert into chat_messages (
    id,
    session_id,
    role,
    content,
    parts,
    created_at_client,
    sequence_number
  ) values (
    p_message_id,
    p_session_id,
    p_role,
    p_content,
    p_parts,
    p_created_at_client,
    v_sequence_number
  );

  -- Update last activity timestamp
  update chat_sessions
  set
    last_message_at = now(),
    updated_at = now()
  where id = p_session_id;

  return p_message_id;
end;
$$;

comment on function save_chat_message(uuid, text, message_role, text, jsonb, timestamptz) is
  'Saves a chat message to the database and updates the session last activity timestamp';

-- Function to load chat messages with pagination
create or replace function load_chat_messages(
  p_session_id uuid,
  p_limit integer default 50,
  p_offset integer default 0
)
returns setof chat_messages
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Return messages with pagination
  return query
  select *
  from chat_messages
  where session_id = p_session_id
  order by sequence_number
  limit p_limit
  offset p_offset;
end;
$$;

comment on function load_chat_messages(uuid, integer, integer) is
  'Loads chat messages for a session with pagination';

-- Function to update a chat session title
create or replace function update_chat_title(
  p_session_id uuid,
  p_title text
)
returns void
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
begin
  -- Check ownership
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to modify this chat session';
  end if;

  -- Update title
  update chat_sessions
  set
    title = p_title,
    updated_at = now()
  where id = p_session_id;
end;
$$;

comment on function update_chat_title(uuid, text) is
  'Updates the title of a chat session';

-- Function to toggle the pinned status of a chat session
create or replace function toggle_chat_pinned(
  p_session_id uuid
)
returns boolean
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_is_pinned boolean;
begin
  -- Check ownership
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to modify this chat session';
  end if;

  -- Toggle pinned status
  update chat_sessions
  set
    is_pinned = not is_pinned,
    updated_at = now()
  where id = p_session_id
  returning is_pinned into v_is_pinned;

  return v_is_pinned;
end;
$$;

comment on function toggle_chat_pinned(uuid) is
  'Toggles the pinned status of a chat session and returns the new status';

-- Function to delete a chat session
create or replace function delete_chat_session(
  p_session_id uuid
)
returns void
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
begin
  -- Check ownership
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to delete this chat session';
  end if;

  -- Delete the chat session (will cascade to messages and theme links)
  delete from chat_sessions where id = p_session_id;
end;
$$;

comment on function delete_chat_session(uuid) is
  'Deletes a chat session and all its messages';

-- Function to link a chat session to a theme
create or replace function link_chat_to_theme(
  p_session_id uuid,
  p_theme_id uuid
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_link_id uuid;
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Check ownership of theme
  if not exists (select 1 from themes where id = p_theme_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this theme';
  end if;

  -- Check if link already exists
  if exists (select 1 from chat_themes where session_id = p_session_id and theme_id = p_theme_id) then
    -- Return existing link ID
    select id into v_link_id from chat_themes where session_id = p_session_id and theme_id = p_theme_id;
    return v_link_id;
  end if;

  -- Create link
  insert into chat_themes (
    session_id,
    theme_id
  ) values (
    p_session_id,
    p_theme_id
  ) returning id into v_link_id;

  return v_link_id;
end;
$$;

comment on function link_chat_to_theme(uuid, uuid) is
  'Links a chat session to a theme and returns the link ID';

-- Function to create a new stream for a chat session
create or replace function create_chat_stream(
  p_session_id uuid,
  p_stream_id text
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_stream_record_id uuid;
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Mark any existing active streams as inactive
  update chat_streams
  set
    is_active = false,
    completed_at = now()
  where session_id = p_session_id and is_active = true;

  -- Create new stream record
  insert into chat_streams (
    session_id,
    stream_id
  ) values (
    p_session_id,
    p_stream_id
  ) returning id into v_stream_record_id;

  return v_stream_record_id;
end;
$$;

comment on function create_chat_stream(uuid, text) is
  'Creates a new stream record for a chat session and marks any existing active streams as inactive';

-- Function to mark a stream as completed
create or replace function complete_chat_stream(
  p_session_id uuid,
  p_stream_id text
)
returns void
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Mark stream as completed
  update chat_streams
  set
    is_active = false,
    completed_at = now()
  where session_id = p_session_id and stream_id = p_stream_id;
end;
$$;

comment on function complete_chat_stream(uuid, text) is
  'Marks a stream as completed';

-- Function to get the latest active stream for a chat session
create or replace function get_latest_chat_stream(
  p_session_id uuid
)
returns text
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_stream_id text;
begin
  -- Check ownership of chat session
  if not exists (select 1 from chat_sessions where id = p_session_id and user_id = v_user_id) then
    raise exception 'Not authorized to access this chat session';
  end if;

  -- Get latest active stream
  select stream_id into v_stream_id
  from chat_streams
  where session_id = p_session_id and is_active = true
  order by created_at desc
  limit 1;

  return v_stream_id;
end;
$$;

comment on function get_latest_chat_stream(uuid) is
  'Gets the latest active stream ID for a chat session';

-- ------------------------------------------------------
-- Row Level Security Policies
-- ------------------------------------------------------
-- Enable RLS on chat_sessions table
alter table chat_sessions enable row level security;

-- Policy for chat_sessions: users can only access their own sessions
create policy chat_sessions_user_access on chat_sessions
  using (user_id = requesting_user_id());

-- Enable RLS on chat_messages table
alter table chat_messages enable row level security;

-- Policy for chat_messages: users can only access messages in their own sessions
create policy chat_messages_user_access on chat_messages
  using (session_id in (
    select id from chat_sessions where user_id = requesting_user_id()
  ));

-- Enable RLS on chat_themes table
alter table chat_themes enable row level security;

-- Policy for chat_themes: users can only access links for their own sessions
create policy chat_themes_user_access on chat_themes
  using (session_id in (
    select id from chat_sessions where user_id = requesting_user_id()
  ));

-- Enable RLS on chat_streams table
alter table chat_streams enable row level security;

-- Policy for chat_streams: users can only access streams for their own sessions
create policy chat_streams_user_access on chat_streams
  using (session_id in (
    select id from chat_sessions where user_id = requesting_user_id()
  ));
