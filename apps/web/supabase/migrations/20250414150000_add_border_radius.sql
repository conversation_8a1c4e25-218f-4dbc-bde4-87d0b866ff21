-- Migration file: 20250414150000_add_border_radius.sql
-- Description: Adds border_radius column to theme_requests table and updates related functions
-- This migration adds support for customizing border radius in themes

-- ------------------------------------------------------
-- Add border_radius column to theme_requests table
-- ------------------------------------------------------

-- Add the border_radius column with a default value
ALTER TABLE theme_requests
ADD COLUMN border_radius text NOT NULL DEFAULT '0.5';

-- Add comment to explain the purpose of the field
COMMENT ON COLUMN theme_requests.border_radius IS 'Border radius value for UI elements (0, 0.3, 0.5, 0.75, 1)';

-- ------------------------------------------------------
-- Update the create_theme function to include border_radius
-- ------------------------------------------------------

-- Drop the existing function
DROP FUNCTION IF EXISTS create_theme(text, text, theme_format, boolean, boolean, boolean, text, theme_model);

-- Create the updated function with the new parameter
CREATE OR REPLACE FUNCTION create_theme(
  p_user_id text,
  p_user_prompt text,
  p_format theme_format,
  p_include_dark_mode boolean,
  p_include_sidebar boolean,
  p_include_chart boolean,
  p_image_path text,
  p_model theme_model,
  p_border_radius text DEFAULT '0.5'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_request_id uuid;
BEGIN
  -- Verify the user exists in user_profiles
  IF NOT EXISTS(SELECT 1 FROM user_profiles WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User does not exist';
  END IF;

  -- Create a new theme request
  INSERT INTO theme_requests (
    user_id,
    user_prompt,
    format,
    include_dark_mode,
    include_sidebar,
    include_chart,
    image_path,
    model,
    status,
    border_radius
  ) VALUES (
    p_user_id,
    p_user_prompt,
    p_format,
    p_include_dark_mode,
    p_include_sidebar,
    p_include_chart,
    p_image_path,
    p_model,
    'pending',
    p_border_radius
  ) RETURNING id INTO v_request_id;

  RETURN v_request_id;
END;
$$;

COMMENT ON FUNCTION create_theme(text, text, theme_format, boolean, boolean, boolean, text, theme_model, text) IS
  'Creates a new theme request with border radius option and returns the request ID';

-- ------------------------------------------------------
-- Fix the fail_theme function to use the correct column name
-- ------------------------------------------------------

-- Drop the existing function
DROP FUNCTION IF EXISTS fail_theme(uuid, text);

-- Create the updated function with the correct column references
CREATE OR REPLACE FUNCTION fail_theme(
  p_request_id uuid,
  p_error_message text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if request exists
  IF NOT EXISTS(SELECT 1 FROM theme_requests WHERE id = p_request_id) THEN
    RAISE EXCEPTION 'Theme request not found';
  END IF;
  
  -- Check if request is already completed or failed
  IF EXISTS(SELECT 1 FROM theme_requests WHERE id = p_request_id AND status != 'pending') THEN
    RAISE EXCEPTION 'Theme request is not in pending status';
  END IF;

  -- Update request status
  UPDATE theme_requests
  SET
    status = 'failed',
    error_message = p_error_message,
    updated_at = now()
  WHERE id = p_request_id;
END;
$$;

COMMENT ON FUNCTION fail_theme(uuid, text) IS
  'Marks a theme request as failed with an error message';

-- ------------------------------------------------------
-- Update the theme_details view to include border_radius
-- ------------------------------------------------------

-- Drop the existing view
DROP VIEW IF EXISTS theme_details;

-- Create the updated view with the border_radius field
CREATE VIEW theme_details AS
SELECT
  t.id,
  t.request_id,
  t.user_id,
  t.name,
  t.theme_description,
  tr.user_prompt,
  t.format,
  t.colors,
  t.dark_colors,
  t.analysis,
  t.created_at,
  t.updated_at,
  t.is_favorite,
  t.is_public,
  t.shared_id,
  t.version,
  tr.include_dark_mode,
  tr.include_sidebar,
  tr.include_chart,
  tr.image_path,
  tr.model,
  tr.status,
  tr.border_radius
FROM
  themes t
JOIN
  theme_requests tr ON t.request_id = tr.id;

COMMENT ON VIEW theme_details IS 'Combined view of themes with their original request details including border radius';
