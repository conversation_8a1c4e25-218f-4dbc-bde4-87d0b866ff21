-- Fix the complete_theme function to use the correct column name
CREATE OR REPLACE FUNCTION complete_theme(
  p_request_id uuid,
  p_name text,
  p_theme_description text,
  p_colors jsonb,
  p_dark_colors jsonb,
  p_analysis jsonb,
  p_processing_time_ms integer
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_theme_id uuid;
  v_user_id text;
  v_format theme_format;
  v_user_prompt text;  -- Changed variable name to match column name
BEGIN
  -- Get request details
  SELECT user_id, format, user_prompt  -- Changed column name from description to user_prompt
  INTO v_user_id, v_format, v_user_prompt
  FROM theme_requests
  WHERE id = p_request_id;

  -- Check if request exists
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Theme request not found';
  END IF;

  -- Check if request is already completed
  IF EXISTS(SELECT 1 FROM theme_requests WHERE id = p_request_id AND status = 'completed') THEN
    RAISE EXCEPTION 'Theme request is already completed';
  END IF;

  -- Create theme
  INSERT INTO themes (
    request_id,
    user_id,
    name,
    theme_description,
    format,
    colors,
    dark_colors,
    analysis
  ) VALUES (
    p_request_id,
    v_user_id,
    p_name,
    p_theme_description,  -- Using the parameter directly
    v_format,
    p_colors,
    p_dark_colors,
    p_analysis
  ) RETURNING id INTO v_theme_id;

  -- Update request status
  UPDATE theme_requests
  SET
    status = 'completed',
    processing_time_ms = p_processing_time_ms,
    updated_at = now()
  WHERE id = p_request_id;

  RETURN v_theme_id;
END;
$$;

COMMENT ON FUNCTION complete_theme(uuid, text, text, jsonb, jsonb, jsonb, integer) IS
  'Marks a theme request as completed and creates the theme with AI-generated description';
