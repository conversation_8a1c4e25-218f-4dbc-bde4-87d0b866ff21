-- Migration file: timestamp_theme_tables.sql
-- Description: Creates all theme-related tables and their RLS policies
-- This migration establishes the core data model for the Chromify application
-- including theme requests, themes, and versioning

-- ------------------------------------------------------
-- Custom Enum Types
-- ------------------------------------------------------
-- Define the status of a theme request
create type theme_status as enum ('pending', 'completed', 'failed');

-- Define the color format used for themes
create type theme_format as enum ('hsl', 'oklch');

-- Define the AI model used for generation
create type theme_model as enum ('deepseek-chat', 'anthropic', 'google');

comment on type theme_status is 'Status of a theme generation request';
comment on type theme_format is 'Color format used for the theme';
comment on type theme_model is 'AI model used for theme generation';

-- ------------------------------------------------------
-- Theme Requests Table
-- ------------------------------------------------------
-- Table to store theme generation requests
create table theme_requests (
  id uuid primary key default gen_random_uuid(),
  user_id text not null references user_profiles(id) on delete cascade,
  description text not null,
  format theme_format not null default 'hsl',
  include_dark_mode boolean not null default false,
  include_sidebar boolean not null default false,
  include_chart boolean not null default false,
  image_path text,
  model theme_model not null default 'anthropic',
  status theme_status not null default 'pending',
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  processing_time_ms integer,
  error_message text
);

comment on table theme_requests is 'Requests for theme generation';
comment on column theme_requests.id is 'Unique identifier for the theme request';
comment on column theme_requests.user_id is 'Clerk user ID who created the request';
comment on column theme_requests.description is 'User description for theme generation';
comment on column theme_requests.image_path is 'Optional path to an uploaded image for image-based generation';
comment on column theme_requests.status is 'Current status of the request (pending, completed, failed)';
comment on column theme_requests.processing_time_ms is 'Processing time in milliseconds for completed requests';
comment on column theme_requests.error_message is 'Error message if the request failed';

-- Create indexes for frequently queried columns
create index idx_theme_requests_user_status on theme_requests(user_id, status);
create index idx_theme_requests_created_at on theme_requests(created_at desc);

-- ------------------------------------------------------
-- Themes Table
-- ------------------------------------------------------
-- Table to store generated themes
create table themes (
  id uuid primary key default gen_random_uuid(),
  request_id uuid not null references theme_requests(id) on delete cascade,
  user_id text not null references user_profiles(id) on delete cascade,
  name text not null,
  description text,
  format theme_format not null default 'hsl',
  colors jsonb not null,
  dark_colors jsonb,
  analysis jsonb,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  is_favorite boolean not null default false,
  is_public boolean not null default false,
  version integer not null default 1,
  constraint check_colors_format check (jsonb_typeof(colors) = 'object')
);

comment on table themes is 'Generated color themes';
comment on column themes.id is 'Unique identifier for the theme';
comment on column themes.request_id is 'Reference to the request that generated this theme';
comment on column themes.user_id is 'Clerk user ID who owns the theme';
comment on column themes.name is 'Display name for the theme';
comment on column themes.colors is 'JSON object containing the theme colors';
comment on column themes.dark_colors is 'JSON object containing dark mode colors (if applicable)';
comment on column themes.analysis is 'AI-generated analysis of the theme';
comment on column themes.is_favorite is 'Whether the user has marked this theme as a favorite';
comment on column themes.is_public is 'Whether this theme is publicly visible';
comment on column themes.version is 'Current version number of the theme';

-- Create indexes for frequently queried columns
create index idx_themes_user_id on themes(user_id);
create index idx_themes_user_favorite on themes(user_id, is_favorite) where is_favorite = true;
create index idx_themes_public on themes(is_public, created_at desc) where is_public = true;
create index idx_themes_format on themes(format);
create index idx_themes_colors on themes using gin (colors);
create index idx_themes_dark_colors on themes using gin (dark_colors);

-- ------------------------------------------------------
-- Theme Versions Table
-- ------------------------------------------------------
-- Table to store version history for themes
create table theme_versions (
  id uuid primary key default gen_random_uuid(),
  theme_id uuid not null references themes(id) on delete cascade,
  version_number integer not null,
  colors jsonb not null,
  dark_colors jsonb,
  change_description text,
  created_at timestamptz not null default now(),
  created_by text not null references user_profiles(id) on delete cascade,
  unique(theme_id, version_number)
);

comment on table theme_versions is 'Version history for themes';
comment on column theme_versions.id is 'Unique identifier for the theme version';
comment on column theme_versions.theme_id is 'Reference to the theme this version belongs to';
comment on column theme_versions.version_number is 'Sequential version number';
comment on column theme_versions.colors is 'JSON object containing the theme colors for this version';
comment on column theme_versions.dark_colors is 'JSON object containing dark mode colors for this version';
comment on column theme_versions.change_description is 'Description of changes made in this version';
comment on column theme_versions.created_by is 'Clerk user ID who created this version';

-- Create index for theme_id to improve lookup performance
create index idx_theme_versions_theme_id on theme_versions(theme_id);

-- ------------------------------------------------------
-- RLS Policies for Theme Requests
-- ------------------------------------------------------
-- Enable Row Level Security for theme_requests
alter table theme_requests enable row level security;

-- Policy allowing users to view their own requests
create policy "Users can view their own requests"
  on theme_requests 
  for select
  using (requesting_user_id() = user_id);

-- Policy allowing users to create their own theme requests
create policy "Users can create theme requests"
  on theme_requests 
  for insert
  with check (requesting_user_id() = user_id);

-- Policy allowing users to update their own theme requests
-- Limited to updating specific fields, not changing ownership
create policy "Users can update their own requests"
  on theme_requests 
  for update
  using (requesting_user_id() = user_id);

-- ------------------------------------------------------
-- RLS Policies for Themes
-- ------------------------------------------------------
-- Enable Row Level Security for themes
alter table themes enable row level security;

-- Policy allowing users to see their own themes or public themes
create policy "Users can see their own themes or public themes"
  on themes 
  for select
  using (requesting_user_id() = user_id or is_public = true);

-- Policy allowing users to update their own themes
create policy "Users can update their own themes"
  on themes 
  for update
  using (requesting_user_id() = user_id);

-- Policy allowing users to delete their own themes
create policy "Users can delete their own themes"
  on themes 
  for delete
  using (requesting_user_id() = user_id);

-- Policy allowing users to insert themes (with ownership check)
create policy "Users can insert their own themes"
  on themes 
  for insert
  with check (requesting_user_id() = user_id);

-- ------------------------------------------------------
-- RLS Policies for Theme Versions
-- ------------------------------------------------------
-- Enable Row Level Security for theme_versions
alter table theme_versions enable row level security;

-- Policy allowing users to see versions of visible themes
-- Uses a subquery to check if user can access the theme
create policy "Users can see versions of visible themes"
  on theme_versions 
  for select
  using (
    exists (
      select 1 from themes
      where themes.id = theme_versions.theme_id
      and (themes.user_id = requesting_user_id() or themes.is_public = true)
    )
  );

-- Policy allowing users to create versions of their own themes
create policy "Users can create versions of their own themes"
  on theme_versions 
  for insert
  with check (
    created_by = requesting_user_id() and
    exists (
      select 1 from themes
      where themes.id = theme_versions.theme_id
      and themes.user_id = requesting_user_id()
    )
  );

-- ------------------------------------------------------
-- Trigger to update the 'updated_at' columns
-- ------------------------------------------------------
-- For theme_requests table
create trigger update_theme_requests_timestamp
before update on theme_requests
for each row
execute function update_timestamp();

-- For themes table
create trigger update_themes_timestamp
before update on themes
for each row
execute function update_timestamp();

-- ------------------------------------------------------
-- Validation trigger for themes
-- ------------------------------------------------------
-- Ensures user_id is consistent with the related request's user_id
create or replace function validate_theme_insertion()
returns trigger as $$
begin
  -- Check that the user_id matches the user_id of the referenced request
  if new.user_id != (select user_id from theme_requests where id = new.request_id) then
    raise exception 'Theme user_id must match the user_id of the referenced request';
  end if;
  return new;
end;
$$ language plpgsql security definer;

create trigger enforce_theme_user_match
before insert on themes
for each row
execute function validate_theme_insertion();