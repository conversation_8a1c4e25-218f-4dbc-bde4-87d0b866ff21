-- Migration file: 20250415150000_add_primary_hue.sql
-- Description: Adds primary_hue column to themes table and updates related functions and views
-- This migration adds support for categorizing themes by their primary hue

-- ------------------------------------------------------
-- Add primary_hue column to themes table
-- ------------------------------------------------------
ALTER TABLE themes
ADD COLUMN primary_hue text;

COMMENT ON COLUMN themes.primary_hue IS 'The primary hue category that best represents this theme';

-- ------------------------------------------------------
-- Update the complete_theme function to include primary_hue
-- ------------------------------------------------------
DROP FUNCTION IF EXISTS complete_theme(uuid, text, text, jsonb, jsonb, jsonb, integer);

CREATE OR REPLACE FUNCTION complete_theme(
  p_request_id uuid,
  p_name text,
  p_theme_description text,
  p_colors jsonb,
  p_dark_colors jsonb,
  p_analysis jsonb,
  p_primary_hue text,
  p_processing_time_ms integer
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_theme_id uuid;
  v_user_id text;
  v_format theme_format;
  v_user_prompt text;
BEGIN
  -- Get request details
  SELECT user_id, format, user_prompt
  INTO v_user_id, v_format, v_user_prompt
  FROM theme_requests
  WHERE id = p_request_id;

  -- Check if request exists
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Theme request not found';
  END IF;

  -- Check if request is already completed
  IF EXISTS(SELECT 1 FROM theme_requests WHERE id = p_request_id AND status = 'completed') THEN
    RAISE EXCEPTION 'Theme request is already completed';
  END IF;

  -- Create theme
  INSERT INTO themes (
    request_id,
    user_id,
    name,
    theme_description,
    format,
    colors,
    dark_colors,
    analysis,
    primary_hue
  ) VALUES (
    p_request_id,
    v_user_id,
    p_name,
    p_theme_description,
    v_format,
    p_colors,
    p_dark_colors,
    p_analysis,
    p_primary_hue
  ) RETURNING id INTO v_theme_id;

  -- Update request status
  UPDATE theme_requests
  SET
    status = 'completed',
    processing_time_ms = p_processing_time_ms,
    updated_at = now()
  WHERE id = p_request_id;

  RETURN v_theme_id;
END;
$$;

COMMENT ON FUNCTION complete_theme(uuid, text, text, jsonb, jsonb, jsonb, text, integer) IS
  'Marks a theme request as completed and creates the theme with AI-generated description and primary hue';

-- ------------------------------------------------------
-- Update the theme_details view to include primary_hue
-- ------------------------------------------------------
DROP VIEW IF EXISTS theme_details;

CREATE VIEW theme_details AS
SELECT
  t.id,
  t.request_id,
  t.user_id,
  t.name,
  t.theme_description,
  tr.user_prompt,
  t.format,
  t.colors,
  t.dark_colors,
  t.analysis,
  t.created_at,
  t.updated_at,
  t.is_favorite,
  t.is_public,
  t.shared_id,
  t.primary_hue,
  t.version,
  tr.include_dark_mode,
  tr.include_sidebar,
  tr.include_chart,
  tr.image_path,
  tr.model,
  tr.status,
  tr.border_radius
FROM
  themes t
JOIN
  theme_requests tr ON t.request_id = tr.id;

COMMENT ON VIEW theme_details IS 'Combined view of themes with their original request details';

-- ------------------------------------------------------
-- Create an index for primary_hue to improve query performance
-- ------------------------------------------------------
CREATE INDEX idx_themes_primary_hue ON themes(primary_hue);
