-- Migration file: timestamp_clerk_user_integration.sql
-- Description: Sets up the Clerk user integration with Supabase
-- This migration creates:
-- 1. A function to extract Clerk user IDs from JWT tokens
-- 2. A user_profiles table linked to Clerk user IDs with RLS policies

-- ------------------------------------------------------
-- Function to extract Clerk user ID from JWT token
-- ------------------------------------------------------
-- This replaces the standard auth.uid() function when using Clerk
-- It extracts the 'sub' claim from the JWT which contains the Clerk user ID
create or replace function requesting_user_id()
returns text
language sql stable as $$
  select nullif(current_setting('request.jwt.claims', true)::json->>'sub', '')::text;
$$;

comment on function requesting_user_id() is 'Extracts the Clerk user ID (sub claim) from the current JWT token';

-- ------------------------------------------------------
-- User profiles table linked to Clerk user IDs
-- ------------------------------------------------------
-- This table stores additional user information beyond what Clerk manages
create table if not exists user_profiles (
  -- Using Clerk's string-based user IDs as the primary key
  id text primary key,
  name text,
  -- Subscription tier with validation using a check constraint
  subscription_tier text check (subscription_tier in ('free', 'pro', 'enterprise')),
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

comment on table user_profiles is 'Extended user profile information linked to Clerk user IDs';
comment on column user_profiles.id is 'The Clerk user ID (string format)';
comment on column user_profiles.subscription_tier is 'User subscription level (free, pro, or enterprise)';

-- ------------------------------------------------------
-- RLS Policies for user_profiles table
-- ------------------------------------------------------
-- Enable Row Level Security on the table
alter table user_profiles enable row level security;

-- Policy allowing users to view their own profile
create policy "Users can view their own profile"
  on user_profiles 
  for select
  using (requesting_user_id() = id);

-- Policy allowing users to update their own profile
create policy "Users can update their own profile"
  on user_profiles 
  for update
  using (requesting_user_id() = id);

-- Policy allowing users to insert their own profile
-- Only needed if users create their own profiles rather than being created by a webhook
create policy "Users can insert their own profile"
  on user_profiles 
  for insert
  with check (requesting_user_id() = id);

-- ------------------------------------------------------
-- Trigger to update the updated_at timestamp
-- ------------------------------------------------------
create or replace function update_timestamp()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql security definer;

create trigger update_user_profiles_timestamp
before update on user_profiles
for each row
execute function update_timestamp();

-- ------------------------------------------------------
-- Function to create a new user profile from Clerk webhook
-- ------------------------------------------------------
create or replace function create_user_profile(
  p_user_id text,
  p_name text,
  p_subscription_tier text default 'free'
)
returns void
language plpgsql security definer as $$
begin
  insert into user_profiles (id, name, subscription_tier)
  values (p_user_id, p_name, p_subscription_tier)
  on conflict (id) do update
  set 
    name = p_name,
    subscription_tier = coalesce(p_subscription_tier, user_profiles.subscription_tier),
    updated_at = now();
end;
$$;

comment on function create_user_profile(text, text, text) is 'Creates or updates a user profile, typically called from a Clerk webhook';