-- Migration file: 20250417150000_add_tool_invocations.sql
-- Description: Adds tool_invocations column to chat_messages table and updates related functions
-- This migration fixes an issue where tool invocations aren't properly saved to the database
-- and don't render when users revisit the page

-- ------------------------------------------------------
-- Add tool_invocations column to chat_messages table
-- ------------------------------------------------------
ALTER TABLE chat_messages
ADD COLUMN tool_invocations jsonb;

COMMENT ON COLUMN chat_messages.tool_invocations IS 'Tool invocations from the AI SDK for proper rendering of interactive elements';

-- ------------------------------------------------------
-- Update the save_chat_message function to include tool_invocations
-- ------------------------------------------------------
DROP FUNCTION IF EXISTS save_chat_message(uuid, text, message_role, text, jsonb, timestamptz);

CREATE OR REPLACE FUNCTION save_chat_message(
  p_session_id uuid,
  p_message_id text,
  p_role message_role,
  p_content text,
  p_parts jsonb,
  p_tool_invocations jsonb,
  p_created_at_client timestamptz default null
)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id text := requesting_user_id();
  v_sequence_number integer;
BEGIN
  -- Check ownership of chat session
  IF NOT EXISTS (SELECT 1 FROM chat_sessions WHERE id = p_session_id AND user_id = v_user_id) THEN
    RAISE EXCEPTION 'Not authorized to access this chat session';
  END IF;

  -- Get next sequence number
  SELECT COALESCE(MAX(sequence_number), 0) + 1 INTO v_sequence_number
  FROM chat_messages
  WHERE session_id = p_session_id;

  -- Insert message
  INSERT INTO chat_messages (
    id,
    session_id,
    role,
    content,
    parts,
    tool_invocations,
    created_at_client,
    sequence_number
  ) VALUES (
    p_message_id,
    p_session_id,
    p_role,
    p_content,
    p_parts,
    p_tool_invocations,
    p_created_at_client,
    v_sequence_number
  );

  -- Update last activity timestamp
  UPDATE chat_sessions
  SET
    last_message_at = now(),
    updated_at = now()
  WHERE id = p_session_id;

  RETURN p_message_id;
END;
$$;

COMMENT ON FUNCTION save_chat_message(uuid, text, message_role, text, jsonb, jsonb, timestamptz) IS
  'Saves a chat message to the database with tool invocations and updates the session last activity timestamp';
