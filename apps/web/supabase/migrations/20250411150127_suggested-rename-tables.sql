-- Migration file: 20250411150127_suggested-rename-tables.sql
-- Description: Renames description fields for clarity between user prompts and AI-generated descriptions
-- This migration improves the data model by clearly distinguishing between:
-- 1. User input prompts (in theme_requests)
-- 2. AI-generated theme descriptions (in themes)

-- ------------------------------------------------------
-- Rename columns for clarity
-- ------------------------------------------------------

-- Rename description to user_prompt in theme_requests table
ALTER TABLE theme_requests
RENAME COLUMN description TO user_prompt;

-- Update the comment to reflect the new name
COMMENT ON COLUMN theme_requests.user_prompt IS 'User input prompt for theme generation';

-- Rename description to theme_description in themes table
ALTER TABLE themes
RENAME COLUMN description TO theme_description;

-- Update the comment to reflect the new name
COMMENT ON COLUMN themes.theme_description IS 'AI-generated description of the theme';

-- ------------------------------------------------------
-- Update functions that reference these fields
-- ------------------------------------------------------

-- Drop and recreate the create_theme function
DROP FUNCTION IF EXISTS create_theme(text, text, theme_format, boolean, boolean, boolean, text, theme_model);

CREATE FUNCTION create_theme(
  p_user_id text,
  p_user_prompt text,  -- New parameter name
  p_format theme_format,
  p_include_dark_mode boolean,
  p_include_sidebar boolean,
  p_include_chart boolean,
  p_image_path text,
  p_model theme_model
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_request_id uuid;
BEGIN
  -- Verify the user exists in user_profiles
  IF NOT EXISTS(SELECT 1 FROM user_profiles WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User does not exist';
  END IF;

  -- Create a new theme request
  INSERT INTO theme_requests (
    user_id,
    user_prompt,  -- Renamed from description
    format,
    include_dark_mode,
    include_sidebar,
    include_chart,
    image_path,
    model,
    status
  ) VALUES (
    p_user_id,
    p_user_prompt,
    p_format,
    p_include_dark_mode,
    p_include_sidebar,
    p_include_chart,
    p_image_path,
    p_model,
    'pending'
  ) RETURNING id INTO v_request_id;

  RETURN v_request_id;
END;
$$;

COMMENT ON FUNCTION create_theme(text, text, theme_format, boolean, boolean, boolean, text, theme_model) IS
  'Creates a new theme request and returns the request ID';

-- Drop and recreate the complete_theme function
DROP FUNCTION IF EXISTS complete_theme(uuid, text, jsonb, jsonb, jsonb, integer);

CREATE FUNCTION complete_theme(
  p_request_id uuid,
  p_name text,
  p_theme_description text,  -- New parameter name
  p_colors jsonb,
  p_dark_colors jsonb,
  p_analysis jsonb,
  p_processing_time_ms integer
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_theme_id uuid;
  v_user_id text;
  v_format theme_format;
  v_user_prompt text;  -- Renamed from v_description
BEGIN
  -- Get request details
  SELECT user_id, format, user_prompt  -- Renamed from description
  INTO v_user_id, v_format, v_user_prompt
  FROM theme_requests
  WHERE id = p_request_id;

  -- Check if request exists
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Theme request not found';
  END IF;

  -- Check if request is already completed
  IF EXISTS(SELECT 1 FROM theme_requests WHERE id = p_request_id AND status = 'completed') THEN
    RAISE EXCEPTION 'Theme request is already completed';
  END IF;

  -- Create theme
  INSERT INTO themes (
    request_id,
    user_id,
    name,
    theme_description,  -- Renamed from description
    format,
    colors,
    dark_colors,
    analysis
  ) VALUES (
    p_request_id,
    v_user_id,
    p_name,
    p_theme_description,  -- Now using the parameter instead of v_description
    v_format,
    p_colors,
    p_dark_colors,
    p_analysis
  ) RETURNING id INTO v_theme_id;

  -- Update request status
  UPDATE theme_requests
  SET
    status = 'completed',
    processing_time_ms = p_processing_time_ms,
    updated_at = now()
  WHERE id = p_request_id;

  RETURN v_theme_id;
END;
$$;

COMMENT ON FUNCTION complete_theme(uuid, text, text, jsonb, jsonb, jsonb, integer) IS
  'Marks a theme request as completed and creates the theme with AI-generated description';

-- ------------------------------------------------------
-- Create a view for convenient access to theme data
-- ------------------------------------------------------

CREATE OR REPLACE VIEW theme_details AS
SELECT
  t.id,
  t.request_id,
  t.user_id,
  t.name,
  t.theme_description,
  tr.user_prompt,
  t.format,
  t.colors,
  t.dark_colors,
  t.analysis,
  t.created_at,
  t.updated_at,
  t.is_favorite,
  t.is_public,
  t.version,
  tr.include_dark_mode,
  tr.include_sidebar,
  tr.include_chart,
  tr.image_path,
  tr.model,
  tr.status
FROM
  themes t
JOIN
  theme_requests tr ON t.request_id = tr.id;

COMMENT ON VIEW theme_details IS 'Combined view of themes with their original request details';