-- Migration file: 20250418150000_add_update_chat_message.sql
-- Description: Adds update_chat_message function to support human-in-the-loop interactions
-- This migration adds a function to update existing chat messages, which is necessary
-- for human-in-the-loop flows where messages are updated after user confirmation

-- ------------------------------------------------------
-- Add updated_at column to chat_messages table if it doesn't exist
-- ------------------------------------------------------
ALTER TABLE chat_messages
ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

COMMENT ON COLUMN chat_messages.updated_at IS 'Timestamp when the message was last updated';

-- ------------------------------------------------------
-- Create update_chat_message function
-- ------------------------------------------------------
CREATE OR REPLACE FUNCTION update_chat_message(
  p_session_id uuid,
  p_message_id text,
  p_content text,
  p_parts jsonb,
  p_updated_at timestamptz default now()
)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id text := requesting_user_id();
  v_message_exists boolean;
BEGIN
  -- Check ownership of chat session
  IF NOT EXISTS (SELECT 1 FROM chat_sessions WHERE id = p_session_id AND user_id = v_user_id) THEN
    RAISE EXCEPTION 'Not authorized to access this chat session';
  END IF;

  -- Check if message exists
  SELECT EXISTS(
    SELECT 1 FROM chat_messages 
    WHERE id = p_message_id AND session_id = p_session_id
  ) INTO v_message_exists;

  IF NOT v_message_exists THEN
    RAISE EXCEPTION 'Message not found in this session';
  END IF;

  -- Update the message
  UPDATE chat_messages
  SET 
    content = p_content,
    parts = p_parts,
    updated_at = p_updated_at
  WHERE 
    id = p_message_id AND
    session_id = p_session_id;

  -- Update last activity timestamp on the session
  UPDATE chat_sessions
  SET
    last_message_at = p_updated_at,
    updated_at = p_updated_at
  WHERE id = p_session_id;

  RETURN p_message_id;
END;
$$;

COMMENT ON FUNCTION update_chat_message(uuid, text, text, jsonb, timestamptz) IS
  'Updates an existing chat message, used for human-in-the-loop interactions where messages are updated after user confirmation';
