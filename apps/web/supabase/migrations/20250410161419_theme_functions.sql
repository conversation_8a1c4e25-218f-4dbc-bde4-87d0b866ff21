-- Migration file: timestamp_theme_functions.sql
-- Description: Creates database functions for theme operations
-- This migration adds functions that provide a secure API for common theme operations

-- ------------------------------------------------------
-- Theme creation function
-- ------------------------------------------------------
-- Function to create a new theme request
create or replace function create_theme(
  p_user_id text,
  p_description text,
  p_format theme_format,
  p_include_dark_mode boolean,
  p_include_sidebar boolean,
  p_include_chart boolean,
  p_image_path text,
  p_model theme_model
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_request_id uuid;
begin
  -- Verify the user exists in user_profiles
  if not exists(select 1 from user_profiles where id = p_user_id) then
    raise exception 'User does not exist';
  end if;

  -- Create a new theme request
  insert into theme_requests (
    user_id,
    description,
    format,
    include_dark_mode,
    include_sidebar,
    include_chart,
    image_path,
    model,
    status
  ) values (
    p_user_id,
    p_description,
    p_format,
    p_include_dark_mode,
    p_include_sidebar,
    p_include_chart,
    p_image_path,
    p_model,
    'pending'
  ) returning id into v_request_id;

  return v_request_id;
end;
$$;

comment on function create_theme(text, text, theme_format, boolean, boolean, boolean, text, theme_model) is 
  'Creates a new theme request and returns the request ID';

-- ------------------------------------------------------
-- Theme completion function
-- ------------------------------------------------------
-- Function to mark a theme request as completed and create the theme
create or replace function complete_theme(
  p_request_id uuid,
  p_name text,
  p_colors jsonb,
  p_dark_colors jsonb,
  p_analysis jsonb,
  p_processing_time_ms integer
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_theme_id uuid;
  v_user_id text;
  v_format theme_format;
  v_description text;
begin
  -- Get request details
  select user_id, format, description
  into v_user_id, v_format, v_description
  from theme_requests
  where id = p_request_id;
  
  -- Check if request exists
  if v_user_id is null then
    raise exception 'Theme request not found';
  end if;
  
  -- Check if request is already completed
  if exists(select 1 from theme_requests where id = p_request_id and status = 'completed') then
    raise exception 'Theme request is already completed';
  end if;

  -- Create theme
  insert into themes (
    request_id,
    user_id,
    name,
    description,
    format,
    colors,
    dark_colors,
    analysis
  ) values (
    p_request_id,
    v_user_id,
    p_name,
    v_description,
    v_format,
    p_colors,
    p_dark_colors,
    p_analysis
  ) returning id into v_theme_id;

  -- Update request status
  update theme_requests
  set
    status = 'completed',
    processing_time_ms = p_processing_time_ms,
    updated_at = now()
  where id = p_request_id;

  return v_theme_id;
end;
$$;

comment on function complete_theme(uuid, text, jsonb, jsonb, jsonb, integer) is 
  'Marks a theme request as completed and creates the theme';

-- ------------------------------------------------------
-- Theme failure function
-- ------------------------------------------------------
-- Function to mark a theme request as failed
create or replace function fail_theme(
  p_request_id uuid,
  p_error_message text
)
returns void
language plpgsql
security definer
as $$
begin
  -- Check if request exists
  if not exists(select 1 from theme_requests where id = p_request_id) then
    raise exception 'Theme request not found';
  end if;
  
  -- Check if request is already completed or failed
  if exists(select 1 from theme_requests where id = p_request_id and status != 'pending') then
    raise exception 'Theme request is not in pending status';
  end if;

  -- Update request status
  update theme_requests
  set
    status = 'failed',
    error_message = p_error_message,
    updated_at = now()
  where id = p_request_id;
end;
$$;

comment on function fail_theme(uuid, text) is 
  'Marks a theme request as failed with an error message';

-- ------------------------------------------------------
-- Theme version creation function
-- ------------------------------------------------------
-- Function to create a new version of a theme
create or replace function create_theme_version(
  p_theme_id uuid,
  p_colors jsonb,
  p_dark_colors jsonb,
  p_change_description text
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_current_version integer;
  v_new_version_id uuid;
  v_user_id text := requesting_user_id();
begin
  -- Check ownership
  if not exists (select 1 from themes where id = p_theme_id and user_id = v_user_id) then
    raise exception 'Not authorized to create version for this theme';
  end if;

  -- Get current version
  select version into v_current_version from themes where id = p_theme_id;

  -- Create new version record
  insert into theme_versions (
    theme_id,
    version_number,
    colors,
    dark_colors,
    change_description,
    created_by
  ) values (
    p_theme_id,
    v_current_version + 1,
    p_colors,
    p_dark_colors,
    p_change_description,
    v_user_id
  ) returning id into v_new_version_id;

  -- Update theme with new data
  update themes
  set
    colors = p_colors,
    dark_colors = p_dark_colors,
    version = v_current_version + 1,
    updated_at = now()
  where id = p_theme_id;

  return v_new_version_id;
end;
$$;

comment on function create_theme_version(uuid, jsonb, jsonb, text) is 
  'Creates a new version of a theme and updates the theme with the new data';

-- ------------------------------------------------------
-- Toggle theme favorite status
-- ------------------------------------------------------
-- Function to toggle the favorite status of a theme
create or replace function toggle_theme_favorite(
  p_theme_id uuid
)
returns boolean
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_is_favorite boolean;
begin
  -- Check ownership
  if not exists (select 1 from themes where id = p_theme_id and user_id = v_user_id) then
    raise exception 'Not authorized to modify this theme';
  end if;

  -- Toggle favorite status
  update themes
  set is_favorite = not is_favorite
  where id = p_theme_id
  returning is_favorite into v_is_favorite;

  return v_is_favorite;
end;
$$;

comment on function toggle_theme_favorite(uuid) is 
  'Toggles the favorite status of a theme and returns the new status';

-- ------------------------------------------------------
-- Toggle theme public status
-- ------------------------------------------------------
-- Function to toggle the public status of a theme
create or replace function toggle_theme_public(
  p_theme_id uuid
)
returns boolean
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_is_public boolean;
begin
  -- Check ownership
  if not exists (select 1 from themes where id = p_theme_id and user_id = v_user_id) then
    raise exception 'Not authorized to modify this theme';
  end if;

  -- Toggle public status
  update themes
  set is_public = not is_public
  where id = p_theme_id
  returning is_public into v_is_public;

  return v_is_public;
end;
$$;

comment on function toggle_theme_public(uuid) is 
  'Toggles the public status of a theme and returns the new status';

-- ------------------------------------------------------
-- Delete theme function
-- ------------------------------------------------------
-- Function to delete a theme and its request
create or replace function delete_theme(
  p_theme_id uuid
)
returns void
language plpgsql
security definer
as $$
declare
  v_user_id text := requesting_user_id();
  v_request_id uuid;
begin
  -- Check ownership
  if not exists (select 1 from themes where id = p_theme_id and user_id = v_user_id) then
    raise exception 'Not authorized to delete this theme';
  end if;

  -- Get the request ID
  select request_id into v_request_id from themes where id = p_theme_id;

  -- Delete the theme (will cascade to versions)
  delete from themes where id = p_theme_id;
  
  -- Delete the request
  delete from theme_requests where id = v_request_id;
end;
$$;

comment on function delete_theme(uuid) is 
  'Deletes a theme, its versions, and the associated request';