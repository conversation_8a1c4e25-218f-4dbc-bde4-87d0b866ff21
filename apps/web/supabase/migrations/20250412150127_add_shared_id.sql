-- Migration file: 20250412150127_add_shared_id.sql
-- Description: Adds shared_id field to themes table for secure public sharing
-- This migration improves security by using a separate ID for public theme sharing

-- ------------------------------------------------------
-- Add shared_id column to themes table
-- ------------------------------------------------------

-- Add the shared_id column
ALTER TABLE themes
ADD COLUMN shared_id uuid;

-- Add comment to explain the purpose of the field
COMMENT ON COLUMN themes.shared_id IS 'Separate ID used for public sharing of themes';

-- Create index for efficient lookups
CREATE INDEX idx_themes_shared_id ON themes(shared_id) WHERE shared_id IS NOT NULL;

-- Create composite index for public themes with shared_id
CREATE INDEX idx_themes_public_shared ON themes(is_public, shared_id)
WHERE is_public = true;

-- ------------------------------------------------------
-- Update toggle_theme_public function to manage shared_id
-- ------------------------------------------------------

CREATE OR REPLACE FUNCTION toggle_theme_public(
  p_theme_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id text := requesting_user_id();
  v_is_public boolean;
  v_current_shared_id uuid;
BEGIN
  -- Check ownership
  IF NOT EXISTS (SELECT 1 FROM themes WHERE id = p_theme_id AND user_id = v_user_id) THEN
    RAISE EXCEPTION 'Not authorized to modify this theme';
  END IF;

  -- Get current values
  SELECT is_public, shared_id
  INTO v_is_public, v_current_shared_id
  FROM themes
  WHERE id = p_theme_id;

  -- Toggle public status and manage shared_id
  UPDATE themes
  SET
    is_public = NOT v_is_public,
    -- Generate new shared_id if becoming public and doesn't have one
    shared_id = CASE
      WHEN NOT v_is_public AND v_current_shared_id IS NULL THEN gen_random_uuid()
      -- Keep existing shared_id if already public
      WHEN NOT v_is_public AND v_current_shared_id IS NOT NULL THEN v_current_shared_id
      -- Remove shared_id if becoming private
      WHEN v_is_public THEN NULL
      ELSE v_current_shared_id
    END
  WHERE id = p_theme_id
  RETURNING is_public INTO v_is_public;

  RETURN v_is_public;
END;
$$;

COMMENT ON FUNCTION toggle_theme_public(uuid) IS
  'Toggles the public status of a theme, manages shared_id, and returns the new status';

-- ------------------------------------------------------
-- Add function to regenerate shared_id
-- ------------------------------------------------------

CREATE OR REPLACE FUNCTION regenerate_theme_shared_id(
  p_theme_id uuid
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id text := requesting_user_id();
  v_new_shared_id uuid;
  v_is_public boolean;
BEGIN
  -- Check ownership
  IF NOT EXISTS (SELECT 1 FROM themes WHERE id = p_theme_id AND user_id = v_user_id) THEN
    RAISE EXCEPTION 'Not authorized to modify this theme';
  END IF;

  -- Check if theme is public
  SELECT is_public INTO v_is_public FROM themes WHERE id = p_theme_id;

  IF NOT v_is_public THEN
    RAISE EXCEPTION 'Cannot regenerate shared_id for private theme';
  END IF;

  -- Generate new shared_id
  v_new_shared_id := gen_random_uuid();

  -- Update the theme
  UPDATE themes
  SET shared_id = v_new_shared_id
  WHERE id = p_theme_id;

  RETURN v_new_shared_id;
END;
$$;

COMMENT ON FUNCTION regenerate_theme_shared_id(uuid) IS
  'Regenerates the shared_id for a public theme to revoke access to old shared links';

-- ------------------------------------------------------
-- Drop and recreate theme_details view to include shared_id
-- ------------------------------------------------------

-- First drop the existing view
DROP VIEW IF EXISTS theme_details;

-- Then create it again with the shared_id field
CREATE VIEW theme_details AS
SELECT
  t.id,
  t.request_id,
  t.user_id,
  t.name,
  t.theme_description,
  tr.user_prompt,
  t.format,
  t.colors,
  t.dark_colors,
  t.analysis,
  t.created_at,
  t.updated_at,
  t.is_favorite,
  t.is_public,
  t.shared_id,
  t.version,
  tr.include_dark_mode,
  tr.include_sidebar,
  tr.include_chart,
  tr.image_path,
  tr.model,
  tr.status
FROM
  themes t
JOIN
  theme_requests tr ON t.request_id = tr.id;

COMMENT ON VIEW theme_details IS 'Combined view of themes with their original request details';

-- Note: We don't need to create a new RLS policy for public themes
-- because there's already a policy "Users can see their own themes or public themes"
-- that allows access to themes where is_public = true
