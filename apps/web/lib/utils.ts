import { AIColor, AIColorFormat } from "@/schema/theme";

export const generateThemeCss = ({
  format,
  colors,
  dark_colors,
}: {
  format: AIColorFormat;
  colors: AIColor;
  dark_colors?: AIColor;
}): string => {
  // Helper function to format color values
  const formatColorValue = (value: string, format: AIColorFormat): string => {
    if (format === "hsl") {
      // If the value is in hsl() format, extract the values
      if (value.startsWith("hsl(")) {
        value = value.replace(/hsl\(([^)]+)\)/, "$1");
      }

      // Replace commas with spaces (even if already partially formatted)
      return value.replace(/,\s*/g, " ");
    }
    // For OKLCH, return as is
    return value;
  };

  // Create CSS block for a specific mode with proper indentation
  const createCssForMode = (
    colorSet: AIColor,
    selector: string,
    format: AIColorFormat,
    indent = "",
    extraVarIndent = ""
  ): string => {
    if (!colorSet) return "";

    const cssLines = Object.entries(colorSet)
      .map(([key, value]) => {
        const formattedValue = formatColorValue(value, format);
        return `${indent}${extraVarIndent}  --${key}: ${formattedValue};`;
      })
      .join("\n");

    return `${indent}${selector} {\n${cssLines}\n${indent}}`;
  };

  // Generate final CSS based on format
  if (format === "hsl") {
    // For HSL format: add extra indentation and wrap in @layer
    // Note the extra space for variable indentation
    const lightCss = createCssForMode(colors, " :root", format, "  ", " ");
    const darkCss = dark_colors
      ? createCssForMode(dark_colors, ".dark", format, "  ", " ")
      : "";

    return `@layer base {\n${lightCss}${darkCss ? "\n" + darkCss : ""}\n}`;
  } else {
    // For OKLCH format: no extra indentation or wrapper
    const lightCss = createCssForMode(colors, ":root", format);
    const darkCss = dark_colors
      ? createCssForMode(dark_colors, ".dark", format)
      : "";

    return `${lightCss}${darkCss ? "\n\n" + darkCss : ""}`;
  }
};
