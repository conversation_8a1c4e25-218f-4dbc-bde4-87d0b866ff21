import {
  DataStreamWriter,
  formatDataStreamPart,
  generateId,
  Message,
  ToolSet,
} from "ai";
import { z } from "zod";

import { saveChat } from "@/services/chat";
import { TOOL_RESPONSE } from "./client-utils";

// Form field validation schemas
export const formSchemas = {
  userProfile: z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Invalid email format"),
    age: z.number().min(18, "Must be at least 18 years old").optional(),
  }),
  feedback: z.object({
    rating: z.number().min(1).max(5),
    comments: z.string().optional(),
  }),
  // Add more form schemas as needed
};

// Constants for tool result states are imported from client-utils.ts

/**
 * Error type for tool processing errors
 */
export interface ToolProcessingError {
  toolCallId: string;
  toolName: string;
  error: Error | string | unknown;
  errorType: string;
  timestamp: string;
}

/**
 * Processes tool results from the client, handling both forms and confirmations
 */
export async function processToolResults<Tools extends ToolSet>(
  {
    dataStream,
    messages,
  }: {
    dataStream: DataStreamWriter;
    messages: Message[];
    tools: Tools;
  },
  handlers: {
    // Form handlers
    forms?: {
      [formName: string]: (formData: unknown) => Promise<any>;
    };
    // Confirmation handlers
    confirmations?: {
      [toolName: string]: (args: any, confirmed: boolean) => Promise<any>;
    };
    // Optional error handler for tool processing errors
    onToolError?: (error: ToolProcessingError) => Promise<void>;
  }
): Promise<Message[]> {
  const lastMessage = messages[messages.length - 1];
  if (!lastMessage?.parts) return messages;

  const processedParts = await Promise.all(
    lastMessage.parts.map(async (part) => {
      // Only process tool invocations
      if (part.type !== "tool-invocation") return part;

      const { toolInvocation } = part;
      const toolName = toolInvocation.toolName;

      // Only process tools in result state
      if (toolInvocation.state !== "result") {
        return part;
      }

      let result = toolInvocation.result;

      // Check if the tool result indicates an error from tool execution
      if (
        result &&
        typeof result === "object" &&
        "error" in result &&
        result.error === true
      ) {
        // This is an error result from a tool execution
        console.error(`Tool execution error in ${toolName}:`, result.message);

        // Call the error handler if provided
        if (handlers.onToolError) {
          try {
            await handlers.onToolError({
              toolCallId: toolInvocation.toolCallId,
              toolName,
              error: result.details || result.message,
              errorType: result.errorType || "tool_execution_error",
              timestamp: new Date().toISOString(),
            });
          } catch (errorHandlerError) {
            console.error("Error in tool error handler:", errorHandlerError);
          }
        }

        // Send the error result back to the client
        dataStream.write(
          formatDataStreamPart("tool_result", {
            toolCallId: toolInvocation.toolCallId,
            result: {
              ...result,
              success: false,
            },
          })
        );

        return {
          ...part,
          toolInvocation: { ...toolInvocation, result },
        };
      }

      // Process form submissions
      if (toolName.startsWith("openForm_") && handlers.forms) {
        const formName = toolName.replace("openForm_", "");
        const formHandler = handlers.forms[formName];

        if (formHandler && result?.status === TOOL_RESPONSE.FORM_SUBMITTED) {
          try {
            // Validate form data against schema if available
            const schema = formSchemas[formName as keyof typeof formSchemas];
            if (schema) {
              try {
                schema.parse(result.data);
              } catch (error) {
                result = {
                  success: false,
                  error: "Form validation failed",
                  details: error,
                };

                // Call the error handler if provided
                if (handlers.onToolError) {
                  try {
                    await handlers.onToolError({
                      toolCallId: toolInvocation.toolCallId,
                      toolName,
                      error,
                      errorType: "form_validation_error",
                      timestamp: new Date().toISOString(),
                    });
                  } catch (errorHandlerError) {
                    console.error(
                      "Error in tool error handler:",
                      errorHandlerError
                    );
                  }
                }

                // Send validation error back to client
                dataStream.write(
                  formatDataStreamPart("tool_result", {
                    toolCallId: toolInvocation.toolCallId,
                    result,
                  })
                );

                return {
                  ...part,
                  toolInvocation: { ...toolInvocation, result },
                };
              }
            }

            // Process valid form data
            const handlerResult = await formHandler(result.data);
            result = {
              ...handlerResult,
              formData: result.data,
              formType: formName,
            };
          } catch (error) {
            result = {
              success: false,
              error: "Failed to process form data",
              formData: result.data,
              formType: formName,
              details: error instanceof Error ? error.message : String(error),
            };

            // Call the error handler if provided
            if (handlers.onToolError) {
              try {
                await handlers.onToolError({
                  toolCallId: toolInvocation.toolCallId,
                  toolName,
                  error,
                  errorType: "form_processing_error",
                  timestamp: new Date().toISOString(),
                });
              } catch (errorHandlerError) {
                console.error(
                  "Error in tool error handler:",
                  errorHandlerError
                );
              }
            }
          }
        } else if (result?.status === TOOL_RESPONSE.FORM_CANCELLED) {
          result = {
            success: false,
            cancelled: true,
            message: "User cancelled the form submission",
          };
        }
      }
      // Process confirmations
      else if (handlers.confirmations && handlers.confirmations[toolName]) {
        const confirmHandler = handlers.confirmations[toolName];

        if (result === TOOL_RESPONSE.CONFIRMED) {
          try {
            result = await confirmHandler(toolInvocation.args, true);
          } catch (error) {
            result = {
              success: false,
              error: "Failed to process confirmation",
              details: error instanceof Error ? error.message : String(error),
            };

            // Call the error handler if provided
            if (handlers.onToolError) {
              try {
                await handlers.onToolError({
                  toolCallId: toolInvocation.toolCallId,
                  toolName,
                  error,
                  errorType: "confirmation_processing_error",
                  timestamp: new Date().toISOString(),
                });
              } catch (errorHandlerError) {
                console.error(
                  "Error in tool error handler:",
                  errorHandlerError
                );
              }
            }
          }
        } else if (result === TOOL_RESPONSE.DENIED) {
          try {
            result = await confirmHandler(toolInvocation.args, false);
          } catch (error) {
            result = {
              success: false,
              error: "Failed to process denial",
              details: error instanceof Error ? error.message : String(error),
            };

            // Call the error handler if provided
            if (handlers.onToolError) {
              try {
                await handlers.onToolError({
                  toolCallId: toolInvocation.toolCallId,
                  toolName,
                  error,
                  errorType: "denial_processing_error",
                  timestamp: new Date().toISOString(),
                });
              } catch (errorHandlerError) {
                console.error(
                  "Error in tool error handler:",
                  errorHandlerError
                );
              }
            }
          }
        }
      }

      // Send result back to client
      dataStream.write(
        formatDataStreamPart("tool_result", {
          toolCallId: toolInvocation.toolCallId,
          result,
        })
      );

      // Update the message part
      return { ...part, toolInvocation: { ...toolInvocation, result } };
    })
  );

  return [...messages.slice(0, -1), { ...lastMessage, parts: processedParts }];
}

/**
 * Returns a list of tools that require confirmation
 */
export function getToolsRequiringConfirmation<T extends ToolSet>(
  tools: T
): string[] {
  return (Object.keys(tools) as (keyof T)[]).filter((key) => {
    const maybeTool = tools[key];
    return typeof maybeTool?.execute !== "function";
  }) as string[];
}

/**
 * Saves an error state to the database
 * @param chatId The ID of the chat session
 * @param messages The current messages in the chat
 * @param error The error that occurred
 * @param errorType A descriptive type for the error
 * @param title The title to use for the chat session
 */
export async function saveErrorState(
  chatId: string,
  messages: Message[],
  error: unknown,
  errorType: string,
  title: string = "Chat with Error"
): Promise<void> {
  try {
    // Create an error message
    const errorMessage: Message = {
      id: generateId(),
      role: "system",
      content: `${errorType}: ${error instanceof Error ? error.message : String(error)}`,
      createdAt: new Date(),
    };

    // Check for pending tool calls in the last message and resolve them with errors
    let updatedMessages = [...messages];
    const lastMessage =
      updatedMessages.length > 0
        ? updatedMessages[updatedMessages.length - 1]
        : null;

    if (lastMessage?.parts) {
      const updatedParts: Message["parts"] = lastMessage.parts.map((part) => {
        // Only update tool invocations in "call" state
        if (
          part.type === "tool-invocation" &&
          part.toolInvocation.state === "call"
        ) {
          console.log(
            `Resolving stuck tool call: ${part.toolInvocation.toolName}`
          );
          return {
            ...part,
            toolInvocation: {
              ...part.toolInvocation,
              state: "result",
              result: {
                error: true,
                message: `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`,
                errorType: errorType,
                success: false,
              },
            },
          };
        }
        return part;
      });

      // Update the last message with resolved tool calls
      updatedMessages[updatedMessages.length - 1] = {
        ...lastMessage,
        parts: updatedParts,
      };
    }

    // Append the error message to the conversation
    updatedMessages = [...updatedMessages, errorMessage];

    // Save the updated messages to preserve error context
    await saveChat({
      id: chatId,
      messages: updatedMessages,
      title,
    });
  } catch (saveError) {
    console.error(`Error saving ${errorType} state:`, saveError);
  }
}
