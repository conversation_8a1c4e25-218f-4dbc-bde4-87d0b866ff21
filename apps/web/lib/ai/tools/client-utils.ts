"use client";

import { ToolSet } from "ai";
import { z } from "zod";

// Form field validation schemas (same as in utils.ts)
export const formSchemas = {
  userProfile: z.object({
    name: z.string().min(2, "Name must be at least 2 characters"),
    email: z.string().email("Invalid email format"),
    age: z.number().min(18, "Must be at least 18 years old").optional(),
  }),
  feedback: z.object({
    rating: z.number().min(1).max(5),
    comments: z.string().optional(),
  }),
  // Add more form schemas as needed
};

// Constants for tool result states (same as in utils.ts)
export const TOOL_RESPONSE = {
  // Form responses
  FORM_SUBMITTED: "form_submitted",
  FORM_CANCELLED: "form_cancelled",
  // Confirmation responses
  CONFIRMED: "Yes, confirmed.",
  DENIED: "No, denied.",
} as const;

/**
 * Error type for tool processing errors
 */
export interface ToolProcessingError {
  toolCallId: string;
  toolName: string;
  error: Error | string | unknown;
  errorType: string;
  timestamp: string;
}

/**
 * Returns a list of tools that require confirmation
 * This is a client-safe version of the function
 */
export function getToolsRequiringConfirmation<T extends ToolSet>(
  tools: T
): string[] {
  return (Object.keys(tools) as (keyof T)[]).filter((key) => {
    const maybeTool = tools[key];
    return typeof maybeTool?.execute !== "function";
  }) as string[];
}
