import { tool as createTool, generateObject } from "ai";
import { z } from "zod";

import {
  createBaseColorsSystemPrompt,
  createExtendColorsSystemPrompt,
} from "@/lib/ai/prompt";
import {
  BaseColorSchema,
  ThemeRequestSchema,
  ThemeSchema,
} from "@/schema/theme";
import { claude, google } from "../client";

//const exa = new Exa(process.env.NEXT_PUBLIC_EXA_API_KEY);
const askToFillForm = createTool({
  description:
    "Ask the user to fill a theme configuration form. Use this tool when you need to collect structured theme preferences from the user. The form includes the following fields:\n\n- description: Text field for the theme description/prompt\n- format: Color format selection (options: 'hsl' for Tailwind v3 or 'oklch' for Tailwind v4)\n- includeDarkMode: Boolean toggle for dark mode colors\n- includeSidebar: Boolean toggle for sidebar-specific colors\n- includeChart: Boolean toggle for chart/data visualization colors\n- borderRadius: Border radius selection (options: '0', '0.3', '0.5', '0.75', '1')\n\nThe form will be pre-filled with the description you provide. Use this tool after understanding the user's preferences through conversation.",
  parameters: z.object({
    message: z
      .string()
      .describe(
        "Message to show to the user explaining why they need to fill the form"
      ),
    description: z
      .string()
      .describe(
        "Optional theme description extracted from the conversation to pre-fill the form. This is the prompt used for creating the theme. Be sure craft a clear and specific description."
      ),
  }),
  // No execute function - this will trigger HITL
});

const createBaseColors = createTool({
  description:
    "Create base colors for the shadcn theme based on user preferences. Only call this after collecting form data through askToFillForm. This tool generates the four foundational colors: background, foreground, primary, and primary-foreground.",
  parameters: ThemeRequestSchema,
  execute: async ({
    description,
    format,
    borderRadius,
    includeChart,
    includeDarkMode,
    includeSidebar,
  }) => {
    try {
      const baseColor = await generateObject({
        model: claude,
        schema: BaseColorSchema,
        system: createBaseColorsSystemPrompt(),
        prompt: `Create base colors using:
      - Description: "${description}".
      - Color format: ${format}

      User's preferences:
      - ${includeDarkMode ? "Include dark mode" : "Do not include dark mode"}.
      - ${
        includeSidebar
          ? "Include sidebar colors"
          : "Do not include sidebar colors"
      }
      - ${includeChart ? "Include chart colors" : "Do not include chart colors"}
      - Border radius: ${borderRadius}rem

      `,
      });
      return baseColor;
    } catch (error) {
      console.error("Error generating base colors:", error);
      return {
        error: true,
        message: "Failed to generate base colors. Please try again.",
        details: error instanceof Error ? error.message : String(error),
        errorType: "base_colors_generation_failed",
      };
    }
  },
});

// Tool to confirm base colors before proceeding to full theme
const confirmBaseColors = createTool({
  description:
    "Ask the user if they are satisfied with the generated base colors before proceeding to create a full theme. Always call this after base colors are generated and before extending to a full theme. Explain the color relationships to help the user make an informed decision.",
  parameters: z.object({
    message: z
      .string()
      .describe(
        "Message to show to the user asking for confirmation of the base colors"
      ),
    baseColors: z
      .record(z.string())
      .optional()
      .describe("The base colors to confirm"),
    themeConfig: z
      .record(z.any())
      .optional()
      .describe("The theme configuration"),
  }),
  // No execute function - this will trigger HITL
});

// Tool to search the web for up-to-date information
// const webSearch = createTool({
//   description: "Search the web for up-to-date information",
//   parameters: z.object({
//     query: z.string().min(1).max(100).describe("The search query"),
//   }),
//   execute: async ({ query }) => {
//     const { results } = await exa.searchAndContents(query, {
//       livecrawl: "auto",
//       numResults: 3,
//     });
//     return results.map((result) => ({
//       title: result.title,
//       url: result.url,
//       content: result.text.slice(0, 1000), // take just the first 1000 characters
//       publishedDate: result.publishedDate,
//     }));
//   },
// });

// Tool to suggest previewing and applying the generated theme
// const previewTheme = createTool({
//   description: "Suggest the user to preview and apply the generated theme",
//   parameters: z.object({
//     message: z.string().describe("Message to show to the user about the theme"),
//     themeDescription: z
//       .string()
//       .optional()
//       .describe("A description of the theme's characteristics"),
//     themeName: z
//       .string()
//       .optional()
//       .describe("The name of the generated theme"),
//     themeData: z
//       .record(z.any())
//       .optional()
//       .describe("The theme data to preview"),
//   }),
//   // No execute function - this will trigger HITL
// });

const extendBaseColors = createTool({
  description:
    "Extend base colors to create a complete theme. Only call this after the user has confirmed the base colors. This tool generates all additional colors needed for a comprehensive UI color system, including secondary, accent, muted, destructive, and utility colors.",
  parameters: BaseColorSchema,
  execute: async ({
    colors: {
      background,
      foreground,
      primary,
      "primary-foreground": primaryForeground,
    },
    format,
    includeDarkMode,
    includeSidebar,
    includeChart,
    borderRadius,
    description,
  }) => {
    try {
      const baseColors = {
        background,
        foreground,
        primary,
        "primary-foreground": primaryForeground,
      };

      //TODO ADD EXAMPLES from RAG
      //   const examples = await searchEmbeddings(
      //     "# Shadcn UI Theme Examples",
      //     {
      //       similarityThreshold: 0.5,
      //       limit: 4,
      //       includeResourceInfo: false,
      //     }
      //   );
      // Before finalizing the theme, analyze ${examples.join("\n\n")} and use them as inspiration for creating a unique theme. Do not copy any of the examples, be creative and original.
      const { object: fullTheme } = await generateObject({
        model: google,
        schema: ThemeSchema,
        system: createExtendColorsSystemPrompt({ format }),
        prompt: `
              Expand these base colors into a complete theme based on user's description: "${description}"

                  Base colors:
                  ${JSON.stringify(baseColors, null, 2)}

                  Requirements:
                  - ${
                    includeDarkMode
                      ? "Include dark mode"
                      : "Do not include dark mode"
                  }.
                  - ${
                    includeSidebar
                      ? "Include sidebar colors"
                      : "Do not include sidebar colors"
                  }
                  - ${
                    includeChart
                      ? "Include chart colors"
                      : "Do not include chart colors"
                  }
                  - Use ${format} format for all colors
                  - Border radius: ${borderRadius}rem

                  Create a cohesive theme that builds upon the provided base colors. Maintain the character and mood established by the base colors while ensuring all color combinations are accessible and harmonious. Be creative with the theme name and provide a thoughtful description that explains the color theory choices.`,
      });
      return fullTheme;
    } catch (error) {
      console.error("Error extending base colors to full theme:", error);
      return {
        error: true,
        message: "Failed to generate the full theme. Please try again.",
        details: error instanceof Error ? error.message : String(error),
        errorType: "theme_extension_failed",
      };
    }
  },
});

export const tools = {
  askToFillForm,
  createBaseColors,
  // confirmBaseColors,
  extendBaseColors,
};
