import dedent from "dedent";

export const themeExamples = {
  default: {
    oklch: dedent`
      :root {
        --radius: 1rem;
        --background: oklch(1 0 0);
        --foreground: oklch(0.141 0.005 285.823);
        --card: oklch(1 0 0);
        --card-foreground: oklch(0.141 0.005 285.823);
        --popover: oklch(1 0 0);
        --popover-foreground: oklch(0.141 0.005 285.823);
        --primary: oklch(0.21 0.006 285.885);
        --primary-foreground: oklch(0.985 0 0);
        --secondary: oklch(0.967 0.001 286.375);
        --secondary-foreground: oklch(0.21 0.006 285.885);
        --muted: oklch(0.967 0.001 286.375);
        --muted-foreground: oklch(0.552 0.016 285.938);
        --accent: oklch(0.967 0.001 286.375);
        --accent-foreground: oklch(0.21 0.006 285.885);
        --destructive: oklch(0.577 0.245 27.325);
        --border: oklch(0.92 0.004 286.32);
        --input: oklch(0.92 0.004 286.32);
        --ring: oklch(0.705 0.015 286.067);
        --chart-1: oklch(0.646 0.222 41.116);
        --chart-2: oklch(0.6 0.118 184.704);
        --chart-3: oklch(0.398 0.07 227.392);
        --chart-4: oklch(0.828 0.189 84.429);
        --chart-5: oklch(0.769 0.188 70.08);
        --sidebar: oklch(0.985 0 0);
        --sidebar-foreground: oklch(0.141 0.005 285.823);
        --sidebar-primary: oklch(0.21 0.006 285.885);
        --sidebar-primary-foreground: oklch(0.985 0 0);
        --sidebar-accent: oklch(0.967 0.001 286.375);
        --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
        --sidebar-border: oklch(0.92 0.004 286.32);
        --sidebar-ring: oklch(0.705 0.015 286.067);
      }

      .dark {
        --background: oklch(0.141 0.005 285.823);
        --foreground: oklch(0.985 0 0);
        --card: oklch(0.21 0.006 285.885);
        --card-foreground: oklch(0.985 0 0);
        --popover: oklch(0.21 0.006 285.885);
        --popover-foreground: oklch(0.985 0 0);
        --primary: oklch(0.92 0.004 286.32);
        --primary-foreground: oklch(0.21 0.006 285.885);
        --secondary: oklch(0.274 0.006 286.033);
        --secondary-foreground: oklch(0.985 0 0);
        --muted: oklch(0.274 0.006 286.033);
        --muted-foreground: oklch(0.705 0.015 286.067);
        --accent: oklch(0.274 0.006 286.033);
        --accent-foreground: oklch(0.985 0 0);
        --destructive: oklch(0.704 0.191 22.216);
        --border: oklch(1 0 0 / 10%);
        --input: oklch(1 0 0 / 15%);
        --ring: oklch(0.552 0.016 285.938);
        --chart-1: oklch(0.488 0.243 264.376);
        --chart-2: oklch(0.696 0.17 162.48);
        --chart-3: oklch(0.769 0.188 70.08);
        --chart-4: oklch(0.627 0.265 303.9);
        --chart-5: oklch(0.645 0.246 16.439);
        --sidebar: oklch(0.21 0.006 285.885);
        --sidebar-foreground: oklch(0.985 0 0);
        --sidebar-primary: oklch(0.488 0.243 264.376);
        --sidebar-primary-foreground: oklch(0.985 0 0);
        --sidebar-accent: oklch(0.274 0.006 286.033);
        --sidebar-accent-foreground: oklch(0.985 0 0);
        --sidebar-border: oklch(1 0 0 / 10%);
        --sidebar-ring: oklch(0.552 0.016 285.938);
      }
    `,
    hsl: dedent`
      @layer base {
        :root {
          --background: 0 0% 100%;
          --foreground: 240 10% 3.9%;
          --card: 0 0% 100%;
          --card-foreground: 240 10% 3.9%;
          --popover: 0 0% 100%;
          --popover-foreground: 240 10% 3.9%;
          --primary: 240 5.9% 10%;
          --primary-foreground: 0 0% 98%;
          --secondary: 240 4.8% 95.9%;
          --secondary-foreground: 240 5.9% 10%;
          --muted: 240 4.8% 95.9%;
          --muted-foreground: 240 3.8% 46.1%;
          --accent: 240 4.8% 95.9%;
          --accent-foreground: 240 5.9% 10%;
          --destructive: 0 84.2% 60.2%;
          --destructive-foreground: 0 0% 98%;
          --border: 240 5.9% 90%;
          --input: 240 5.9% 90%;
          --ring: 240 5.9% 10%;
          --radius: 1rem;
          --chart-1: 12 76% 61%;
          --chart-2: 173 58% 39%;
          --chart-3: 197 37% 24%;
          --chart-4: 43 74% 66%;
          --chart-5: 27 87% 67%;
        }

        .dark {
          --background: 240 10% 3.9%;
          --foreground: 0 0% 98%;
          --card: 240 10% 3.9%;
          --card-foreground: 0 0% 98%;
          --popover: 240 10% 3.9%;
          --popover-foreground: 0 0% 98%;
          --primary: 0 0% 98%;
          --primary-foreground: 240 5.9% 10%;
          --secondary: 240 3.7% 15.9%;
          --secondary-foreground: 0 0% 98%;
          --muted: 240 3.7% 15.9%;
          --muted-foreground: 240 5% 64.9%;
          --accent: 240 3.7% 15.9%;
          --accent-foreground: 0 0% 98%;
          --destructive: 0 62.8% 30.6%;
          --destructive-foreground: 0 0% 98%;
          --border: 240 3.7% 15.9%;
          --input: 240 3.7% 15.9%;
          --ring: 240 4.9% 83.9%;
          --chart-1: 220 70% 50%;
          --chart-2: 160 60% 45%;
          --chart-3: 30 80% 55%;
          --chart-4: 280 65% 60%;
          --chart-5: 340 75% 55%;
        }
      }
    `,
  },
  red: {
    oklch: dedent`
      :root {
        --radius: 0.5rem;
        --background: oklch(1 0 0);
        --foreground: oklch(0.141 0.005 285.823);
        --card: oklch(1 0 0);
        --card-foreground: oklch(0.141 0.005 285.823);
        --popover: oklch(1 0 0);
        --popover-foreground: oklch(0.141 0.005 285.823);
        --primary: oklch(0.637 0.237 25.331);
        --primary-foreground: oklch(0.971 0.013 17.38);
        --secondary: oklch(0.967 0.001 286.375);
        --secondary-foreground: oklch(0.21 0.006 285.885);
        --muted: oklch(0.967 0.001 286.375);
        --muted-foreground: oklch(0.552 0.016 285.938);
        --accent: oklch(0.967 0.001 286.375);
        --accent-foreground: oklch(0.21 0.006 285.885);
        --destructive: oklch(0.577 0.245 27.325);
        --border: oklch(0.92 0.004 286.32);
        --input: oklch(0.92 0.004 286.32);
        --ring: oklch(0.637 0.237 25.331);
        --chart-1: oklch(0.646 0.222 41.116);
        --chart-2: oklch(0.6 0.118 184.704);
        --chart-3: oklch(0.398 0.07 227.392);
        --chart-4: oklch(0.828 0.189 84.429);
        --chart-5: oklch(0.769 0.188 70.08);
        --sidebar: oklch(0.985 0 0);
        --sidebar-foreground: oklch(0.141 0.005 285.823);
        --sidebar-primary: oklch(0.637 0.237 25.331);
        --sidebar-primary-foreground: oklch(0.971 0.013 17.38);
        --sidebar-accent: oklch(0.967 0.001 286.375);
        --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
        --sidebar-border: oklch(0.92 0.004 286.32);
        --sidebar-ring: oklch(0.637 0.237 25.331);
      }

      .dark {
        --background: oklch(0.141 0.005 285.823);
        --foreground: oklch(0.985 0 0);
        --card: oklch(0.21 0.006 285.885);
        --card-foreground: oklch(0.985 0 0);
        --popover: oklch(0.21 0.006 285.885);
        --popover-foreground: oklch(0.985 0 0);
        --primary: oklch(0.637 0.237 25.331);
        --primary-foreground: oklch(0.971 0.013 17.38);
        --secondary: oklch(0.274 0.006 286.033);
        --secondary-foreground: oklch(0.985 0 0);
        --muted: oklch(0.274 0.006 286.033);
        --muted-foreground: oklch(0.705 0.015 286.067);
        --accent: oklch(0.274 0.006 286.033);
        --accent-foreground: oklch(0.985 0 0);
        --destructive: oklch(0.704 0.191 22.216);
        --border: oklch(1 0 0 / 10%);
        --input: oklch(1 0 0 / 15%);
        --ring: oklch(0.637 0.237 25.331);
        --chart-1: oklch(0.488 0.243 264.376);
        --chart-2: oklch(0.696 0.17 162.48);
        --chart-3: oklch(0.769 0.188 70.08);
        --chart-4: oklch(0.627 0.265 303.9);
        --chart-5: oklch(0.645 0.246 16.439);
        --sidebar: oklch(0.21 0.006 285.885);
        --sidebar-foreground: oklch(0.985 0 0);
        --sidebar-primary: oklch(0.637 0.237 25.331);
        --sidebar-primary-foreground: oklch(0.971 0.013 17.38);
        --sidebar-accent: oklch(0.274 0.006 286.033);
        --sidebar-accent-foreground: oklch(0.985 0 0);
        --sidebar-border: oklch(1 0 0 / 10%);
        --sidebar-ring: oklch(0.637 0.237 25.331);
      }
    `,
    hsl: dedent`
      @layer base {
        :root {
          --background: 0 0% 100%;
          --foreground: 0 0% 3.9%;
          --card: 0 0% 100%;
          --card-foreground: 0 0% 3.9%;
          --popover: 0 0% 100%;
          --popover-foreground: 0 0% 3.9%;
          --primary: 0 72.2% 50.6%;
          --primary-foreground: 0 85.7% 97.3%;
          --secondary: 0 0% 96.1%;
          --secondary-foreground: 0 0% 9%;
          --muted: 0 0% 96.1%;
          --muted-foreground: 0 0% 45.1%;
          --accent: 0 0% 96.1%;
          --accent-foreground: 0 0% 9%;
          --destructive: 0 84.2% 60.2%;
          --destructive-foreground: 0 0% 98%;
          --border: 0 0% 89.8%;
          --input: 0 0% 89.8%;
          --ring: 0 72.2% 50.6%;
          --radius: 0.5rem;
          --chart-1: 12 76% 61%;
          --chart-2: 173 58% 39%;
          --chart-3: 197 37% 24%;
          --chart-4: 43 74% 66%;
          --chart-5: 27 87% 67%;
        }

        .dark {
          --background: 0 0% 3.9%;
          --foreground: 0 0% 98%;
          --card: 0 0% 3.9%;
          --card-foreground: 0 0% 98%;
          --popover: 0 0% 3.9%;
          --popover-foreground: 0 0% 98%;
          --primary: 0 72.2% 50.6%;
          --primary-foreground: 0 85.7% 97.3%;
          --secondary: 0 0% 14.9%;
          --secondary-foreground: 0 0% 98%;
          --muted: 0 0% 14.9%;
          --muted-foreground: 0 0% 63.9%;
          --accent: 0 0% 14.9%;
          --accent-foreground: 0 0% 98%;
          --destructive: 0 62.8% 30.6%;
          --destructive-foreground: 0 0% 98%;
          --border: 0 0% 14.9%;
          --input: 0 0% 14.9%;
          --ring: 0 72.2% 50.6%;
          --chart-1: 220 70% 50%;
          --chart-2: 160 60% 45%;
          --chart-3: 30 80% 55%;
          --chart-4: 280 65% 60%;
          --chart-5: 340 75% 55%;
        }
      }
    `,
  },
  rose: {
    oklch: dedent`
      :root {
        --radius: 0.5rem;
        --background: oklch(1 0 0);
        --foreground: oklch(0.141 0.005 285.823);
        --card: oklch(1 0 0);
        --card-foreground: oklch(0.141 0.005 285.823);
        --popover: oklch(1 0 0);
        --popover-foreground: oklch(0.141 0.005 285.823);
        --primary: oklch(0.645 0.246 16.439);
        --primary-foreground: oklch(0.969 0.015 12.422);
        --secondary: oklch(0.967 0.001 286.375);
        --secondary-foreground: oklch(0.21 0.006 285.885);
        --muted: oklch(0.967 0.001 286.375);
        --muted-foreground: oklch(0.552 0.016 285.938);
        --accent: oklch(0.967 0.001 286.375);
        --accent-foreground: oklch(0.21 0.006 285.885);
        --destructive: oklch(0.577 0.245 27.325);
        --border: oklch(0.92 0.004 286.32);
        --input: oklch(0.92 0.004 286.32);
        --ring: oklch(0.645 0.246 16.439);
        --chart-1: oklch(0.646 0.222 41.116);
        --chart-2: oklch(0.6 0.118 184.704);
        --chart-3: oklch(0.398 0.07 227.392);
        --chart-4: oklch(0.828 0.189 84.429);
        --chart-5: oklch(0.769 0.188 70.08);
        --sidebar: oklch(0.985 0 0);
        --sidebar-foreground: oklch(0.141 0.005 285.823);
        --sidebar-primary: oklch(0.645 0.246 16.439);
        --sidebar-primary-foreground: oklch(0.969 0.015 12.422);
        --sidebar-accent: oklch(0.967 0.001 286.375);
        --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
        --sidebar-border: oklch(0.92 0.004 286.32);
        --sidebar-ring: oklch(0.645 0.246 16.439);
      }

      .dark {
        --background: oklch(0.141 0.005 285.823);
        --foreground: oklch(0.985 0 0);
        --card: oklch(0.21 0.006 285.885);
        --card-foreground: oklch(0.985 0 0);
        --popover: oklch(0.21 0.006 285.885);
        --popover-foreground: oklch(0.985 0 0);
        --primary: oklch(0.645 0.246 16.439);
        --primary-foreground: oklch(0.969 0.015 12.422);
        --secondary: oklch(0.274 0.006 286.033);
        --secondary-foreground: oklch(0.985 0 0);
        --muted: oklch(0.274 0.006 286.033);
        --muted-foreground: oklch(0.705 0.015 286.067);
        --accent: oklch(0.274 0.006 286.033);
        --accent-foreground: oklch(0.985 0 0);
        --destructive: oklch(0.704 0.191 22.216);
        --border: oklch(1 0 0 / 10%);
        --input: oklch(1 0 0 / 15%);
        --ring: oklch(0.645 0.246 16.439);
        --chart-1: oklch(0.488 0.243 264.376);
        --chart-2: oklch(0.696 0.17 162.48);
        --chart-3: oklch(0.769 0.188 70.08);
        --chart-4: oklch(0.627 0.265 303.9);
        --chart-5: oklch(0.645 0.246 16.439);
        --sidebar: oklch(0.21 0.006 285.885);
        --sidebar-foreground: oklch(0.985 0 0);
        --sidebar-primary: oklch(0.645 0.246 16.439);
        --sidebar-primary-foreground: oklch(0.969 0.015 12.422);
        --sidebar-accent: oklch(0.274 0.006 286.033);
        --sidebar-accent-foreground: oklch(0.985 0 0);
        --sidebar-border: oklch(1 0 0 / 10%);
        --sidebar-ring: oklch(0.645 0.246 16.439);
      }
    `,
    hsl: dedent`
      @layer base {
        :root {
          --background: 0 0% 100%;
          --foreground: 240 10% 3.9%;
          --card: 0 0% 100%;
          --card-foreground: 240 10% 3.9%;
          --popover: 0 0% 100%;
          --popover-foreground: 240 10% 3.9%;
          --primary: 346.8 77.2% 49.8%;
          --primary-foreground: 355.7 100% 97.3%;
          --secondary: 240 4.8% 95.9%;
          --secondary-foreground: 240 5.9% 10%;
          --muted: 240 4.8% 95.9%;
          --muted-foreground: 240 3.8% 46.1%;
          --accent: 240 4.8% 95.9%;
          --accent-foreground: 240 5.9% 10%;
          --destructive: 0 84.2% 60.2%;
          --destructive-foreground: 0 0% 98%;
          --border: 240 5.9% 90%;
          --input: 240 5.9% 90%;
          --ring: 346.8 77.2% 49.8%;
          --radius: 0.5rem;
          --chart-1: 12 76% 61%;
          --chart-2: 173 58% 39%;
          --chart-3: 197 37% 24%;
          --chart-4: 43 74% 66%;
          --chart-5: 27 87% 67%;
        }

        .dark {
          --background: 20 14.3% 4.1%;
          --foreground: 0 0% 95%;
          --card: 24 9.8% 10%;
          --card-foreground: 0 0% 95%;
          --popover: 0 0% 9%;
          --popover-foreground: 0 0% 95%;
          --primary: 346.8 77.2% 49.8%;
          --primary-foreground: 355.7 100% 97.3%;
          --secondary: 240 3.7% 15.9%;
          --secondary-foreground: 0 0% 98%;
          --muted: 0 0% 15%;
          --muted-foreground: 240 5% 64.9%;
          --accent: 12 6.5% 15.1%;
          --accent-foreground: 0 0% 98%;
          --destructive: 0 62.8% 30.6%;
          --destructive-foreground: 0 85.7% 97.3%;
          --border: 240 3.7% 15.9%;
          --input: 240 3.7% 15.9%;
          --ring: 346.8 77.2% 49.8%;
          --chart-1: 220 70% 50%;
          --chart-2: 160 60% 45%;
          --chart-3: 30 80% 55%;
          --chart-4: 280 65% 60%;
          --chart-5: 340 75% 55%;
        }
      }
    `,
  },
  blue: {
    oklch: dedent`
      :root {
        --radius: 0.5rem;
        --background: oklch(1 0 0);
        --foreground: oklch(0.141 0.005 285.823);
        --card: oklch(1 0 0);
        --card-foreground: oklch(0.141 0.005 285.823);
        --popover: oklch(1 0 0);
        --popover-foreground: oklch(0.141 0.005 285.823);
        --primary: oklch(0.623 0.214 259.815);
        --primary-foreground: oklch(0.97 0.014 254.604);
        --secondary: oklch(0.967 0.001 286.375);
        --secondary-foreground: oklch(0.21 0.006 285.885);
        --muted: oklch(0.967 0.001 286.375);
        --muted-foreground: oklch(0.552 0.016 285.938);
        --accent: oklch(0.967 0.001 286.375);
        --accent-foreground: oklch(0.21 0.006 285.885);
        --destructive: oklch(0.577 0.245 27.325);
        --border: oklch(0.92 0.004 286.32);
        --input: oklch(0.92 0.004 286.32);
        --ring: oklch(0.623 0.214 259.815);
        --chart-1: oklch(0.646 0.222 41.116);
        --chart-2: oklch(0.6 0.118 184.704);
        --chart-3: oklch(0.398 0.07 227.392);
        --chart-4: oklch(0.828 0.189 84.429);
        --chart-5: oklch(0.769 0.188 70.08);
        --sidebar: oklch(0.985 0 0);
        --sidebar-foreground: oklch(0.141 0.005 285.823);
        --sidebar-primary: oklch(0.623 0.214 259.815);
        --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
        --sidebar-accent: oklch(0.967 0.001 286.375);
        --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
        --sidebar-border: oklch(0.92 0.004 286.32);
        --sidebar-ring: oklch(0.623 0.214 259.815);
      }

      .dark {
        --background: oklch(0.141 0.005 285.823);
        --foreground: oklch(0.985 0 0);
        --card: oklch(0.21 0.006 285.885);
        --card-foreground: oklch(0.985 0 0);
        --popover: oklch(0.21 0.006 285.885);
        --popover-foreground: oklch(0.985 0 0);
        --primary: oklch(0.546 0.245 262.881);
        --primary-foreground: oklch(0.379 0.146 265.522);
        --secondary: oklch(0.274 0.006 286.033);
        --secondary-foreground: oklch(0.985 0 0);
        --muted: oklch(0.274 0.006 286.033);
        --muted-foreground: oklch(0.705 0.015 286.067);
        --accent: oklch(0.274 0.006 286.033);
        --accent-foreground: oklch(0.985 0 0);
        --destructive: oklch(0.704 0.191 22.216);
        --border: oklch(1 0 0 / 10%);
        --input: oklch(1 0 0 / 15%);
        --ring: oklch(0.488 0.243 264.376);
        --chart-1: oklch(0.488 0.243 264.376);
        --chart-2: oklch(0.696 0.17 162.48);
        --chart-3: oklch(0.769 0.188 70.08);
        --chart-4: oklch(0.627 0.265 303.9);
        --chart-5: oklch(0.645 0.246 16.439);
        --sidebar: oklch(0.21 0.006 285.885);
        --sidebar-foreground: oklch(0.985 0 0);
        --sidebar-primary: oklch(0.546 0.245 262.881);
        --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
        --sidebar-accent: oklch(0.274 0.006 286.033);
        --sidebar-accent-foreground: oklch(0.985 0 0);
        --sidebar-border: oklch(1 0 0 / 10%);
        --sidebar-ring: oklch(0.488 0.243 264.376);
      }
    `,
    hsl: dedent`
    @layer base {
      :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 221.2 83.2% 53.3%;
        --radius: 0.5rem;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
      }

      .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 217.2 91.2% 59.8%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 224.3 76.3% 48%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
      }
    }
  `,
  },
};

export default themeExamples;
