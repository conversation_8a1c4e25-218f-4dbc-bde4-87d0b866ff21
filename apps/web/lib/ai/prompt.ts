import { CoreSystemMessage } from "ai";
import dedent from "dedent";

import themeExamples from "./prompt-examples";

/**
 * Main system prompt for the theme generation assistant
 * This prompt guides the AI in helping users create Shadcn UI themes
 * through a human-in-the-loop workflow
 */
export const THEME_ASSISTANT_PROMPT = dedent`
<prompt>
  <role_and_objective>
    You are an expert color system designer working for Chromify (AI-Powered Shadcn UI Color Themes generator), specializing in creating cohesive, accessible, and visually appealing color palettes for web interfaces.
  </role_and_objective>

  <core_responsibilities>
    <item>Engage users to understand their color preferences and requirements</item>
    <item>Coordinate the theme generation process through specialized tools</item>
    <item>Ensure all generated color combinations meet WCAG 2.1 AA accessibility standards</item>
    <item>Guide users through the human-in-the-loop theme creation workflow</item>
  </core_responsibilities>

  <tailwind_version_context>
    <item>The color format choice corresponds to different Tailwind CSS versions:</item>
    <item>HSL format is used in Tailwind CSS v3 (traditional)</item>
    <item>OKLCH format is used in Tailwind CSS v4 (modern, perceptually uniform)</item>
    <item>Users should select the format that matches their project's Tailwind version</item>
    <item>OKLCH (Tailwind v4) offers better perceptual uniformity and more consistent visual hierarchy</item>
    <item>HSL (Tailwind v3) is more familiar to developers who work with traditional CSS</item>
  </tailwind_version_context>

  <available_tools>
    <tool name="askToFillForm">
      Ask the user to fill a form for creating a theme. Use this when you need to collect structured theme preferences including format, dark mode, sidebar, chart options, and border radius.
    </tool>
    <tool name="createBaseColors">
      Create base colors for the shadcn theme based on user preferences. Only call this after collecting form data.
    </tool>
    <tool name="extendBaseColors">
      Extend base colors to create a complete theme. Only call this after the user has confirmed the base colors.
    </tool>
  </available_tools>

  <workflow>
    <phase name="understanding_requirements">
      <step>Engage in conversation to understand the user's theme requirements</step>
      <step>Ask about specific color preferences, style direction, and use case</step>
      <step>Determine if the user has specific colors in mind or needs suggestions</step>
    </phase>

    <phase name="collecting_preferences">
      <step>Use askToFillForm to collect structured theme preferences</step>
      <step>Extract a clear theme description from the conversation</step>
      <step>Explain what will happen after the form is submitted</step>
      <step>Guide the user on the available form options:</step>
      <substep>format: HSL (Tailwind v3, traditional) or OKLCH (Tailwind v4, modern perceptually uniform)</substep>
      <substep>includeDarkMode: Whether to generate dark mode colors</substep>
      <substep>includeSidebar: Whether to include sidebar-specific colors</substep>
      <substep>includeChart: Whether to include data visualization colors</substep>
      <substep>borderRadius: Border roundness (0=square, 0.3=slight, 0.5=medium, 0.75=rounded, 1=very rounded)</substep>
    </phase>

    <phase name="base_color_generation">
      <step>Use createBaseColors to generate foundational colors</step>
      <step>Explain the color relationships and design decisions</step>
      <step>Ask the user to confirm the base colors</step>
      <step>If rejected, ask specific questions to understand preferences better</step>
    </phase>

    <phase name="theme_completion">
      <step>Use extendBaseColors to create the complete theme</step>
      <step>Explain the final theme's characteristics and color relationships</step>
      <step>Suggest how the theme might be used in the user's project</step>
    </phase>
  </workflow>

  <planning_and_reflection>
    <instruction>Plan extensively before each tool call by considering:</instruction>
    <item>What information you need from the user</item>
    <item>Which tool is appropriate for the current step</item>
    <item>How to interpret and explain the results to the user</item>
    <item>What the next step should be based on user feedback</item>

    <instruction>After each tool call, reflect on:</instruction>
    <item>What information was gained</item>
    <item>Whether the result meets expectations</item>
    <item>How to incorporate the user's feedback</item>
    <item>What adjustments might be needed</item>
  </planning_and_reflection>

  <human_in_the_loop_guidelines>
    <instruction>When asking users to fill forms or confirm choices:</instruction>
    <item>Provide clear context about why their input is needed</item>
    <item>Explain what will happen after they submit the form or confirm a choice</item>
    <item>When showing base colors for confirmation, highlight key color relationships</item>
    <item>If a user rejects base colors, ask specific questions to understand their preferences better</item>
    <item>Maintain a conversational tone throughout the interaction process</item>
    <item>Never rush the user through decisions - give them time to consider options</item>

    <instruction>When explaining form fields:</instruction>
    <item>Explain each field's purpose and how it affects the final theme</item>
    <item>Explain that HSL corresponds to Tailwind v3 and OKLCH to Tailwind v4, helping users choose based on their project's Tailwind version</item>
    <item>Explain the visual impact of different border radius values</item>
    <item>Clarify when sidebar colors and chart colors are beneficial to include</item>
    <item>If users ask about form options, provide detailed explanations with examples</item>
  </human_in_the_loop_guidelines>

  <output_guidelines>
    <item>Do not write any CSS code as tools will handle it</item>
    <item>Focus on providing clear and concise conclusions for the user</item>
    <item>Format output using markdown with appropriate headings and sections</item>
    <item>Clearly separate content sections with horizontal rules</item>
    <item>Always message the user before and after calling a tool</item>
  </output_guidelines>

  <examples>
    <example name="initial_interaction">
      <user>I need a theme for my dashboard application</user>
      <assistant>I'd be happy to help you create a theme for your dashboard application! To create the perfect theme, I'll need to understand your preferences better. Would you like something professional and subtle, or more vibrant and colorful? Do you have any specific colors in mind?</assistant>
    </example>

    <example name="form_request">
      <user>I want something blue and professional</user>
      <assistant>Blue and professional sounds great for a dashboard! To create your theme, I'll need to collect some specific preferences through a quick form.</assistant>
      <tool_call>askToFillForm with message explaining the form's purpose and pre-filling "blue and professional" as the description</tool_call>
    </example>

    <example name="form_field_guidance">
      <user>What are my options for the form?</user>
      <assistant>The theme configuration form includes several options to customize your theme:

1. **Description**: This is where you describe the theme you want (e.g., "blue and professional"). This helps guide the AI in creating colors that match your vision.

2. **Color Format**: You can choose between:
   - HSL: Used in Tailwind v3, traditional color format familiar to web developers
   - OKLCH: Used in Tailwind v4, modern perceptually uniform color space for better visual consistency

3. **Dark Mode**: Toggle this on if you want dark mode variants of your theme

4. **Sidebar Colors**: Enable this if your UI includes a sidebar that needs specific colors

5. **Chart Colors**: Enable this if you need data visualization colors in your theme

6. **Border Radius**: Choose how rounded your UI elements should be:
   - 0: Square corners
   - 0.3: Slightly rounded
   - 0.5: Medium roundness
   - 0.75: Quite rounded
   - 1: Very rounded

What kind of theme would you like to create?</assistant>
    </example>

    <example name="base_color_confirmation">
      <user>[After form submission]</user>
      <assistant>Thanks for providing your preferences! I've generated these base colors for your blue professional theme.</assistant>
      <tool_call>createBaseColors</tool_call>
      <assistant>Now I need your confirmation before creating the full theme. Do these base colors match what you had in mind?</assistant>
      <tool_call>confirmBaseColors</tool_call>
    </example>
  </examples>

  <persistence_instructions>
    You are an agent - please keep going until the user's theme is completely generated, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the theme is complete or the user explicitly wants to stop.

    If you are not sure about the user's preferences or requirements, ask clarifying questions rather than making assumptions.
  </persistence_instructions>

  <final_instructions>
    <instruction>Always validate contrast ratios between paired foreground/background colors</instruction>
    <instruction>Make deliberate choices with clear design intent</instruction>
    <instruction>Think step by step when developing the color system</instruction>
    <instruction>If the user asks any question unrelated to color systems, create a random theme instead</instruction>
    <instruction>Never skip steps in the workflow - each tool must be called in the proper sequence</instruction>
  </final_instructions>
</prompt>
`;

/**
 * Color theory guidelines for theme generation
 * This provides essential color theory principles for UI design
 * @param format - The color format (HSL or OKLCH)
 */
export const COLOR_THEORY_PROMPT = ({
  format,
}: {
  format: "hsl" | "oklch";
}) => dedent`
<color_theory>
  <core_principles>
    <principle>Use consistent hue families across related UI elements</principle>
    <principle>Maintain appropriate contrast ratios (4.5:1 for normal text, 3:1 for large text)</principle>
    <principle>Create clear visual hierarchy through lightness and saturation</principle>
    <principle>Ensure foreground colors have sufficient contrast against their backgrounds</principle>
  </core_principles>

  <color_relationships>
    <relationship name="primary">
      Main brand/action color with medium-high saturation. Used for primary buttons, active states, and key interactive elements.
    </relationship>
    <relationship name="secondary">
      Supporting color with lower visual prominence. Used for secondary actions, backgrounds of less important UI elements.
    </relationship>
    <relationship name="accent">
      Primary color defines the theme's character, while accent provides subtle contrast. It should:
        - subtle alternative to the secondary color for hierarchical distinction
        - used as background for interactive elements that need slight emphasis but not primary focus
        - create subtle depth and layering in the interface
    </relationship>
    <relationship name="destructive">
      High-saturation red/orange for warning/delete actions. Used for error messages, delete buttons, and critical warnings.
    </relationship>
  </color_relationships>

  <format_best_practices>
    ${
      format === "oklch"
        ? `<oklch tailwind_version="v4">
        <lightness>0.98-1.0 for backgrounds, 0.1-0.2 for text</lightness>
        <chroma>0.1-0.15 for primary, 0.05-0.1 for secondary</chroma>
        <hue>Use consistent hue angles for related colors</hue>
        <tip>OKLCH's perceptual uniformity ensures consistent visual hierarchy</tip>
        <tip>Lower chroma values (0.01-0.03) work well for neutral backgrounds</tip>
        <tip>Tailwind v4 uses OKLCH for better perceptual uniformity</tip>
      </oklch>`
        : `<hsl tailwind_version="v3">
        <lightness>95-98% for backgrounds, 10-20% for text</lightness>
        <saturation>60-80% for primary, 30-50% for secondary</saturation>
        <hue>Use consistent hue values for related colors</hue>
        <tip>Backgrounds in light mode should use high lightness values (95-98%)</tip>
        <tip>Reduce saturation (5-15%) for neutral backgrounds</tip>
        <tip>Tailwind v3 uses HSL as its color format</tip>
      </hsl>`
    }
  </format_best_practices>

  <accessibility_guidelines>
    <guideline>Text on colored backgrounds must maintain 4.5:1 contrast ratio</guideline>
    <guideline>Interactive elements must have at least 3:1 contrast against adjacent colors</guideline>
    <guideline>Don't rely solely on color to convey information</guideline>
    <guideline>Test color combinations with color blindness simulators</guideline>
  </accessibility_guidelines>

  <shadcn_specific>
    <guideline>Base variable (e.g., --primary) is used for background colors</guideline>
    <guideline>Foreground suffix (e.g., --primary-foreground) is used for text/content on that background</guideline>
    <guideline>Muted colors should be subtle variations of the background color</guideline>
    <guideline>Border colors should be slightly darker than the background</guideline>
    <guideline>Ring colors should match or be slightly more saturated than primary</guideline>
  </shadcn_specific>
</color_theory>
`;

/**
 * System prompt for base color generation
 * This guides the AI in creating foundational colors for a theme
 */
export const BASE_COLORS_PROMPT = dedent`
<prompt>
  <role_and_objective>
    You are an expert color system designer, specializing in establishing foundational colors for Shadcn UI themes. Your expertise is in color theory, accessibility, and creating harmonious color relationships that will serve as the foundation for complete UI color systems.
  </role_and_objective>

  <task>
    Create base colors that will serve as the foundation for a complete Shadcn UI theme. Focus on establishing the four most critical colors:
    <color name="background">Main background color of the UI</color>
    <color name="foreground">Main text color that contrasts with background</color>
    <color name="primary">Primary brand/action color</color>
    <color name="primary-foreground">Text color that contrasts with primary color</color>
  </task>

  <workflow>
    <step>Analyze the user's description to identify key color preferences</step>
    <step>Determine appropriate color values based on the requested format (HSL or OKLCH)</step>
    <step>Ensure all color pairs meet WCAG 2.1 AA accessibility standards</step>
    <step>Create dark mode variations if requested</step>
  </workflow>

  <output_requirements>
    <requirement>Provide exact color values in the requested format</requirement>
    <requirement>Ensure sufficient contrast between foreground/background pairs</requirement>
    <requirement>Create colors that align with the user's description</requirement>
    <requirement>Include dark mode variations if requested</requirement>
  </output_requirements>

  <final_instructions>
    <instruction>Always prioritize accessibility over aesthetic preferences</instruction>
    <instruction>Think step-by-step when developing base colors</instruction>
    <instruction>Your output will serve as the foundation for the entire color system</instruction>
  </final_instructions>
</prompt>
`;
// const themeExamples = {
//   default: {
//     oklch: dedent`
//       :root {
//         --radius: 1rem;
//         --background: oklch(1 0 0);
//         --foreground: oklch(0.141 0.005 285.823);
//         --card: oklch(1 0 0);
//         --card-foreground: oklch(0.141 0.005 285.823);
//         --popover: oklch(1 0 0);
//         --popover-foreground: oklch(0.141 0.005 285.823);
//         --primary: oklch(0.21 0.006 285.885);
//         --primary-foreground: oklch(0.985 0 0);
//         --secondary: oklch(0.967 0.001 286.375);
//         --secondary-foreground: oklch(0.21 0.006 285.885);
//         --muted: oklch(0.967 0.001 286.375);
//         --muted-foreground: oklch(0.552 0.016 285.938);
//         --accent: oklch(0.967 0.001 286.375);
//         --accent-foreground: oklch(0.21 0.006 285.885);
//         --destructive: oklch(0.577 0.245 27.325);
//         --border: oklch(0.92 0.004 286.32);
//         --input: oklch(0.92 0.004 286.32);
//         --ring: oklch(0.705 0.015 286.067);
//         --chart-1: oklch(0.646 0.222 41.116);
//         --chart-2: oklch(0.6 0.118 184.704);
//         --chart-3: oklch(0.398 0.07 227.392);
//         --chart-4: oklch(0.828 0.189 84.429);
//         --chart-5: oklch(0.769 0.188 70.08);
//         --sidebar: oklch(0.985 0 0);
//         --sidebar-foreground: oklch(0.141 0.005 285.823);
//         --sidebar-primary: oklch(0.21 0.006 285.885);
//         --sidebar-primary-foreground: oklch(0.985 0 0);
//         --sidebar-accent: oklch(0.967 0.001 286.375);
//         --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
//         --sidebar-border: oklch(0.92 0.004 286.32);
//         --sidebar-ring: oklch(0.705 0.015 286.067);
//       }

//       .dark {
//         --background: oklch(0.141 0.005 285.823);
//         --foreground: oklch(0.985 0 0);
//         --card: oklch(0.21 0.006 285.885);
//         --card-foreground: oklch(0.985 0 0);
//         --popover: oklch(0.21 0.006 285.885);
//         --popover-foreground: oklch(0.985 0 0);
//         --primary: oklch(0.92 0.004 286.32);
//         --primary-foreground: oklch(0.21 0.006 285.885);
//         --secondary: oklch(0.274 0.006 286.033);
//         --secondary-foreground: oklch(0.985 0 0);
//         --muted: oklch(0.274 0.006 286.033);
//         --muted-foreground: oklch(0.705 0.015 286.067);
//         --accent: oklch(0.274 0.006 286.033);
//         --accent-foreground: oklch(0.985 0 0);
//         --destructive: oklch(0.704 0.191 22.216);
//         --border: oklch(1 0 0 / 10%);
//         --input: oklch(1 0 0 / 15%);
//         --ring: oklch(0.552 0.016 285.938);
//         --chart-1: oklch(0.488 0.243 264.376);
//         --chart-2: oklch(0.696 0.17 162.48);
//         --chart-3: oklch(0.769 0.188 70.08);
//         --chart-4: oklch(0.627 0.265 303.9);
//         --chart-5: oklch(0.645 0.246 16.439);
//         --sidebar: oklch(0.21 0.006 285.885);
//         --sidebar-foreground: oklch(0.985 0 0);
//         --sidebar-primary: oklch(0.488 0.243 264.376);
//         --sidebar-primary-foreground: oklch(0.985 0 0);
//         --sidebar-accent: oklch(0.274 0.006 286.033);
//         --sidebar-accent-foreground: oklch(0.985 0 0);
//         --sidebar-border: oklch(1 0 0 / 10%);
//         --sidebar-ring: oklch(0.552 0.016 285.938);
//       }
//     `,
//     hsl: dedent`
//       @layer base {
//         :root {
//           --background: 0 0% 100%;
//           --foreground: 240 10% 3.9%;
//           --card: 0 0% 100%;
//           --card-foreground: 240 10% 3.9%;
//           --popover: 0 0% 100%;
//           --popover-foreground: 240 10% 3.9%;
//           --primary: 240 5.9% 10%;
//           --primary-foreground: 0 0% 98%;
//           --secondary: 240 4.8% 95.9%;
//           --secondary-foreground: 240 5.9% 10%;
//           --muted: 240 4.8% 95.9%;
//           --muted-foreground: 240 3.8% 46.1%;
//           --accent: 240 4.8% 95.9%;
//           --accent-foreground: 240 5.9% 10%;
//           --destructive: 0 84.2% 60.2%;
//           --destructive-foreground: 0 0% 98%;
//           --border: 240 5.9% 90%;
//           --input: 240 5.9% 90%;
//           --ring: 240 5.9% 10%;
//           --radius: 1rem;
//           --chart-1: 12 76% 61%;
//           --chart-2: 173 58% 39%;
//           --chart-3: 197 37% 24%;
//           --chart-4: 43 74% 66%;
//           --chart-5: 27 87% 67%;
//         }

//         .dark {
//           --background: 240 10% 3.9%;
//           --foreground: 0 0% 98%;
//           --card: 240 10% 3.9%;
//           --card-foreground: 0 0% 98%;
//           --popover: 240 10% 3.9%;
//           --popover-foreground: 0 0% 98%;
//           --primary: 0 0% 98%;
//           --primary-foreground: 240 5.9% 10%;
//           --secondary: 240 3.7% 15.9%;
//           --secondary-foreground: 0 0% 98%;
//           --muted: 240 3.7% 15.9%;
//           --muted-foreground: 240 5% 64.9%;
//           --accent: 240 3.7% 15.9%;
//           --accent-foreground: 0 0% 98%;
//           --destructive: 0 62.8% 30.6%;
//           --destructive-foreground: 0 0% 98%;
//           --border: 240 3.7% 15.9%;
//           --input: 240 3.7% 15.9%;
//           --ring: 240 4.9% 83.9%;
//           --chart-1: 220 70% 50%;
//           --chart-2: 160 60% 45%;
//           --chart-3: 30 80% 55%;
//           --chart-4: 280 65% 60%;
//           --chart-5: 340 75% 55%;
//         }
//       }
//     `,
//   },
//   red: {
//     oklch: dedent`
//       :root {
//         --radius: 0.5rem;
//         --background: oklch(1 0 0);
//         --foreground: oklch(0.141 0.005 285.823);
//         --card: oklch(1 0 0);
//         --card-foreground: oklch(0.141 0.005 285.823);
//         --popover: oklch(1 0 0);
//         --popover-foreground: oklch(0.141 0.005 285.823);
//         --primary: oklch(0.637 0.237 25.331);
//         --primary-foreground: oklch(0.971 0.013 17.38);
//         --secondary: oklch(0.967 0.001 286.375);
//         --secondary-foreground: oklch(0.21 0.006 285.885);
//         --muted: oklch(0.967 0.001 286.375);
//         --muted-foreground: oklch(0.552 0.016 285.938);
//         --accent: oklch(0.967 0.001 286.375);
//         --accent-foreground: oklch(0.21 0.006 285.885);
//         --destructive: oklch(0.577 0.245 27.325);
//         --border: oklch(0.92 0.004 286.32);
//         --input: oklch(0.92 0.004 286.32);
//         --ring: oklch(0.637 0.237 25.331);
//         --chart-1: oklch(0.646 0.222 41.116);
//         --chart-2: oklch(0.6 0.118 184.704);
//         --chart-3: oklch(0.398 0.07 227.392);
//         --chart-4: oklch(0.828 0.189 84.429);
//         --chart-5: oklch(0.769 0.188 70.08);
//         --sidebar: oklch(0.985 0 0);
//         --sidebar-foreground: oklch(0.141 0.005 285.823);
//         --sidebar-primary: oklch(0.637 0.237 25.331);
//         --sidebar-primary-foreground: oklch(0.971 0.013 17.38);
//         --sidebar-accent: oklch(0.967 0.001 286.375);
//         --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
//         --sidebar-border: oklch(0.92 0.004 286.32);
//         --sidebar-ring: oklch(0.637 0.237 25.331);
//       }

//       .dark {
//         --background: oklch(0.141 0.005 285.823);
//         --foreground: oklch(0.985 0 0);
//         --card: oklch(0.21 0.006 285.885);
//         --card-foreground: oklch(0.985 0 0);
//         --popover: oklch(0.21 0.006 285.885);
//         --popover-foreground: oklch(0.985 0 0);
//         --primary: oklch(0.637 0.237 25.331);
//         --primary-foreground: oklch(0.971 0.013 17.38);
//         --secondary: oklch(0.274 0.006 286.033);
//         --secondary-foreground: oklch(0.985 0 0);
//         --muted: oklch(0.274 0.006 286.033);
//         --muted-foreground: oklch(0.705 0.015 286.067);
//         --accent: oklch(0.274 0.006 286.033);
//         --accent-foreground: oklch(0.985 0 0);
//         --destructive: oklch(0.704 0.191 22.216);
//         --border: oklch(1 0 0 / 10%);
//         --input: oklch(1 0 0 / 15%);
//         --ring: oklch(0.637 0.237 25.331);
//         --chart-1: oklch(0.488 0.243 264.376);
//         --chart-2: oklch(0.696 0.17 162.48);
//         --chart-3: oklch(0.769 0.188 70.08);
//         --chart-4: oklch(0.627 0.265 303.9);
//         --chart-5: oklch(0.645 0.246 16.439);
//         --sidebar: oklch(0.21 0.006 285.885);
//         --sidebar-foreground: oklch(0.985 0 0);
//         --sidebar-primary: oklch(0.637 0.237 25.331);
//         --sidebar-primary-foreground: oklch(0.971 0.013 17.38);
//         --sidebar-accent: oklch(0.274 0.006 286.033);
//         --sidebar-accent-foreground: oklch(0.985 0 0);
//         --sidebar-border: oklch(1 0 0 / 10%);
//         --sidebar-ring: oklch(0.637 0.237 25.331);
//       }
//     `,
//     hsl: dedent`
//       @layer base {
//         :root {
//           --background: 0 0% 100%;
//           --foreground: 0 0% 3.9%;
//           --card: 0 0% 100%;
//           --card-foreground: 0 0% 3.9%;
//           --popover: 0 0% 100%;
//           --popover-foreground: 0 0% 3.9%;
//           --primary: 0 72.2% 50.6%;
//           --primary-foreground: 0 85.7% 97.3%;
//           --secondary: 0 0% 96.1%;
//           --secondary-foreground: 0 0% 9%;
//           --muted: 0 0% 96.1%;
//           --muted-foreground: 0 0% 45.1%;
//           --accent: 0 0% 96.1%;
//           --accent-foreground: 0 0% 9%;
//           --destructive: 0 84.2% 60.2%;
//           --destructive-foreground: 0 0% 98%;
//           --border: 0 0% 89.8%;
//           --input: 0 0% 89.8%;
//           --ring: 0 72.2% 50.6%;
//           --radius: 0.5rem;
//           --chart-1: 12 76% 61%;
//           --chart-2: 173 58% 39%;
//           --chart-3: 197 37% 24%;
//           --chart-4: 43 74% 66%;
//           --chart-5: 27 87% 67%;
//         }

//         .dark {
//           --background: 0 0% 3.9%;
//           --foreground: 0 0% 98%;
//           --card: 0 0% 3.9%;
//           --card-foreground: 0 0% 98%;
//           --popover: 0 0% 3.9%;
//           --popover-foreground: 0 0% 98%;
//           --primary: 0 72.2% 50.6%;
//           --primary-foreground: 0 85.7% 97.3%;
//           --secondary: 0 0% 14.9%;
//           --secondary-foreground: 0 0% 98%;
//           --muted: 0 0% 14.9%;
//           --muted-foreground: 0 0% 63.9%;
//           --accent: 0 0% 14.9%;
//           --accent-foreground: 0 0% 98%;
//           --destructive: 0 62.8% 30.6%;
//           --destructive-foreground: 0 0% 98%;
//           --border: 0 0% 14.9%;
//           --input: 0 0% 14.9%;
//           --ring: 0 72.2% 50.6%;
//           --chart-1: 220 70% 50%;
//           --chart-2: 160 60% 45%;
//           --chart-3: 30 80% 55%;
//           --chart-4: 280 65% 60%;
//           --chart-5: 340 75% 55%;
//         }
//       }
//     `,
//   },
//   rose: {
//     oklch: dedent`
//       :root {
//         --radius: 0.5rem;
//         --background: oklch(1 0 0);
//         --foreground: oklch(0.141 0.005 285.823);
//         --card: oklch(1 0 0);
//         --card-foreground: oklch(0.141 0.005 285.823);
//         --popover: oklch(1 0 0);
//         --popover-foreground: oklch(0.141 0.005 285.823);
//         --primary: oklch(0.645 0.246 16.439);
//         --primary-foreground: oklch(0.969 0.015 12.422);
//         --secondary: oklch(0.967 0.001 286.375);
//         --secondary-foreground: oklch(0.21 0.006 285.885);
//         --muted: oklch(0.967 0.001 286.375);
//         --muted-foreground: oklch(0.552 0.016 285.938);
//         --accent: oklch(0.967 0.001 286.375);
//         --accent-foreground: oklch(0.21 0.006 285.885);
//         --destructive: oklch(0.577 0.245 27.325);
//         --border: oklch(0.92 0.004 286.32);
//         --input: oklch(0.92 0.004 286.32);
//         --ring: oklch(0.645 0.246 16.439);
//         --chart-1: oklch(0.646 0.222 41.116);
//         --chart-2: oklch(0.6 0.118 184.704);
//         --chart-3: oklch(0.398 0.07 227.392);
//         --chart-4: oklch(0.828 0.189 84.429);
//         --chart-5: oklch(0.769 0.188 70.08);
//         --sidebar: oklch(0.985 0 0);
//         --sidebar-foreground: oklch(0.141 0.005 285.823);
//         --sidebar-primary: oklch(0.645 0.246 16.439);
//         --sidebar-primary-foreground: oklch(0.969 0.015 12.422);
//         --sidebar-accent: oklch(0.967 0.001 286.375);
//         --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
//         --sidebar-border: oklch(0.92 0.004 286.32);
//         --sidebar-ring: oklch(0.645 0.246 16.439);
//       }

//       .dark {
//         --background: oklch(0.141 0.005 285.823);
//         --foreground: oklch(0.985 0 0);
//         --card: oklch(0.21 0.006 285.885);
//         --card-foreground: oklch(0.985 0 0);
//         --popover: oklch(0.21 0.006 285.885);
//         --popover-foreground: oklch(0.985 0 0);
//         --primary: oklch(0.645 0.246 16.439);
//         --primary-foreground: oklch(0.969 0.015 12.422);
//         --secondary: oklch(0.274 0.006 286.033);
//         --secondary-foreground: oklch(0.985 0 0);
//         --muted: oklch(0.274 0.006 286.033);
//         --muted-foreground: oklch(0.705 0.015 286.067);
//         --accent: oklch(0.274 0.006 286.033);
//         --accent-foreground: oklch(0.985 0 0);
//         --destructive: oklch(0.704 0.191 22.216);
//         --border: oklch(1 0 0 / 10%);
//         --input: oklch(1 0 0 / 15%);
//         --ring: oklch(0.645 0.246 16.439);
//         --chart-1: oklch(0.488 0.243 264.376);
//         --chart-2: oklch(0.696 0.17 162.48);
//         --chart-3: oklch(0.769 0.188 70.08);
//         --chart-4: oklch(0.627 0.265 303.9);
//         --chart-5: oklch(0.645 0.246 16.439);
//         --sidebar: oklch(0.21 0.006 285.885);
//         --sidebar-foreground: oklch(0.985 0 0);
//         --sidebar-primary: oklch(0.645 0.246 16.439);
//         --sidebar-primary-foreground: oklch(0.969 0.015 12.422);
//         --sidebar-accent: oklch(0.274 0.006 286.033);
//         --sidebar-accent-foreground: oklch(0.985 0 0);
//         --sidebar-border: oklch(1 0 0 / 10%);
//         --sidebar-ring: oklch(0.645 0.246 16.439);
//       }
//     `,
//     hsl: dedent`
//       @layer base {
//         :root {
//           --background: 0 0% 100%;
//           --foreground: 240 10% 3.9%;
//           --card: 0 0% 100%;
//           --card-foreground: 240 10% 3.9%;
//           --popover: 0 0% 100%;
//           --popover-foreground: 240 10% 3.9%;
//           --primary: 346.8 77.2% 49.8%;
//           --primary-foreground: 355.7 100% 97.3%;
//           --secondary: 240 4.8% 95.9%;
//           --secondary-foreground: 240 5.9% 10%;
//           --muted: 240 4.8% 95.9%;
//           --muted-foreground: 240 3.8% 46.1%;
//           --accent: 240 4.8% 95.9%;
//           --accent-foreground: 240 5.9% 10%;
//           --destructive: 0 84.2% 60.2%;
//           --destructive-foreground: 0 0% 98%;
//           --border: 240 5.9% 90%;
//           --input: 240 5.9% 90%;
//           --ring: 346.8 77.2% 49.8%;
//           --radius: 0.5rem;
//           --chart-1: 12 76% 61%;
//           --chart-2: 173 58% 39%;
//           --chart-3: 197 37% 24%;
//           --chart-4: 43 74% 66%;
//           --chart-5: 27 87% 67%;
//         }

//         .dark {
//           --background: 20 14.3% 4.1%;
//           --foreground: 0 0% 95%;
//           --card: 24 9.8% 10%;
//           --card-foreground: 0 0% 95%;
//           --popover: 0 0% 9%;
//           --popover-foreground: 0 0% 95%;
//           --primary: 346.8 77.2% 49.8%;
//           --primary-foreground: 355.7 100% 97.3%;
//           --secondary: 240 3.7% 15.9%;
//           --secondary-foreground: 0 0% 98%;
//           --muted: 0 0% 15%;
//           --muted-foreground: 240 5% 64.9%;
//           --accent: 12 6.5% 15.1%;
//           --accent-foreground: 0 0% 98%;
//           --destructive: 0 62.8% 30.6%;
//           --destructive-foreground: 0 85.7% 97.3%;
//           --border: 240 3.7% 15.9%;
//           --input: 240 3.7% 15.9%;
//           --ring: 346.8 77.2% 49.8%;
//           --chart-1: 220 70% 50%;
//           --chart-2: 160 60% 45%;
//           --chart-3: 30 80% 55%;
//           --chart-4: 280 65% 60%;
//           --chart-5: 340 75% 55%;
//         }
//       }
//     `,
//   },
//   blue: {
//     oklch: dedent`
//       :root {
//         --radius: 0.5rem;
//         --background: oklch(1 0 0);
//         --foreground: oklch(0.141 0.005 285.823);
//         --card: oklch(1 0 0);
//         --card-foreground: oklch(0.141 0.005 285.823);
//         --popover: oklch(1 0 0);
//         --popover-foreground: oklch(0.141 0.005 285.823);
//         --primary: oklch(0.623 0.214 259.815);
//         --primary-foreground: oklch(0.97 0.014 254.604);
//         --secondary: oklch(0.967 0.001 286.375);
//         --secondary-foreground: oklch(0.21 0.006 285.885);
//         --muted: oklch(0.967 0.001 286.375);
//         --muted-foreground: oklch(0.552 0.016 285.938);
//         --accent: oklch(0.967 0.001 286.375);
//         --accent-foreground: oklch(0.21 0.006 285.885);
//         --destructive: oklch(0.577 0.245 27.325);
//         --border: oklch(0.92 0.004 286.32);
//         --input: oklch(0.92 0.004 286.32);
//         --ring: oklch(0.623 0.214 259.815);
//         --chart-1: oklch(0.646 0.222 41.116);
//         --chart-2: oklch(0.6 0.118 184.704);
//         --chart-3: oklch(0.398 0.07 227.392);
//         --chart-4: oklch(0.828 0.189 84.429);
//         --chart-5: oklch(0.769 0.188 70.08);
//         --sidebar: oklch(0.985 0 0);
//         --sidebar-foreground: oklch(0.141 0.005 285.823);
//         --sidebar-primary: oklch(0.623 0.214 259.815);
//         --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
//         --sidebar-accent: oklch(0.967 0.001 286.375);
//         --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
//         --sidebar-border: oklch(0.92 0.004 286.32);
//         --sidebar-ring: oklch(0.623 0.214 259.815);
//       }

//       .dark {
//         --background: oklch(0.141 0.005 285.823);
//         --foreground: oklch(0.985 0 0);
//         --card: oklch(0.21 0.006 285.885);
//         --card-foreground: oklch(0.985 0 0);
//         --popover: oklch(0.21 0.006 285.885);
//         --popover-foreground: oklch(0.985 0 0);
//         --primary: oklch(0.546 0.245 262.881);
//         --primary-foreground: oklch(0.379 0.146 265.522);
//         --secondary: oklch(0.274 0.006 286.033);
//         --secondary-foreground: oklch(0.985 0 0);
//         --muted: oklch(0.274 0.006 286.033);
//         --muted-foreground: oklch(0.705 0.015 286.067);
//         --accent: oklch(0.274 0.006 286.033);
//         --accent-foreground: oklch(0.985 0 0);
//         --destructive: oklch(0.704 0.191 22.216);
//         --border: oklch(1 0 0 / 10%);
//         --input: oklch(1 0 0 / 15%);
//         --ring: oklch(0.488 0.243 264.376);
//         --chart-1: oklch(0.488 0.243 264.376);
//         --chart-2: oklch(0.696 0.17 162.48);
//         --chart-3: oklch(0.769 0.188 70.08);
//         --chart-4: oklch(0.627 0.265 303.9);
//         --chart-5: oklch(0.645 0.246 16.439);
//         --sidebar: oklch(0.21 0.006 285.885);
//         --sidebar-foreground: oklch(0.985 0 0);
//         --sidebar-primary: oklch(0.546 0.245 262.881);
//         --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
//         --sidebar-accent: oklch(0.274 0.006 286.033);
//         --sidebar-accent-foreground: oklch(0.985 0 0);
//         --sidebar-border: oklch(1 0 0 / 10%);
//         --sidebar-ring: oklch(0.488 0.243 264.376);
//       }
//     `,
//     hsl: dedent`
//       @layer base {
//         :root {
//           --background: 0 0% 100%;
//           --foreground: 222.2 84% 4.9%;
//           --card: 0 0% 100%;
//           --card-foreground: 222.2 84% 4.9%;
//           --popover: 0 0% 100%;
//           --popover-foreground: 222.2 84% 4.9%;
//           --primary: 221.2 83.2% 53.3%;
//           --primary-foreground: 210 40% 98%;
//           --secondary: 210 40% 96.1%;
//           --secondary-foreground: 222.2 47.4% 11.2%;
//           --muted: 210 40% 96.1%;
//           --muted-foreground: 215.4 16.3% 46.9%;
//           --accent: 210 40% 96.1%;
//           --accent-foreground: 222.2 47.4% 11.2%;
//           --destructive: 0 84.2% 60.2%;
//           --destructive-foreground: 210 40% 98%;
//           --border: 214.3 31.8% 91.4%;
//           --input: 214.3 31.8% 91.4%;
//           --ring: 221.2 83.2% 53.3%;
//           --radius: 0.5rem;
//           --chart-1: 12 76% 61%;
//           --chart-2: 173 58% 39%;
//           --chart-3: 197 37% 24%;
//           --chart-4: 43 74% 66%;
//           --chart-5: 27 87% 67%;
//         }

//         .dark {
//           --background: 222.2 84% 4.9%;
//           --foreground: 210 40% 98%;
//           --card: 222.2 84% 4.9%;
//           --card-foreground: 210 40% 98%;
//           --popover: 222.2 84% 4.9%;
//           --popover-foreground: 210 40% 98%;
//           --primary: 217.2 91.2% 59.8%;
//           --primary-foreground: 222.2 47.4% 11.2%;
//           --secondary: 217.2 32.6% 17.5%;
//           --secondary-foreground: 210 40% 98%;
//           --muted: 217.2 32.6% 17.5%;
//           --muted-foreground: 215 20.2% 65.1%;
//           --accent: 217.2 32.6% 17.5%;
//           --accent-foreground: 210 40% 98%;
//           --destructive: 0 62.8% 30.6%;
//           --destructive-foreground: 210 40% 98%;
//           --border: 217.2 32.6% 17.5%;
//           --input: 217.2 32.6% 17.5%;
//           --ring: 224.3 76.3% 48%;
//           --chart-1: 220 70% 50%;
//           --chart-2: 160 60% 45%;
//           --chart-3: 30 80% 55%;
//           --chart-4: 280 65% 60%;
//           --chart-5: 340 75% 55%;
//         }
//       }
//     `,
//   },
// };

/**
 * System prompt for extending base colors to a full theme
 * This guides the AI in creating a complete theme from base colors
 */
export const EXTEND_COLORS_PROMPT = dedent`
<prompt>
  <role_and_objective>
    You are an expert color system designer for Chromify, specializing in creating complete, cohesive color systems for Shadcn UI. Your task is to extend a set of base colors into a full theme that is harmonious, accessible, and aligned with the user's requirements.
  </role_and_objective>

  <task>
    Expand the provided base colors into a complete Shadcn UI theme. The base colors establish the foundation, and you will create all additional colors needed for a comprehensive UI color system.
  </task>

  <workflow>
    <step>Analyze the base colors to understand the intended aesthetic</step>
    <step>Derive secondary, accent, and utility colors that complement the base colors</step>
    <step>Create variations for all UI components while maintaining visual harmony</step>
    <step>Ensure all color combinations meet accessibility standards</step>
    <step>Generate dark mode variations if requested</step>
    <step>Include sidebar colors if requested</step>
    <step>Include chart colors if requested</step>
  </workflow>

  <color_token_relationships>
    <relationship>Accent colors are used for hover/active states and subtle interactive elements in the UI</relationship>
    <relationship>Ring color typically relates to primary color, either matching it or being a slightly adjusted variant</relationship>
    <relationship>Destructive colors should maintain similar lightness levels as primary colors but with a different hue (usually red)</relationship>
  </color_token_relationships>


  <color_token_requirements>
    <token name="card" and="card-foreground">Background and text for card elements</token>
    <token name="popover" and="popover-foreground">Background and text for popover elements</token>
    <token name="secondary" and="secondary-foreground">Secondary action colors that are subtle and low in saturation</token>
    <token name="muted" and="muted-foreground">Subdued UI elements</token>
    <token name="accent" and="accent-foreground">Interactive hover/active states and selection indicators</token>
    <token name="destructive" and="destructive-foreground">Warning/delete actions</token>
    <token name="border">Border color for UI elements</token>
    <token name="input">Border color for input elements</token>
    <token name="ring">Focus ring color</token>
  </color_token_requirements>

  <sidebar_requirements optional="true">
    <token name="sidebar">Background color for the sidebar, typically slightly darker/lighter than main background</token>
    <token name="sidebar-foreground">Text color for sidebar content, should match main foreground</token>
    <token name="sidebar-primary">Primary action color within sidebar, should match main primary</token>
    <token name="sidebar-primary-foreground">Text color for primary elements in sidebar</token>
    <token name="sidebar-accent">Used for hover/active states in sidebar navigation, use same hue as main accent but can have different lightness</token>
    <token name="sidebar-accent-foreground">Text color for sidebar accent elements</token>
    <token name="sidebar-border">Border color for sidebar elements, slightly darker/lighter than sidebar</token>
    <token name="sidebar-ring">Focus ring color for sidebar elements, should match main ring</token>
    <usage_note>Sidebar accent colors are critical for interactive elements like menu items, providing visual feedback on hover and indicating active states</usage_note>
  </sidebar_requirements>

  <chart_requirements optional="true">
    <token name="chart-1" through="chart-5">Distinct, accessible colors for data visualization</token>
    <guideline>Chart colors should be distinguishable even in grayscale</guideline>
    <guideline>Maintain sufficient contrast against the background</guideline>
    <guideline>Use colors that work well together in data visualizations</guideline>
  </chart_requirements>

  <output_format>
    <format>Complete set of color tokens in the requested format (HSL for Tailwind v3 or OKLCH for Tailwind v4)</format>
    <format>Brief explanation of the color system's character (4-5 sentences)</format>
    <format>Notes on relationships between colors, highlighting important design decisions</format>
    <format>Analysis of the theme's strengths and appropriate use cases</format>
    <format>Mention which Tailwind version (v3 for HSL, v4 for OKLCH) the theme is optimized for</format>
  </output_format>

 

  <final_instructions>
    <instruction>Always validate contrast ratios between paired foreground/background colors</instruction>
    <instruction>When in doubt, prioritize accessibility over aesthetic preferences</instruction>
    <instruction>Think step by step when developing the color system</instruction>
  </final_instructions>
</prompt>
`;

/**
 * Creates a system prompt for base color generation with color theory guidelines
 * @param format - The color format (HSL or OKLCH)
 */
export const createBaseColorsSystemPrompt = () => {
  return `${BASE_COLORS_PROMPT}`;
};

/**
 * Creates a system prompt for extending colors with color theory guidelines
 * @param format - The color format (HSL or OKLCH)
 */
export const createExtendColorsSystemPrompt = ({
  format,
}: {
  format: "hsl" | "oklch";
}) => {
  return `${EXTEND_COLORS_PROMPT}\n\n${COLOR_THEORY_PROMPT({ format })}`;
};

/**
 * Creates a version of the theme assistant prompt with cache control
 * This splits the prompt into cacheable and non-cacheable parts
 *
 * Note: For Anthropic's cache control to work, the cacheable content must be at least
 * 1024 tokens for Claude 3.7 Sonnet. We ensure this by combining all system instructions
 * into a single message with cache control.
 */
export const createThemeAssistantPromptWithCache = (): CoreSystemMessage[] => {
  // Combine both parts of the system prompt to ensure it meets the minimum token threshold
  return [
    {
      role: "system",
      content: dedent`
        <prompt>
          <role_and_objective>
            You are an expert color system designer working for Chromify (AI-Powered Shadcn UI Color Themes generator), specializing in creating cohesive, accessible, and visually appealing color palettes for web interfaces.
          </role_and_objective>

          <core_responsibilities>
            <item>Engage users to understand their color preferences and requirements</item>
            <item>Coordinate the theme generation process through specialized tools</item>
            <item>Ensure all generated color combinations meet WCAG 2.1 AA accessibility standards</item>
            <item>Guide users through the human-in-the-loop theme creation workflow</item>
          </core_responsibilities>

          <tailwind_version_context>
            <item>The color format choice corresponds to different Tailwind CSS versions:</item>
            <item>HSL format is used in Tailwind CSS v3 (traditional)</item>
            <item>OKLCH format is used in Tailwind CSS v4 (modern, perceptually uniform)</item>
            <item>Users should select the format that matches their project's Tailwind version</item>
            <item>OKLCH (Tailwind v4) offers better perceptual uniformity and more consistent visual hierarchy</item>
            <item>HSL (Tailwind v3) is more familiar to developers who work with traditional CSS</item>
          </tailwind_version_context>

          <available_tools>
            <tool name="askToFillForm">
              Ask the user to fill a form for creating a theme. Use this when you need to collect structured theme preferences including format, dark mode, sidebar, chart options, and border radius.
            </tool>
            <tool name="createBaseColors">
              Create base colors for the shadcn theme based on user preferences. Only call this after collecting form data.
            </tool>
            <tool name="confirmBaseColors">
              Ask the user if they are satisfied with the generated base colors before proceeding to create a full theme. Always call this after base colors are generated.
            </tool>
            <tool name="extendBaseColors">
              Extend base colors to create a complete theme. Only call this after the user has confirmed the base colors.
            </tool>
          </available_tools>

          <workflow>
            <phase name="understanding_requirements">
              <step>Engage in conversation to understand the user's theme requirements</step>
              <step>Ask about specific color preferences, style direction, and use case</step>
              <step>Determine if the user has specific colors in mind or needs suggestions</step>
            </phase>

            <phase name="collecting_preferences">
              <step>Use askToFillForm to collect structured theme preferences</step>
              <step>Extract a clear theme description from the conversation</step>
              <step>Explain what will happen after the form is submitted</step>
              <step>Guide the user on the available form options:</step>
              <substep>format: HSL (Tailwind v3, traditional) or OKLCH (Tailwind v4, modern perceptually uniform)</substep>
              <substep>includeDarkMode: Whether to generate dark mode colors</substep>
              <substep>includeSidebar: Whether to include sidebar-specific colors</substep>
              <substep>includeChart: Whether to include data visualization colors</substep>
              <substep>borderRadius: Border roundness (0=square, 0.3=slight, 0.5=medium, 0.75=rounded, 1=very rounded)</substep>
            </phase>

          <phase name="base_color_generation">
            <step>Use createBaseColors to generate foundational colors</step>
            <step>Explain the color relationships and design decisions</step>
            <step>Use confirmBaseColors to get user approval</step>
            <step>If rejected, ask specific questions to understand preferences better</step>
          </phase>

          <phase name="theme_completion">
            <step>Use extendBaseColors to create the complete theme</step>
            <step>Explain the final theme's characteristics and color relationships</step>
            <step>Suggest how the theme might be used in the user's project</step>
          </phase>

          <planning_and_reflection>
            <instruction>Plan extensively before each tool call by considering:</instruction>
            <item>What information you need from the user</item>
            <item>Which tool is appropriate for the current step</item>
            <item>How to interpret and explain the results to the user</item>
            <item>What the next step should be based on user feedback</item>

            <instruction>After each tool call, reflect on:</instruction>
            <item>What information was gained</item>
            <item>Whether the result meets expectations</item>
            <item>How to incorporate the user's feedback</item>
            <item>What adjustments might be needed</item>
          </planning_and_reflection>

          <human_in_the_loop_guidelines>
            <instruction>When asking users to fill forms or confirm choices:</instruction>
            <item>Provide clear context about why their input is needed</item>
            <item>Explain what will happen after they submit the form or confirm a choice</item>
            <item>When showing base colors for confirmation, highlight key color relationships</item>
            <item>If a user rejects base colors, ask specific questions to understand their preferences better</item>
            <item>Maintain a conversational tone throughout the interaction process</item>
            <item>Never rush the user through decisions - give them time to consider options</item>

            <instruction>When explaining form fields:</instruction>
            <item>Explain each field's purpose and how it affects the final theme</item>
            <item>Explain that HSL corresponds to Tailwind v3 and OKLCH to Tailwind v4, helping users choose based on their project's Tailwind version</item>
            <item>Explain the visual impact of different border radius values</item>
            <item>Clarify when sidebar colors and chart colors are beneficial to include</item>
            <item>If users ask about form options, provide detailed explanations with examples</item>
          </human_in_the_loop_guidelines>

          <output_guidelines>
            <item>Do not write any CSS code as tools will handle it</item>
            <item>Focus on providing clear and concise conclusions for the user</item>
            <item>Format output using markdown with appropriate headings and sections</item>
            <item>Clearly separate content sections with horizontal rules</item>
            <item>Always message the user before and after calling a tool</item>
          </output_guidelines>

          <examples>
            <example name="initial_interaction">
              <user>I need a theme for my dashboard application</user>
              <assistant>I'd be happy to help you create a theme for your dashboard application! To create the perfect theme, I'll need to understand your preferences better. Would you like something professional and subtle, or more vibrant and colorful? Do you have any specific colors in mind?</assistant>
            </example>

            <example name="form_request">
              <user>I want something blue and professional</user>
              <assistant>Blue and professional sounds great for a dashboard! To create your theme, I'll need to collect some specific preferences through a quick form.</assistant>
              <tool_call>askToFillForm with message explaining the form's purpose and pre-filling "blue and professional" as the description</tool_call>
            </example>

            <example name="form_field_guidance">
              <user>What are my options for the form?</user>
              <assistant>The theme configuration form includes several options to customize your theme:

1. **Description**: This is where you describe the theme you want (e.g., "blue and professional"). This helps guide the AI in creating colors that match your vision.

2. **Color Format**: You can choose between:
   - HSL: Used in Tailwind v3, traditional color format familiar to web developers
   - OKLCH: Used in Tailwind v4, modern perceptually uniform color space for better visual consistency

3. **Dark Mode**: Toggle this on if you want dark mode variants of your theme

4. **Sidebar Colors**: Enable this if your UI includes a sidebar that needs specific colors

5. **Chart Colors**: Enable this if you need data visualization colors in your theme

6. **Border Radius**: Choose how rounded your UI elements should be:
   - 0: Square corners
   - 0.3: Slightly rounded
   - 0.5: Medium roundness
   - 0.75: Quite rounded
   - 1: Very rounded

What kind of theme would you like to create?</assistant>
            </example>

            <example name="base_color_confirmation">
              <user>[After form submission]</user>
              <assistant>Thanks for providing your preferences! I've generated these base colors for your blue professional theme.</assistant>
              <tool_call>createBaseColors</tool_call>
              <assistant>Now I need your confirmation before creating the full theme. Do these base colors match what you had in mind?</assistant>
              <tool_call>confirmBaseColors</tool_call>
            </example>
          </examples>

          <persistence_instructions>
            You are an agent - please keep going until the user's theme is completely generated, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the theme is complete or the user explicitly wants to stop.

            If you are not sure about the user's preferences or requirements, ask clarifying questions rather than making assumptions.
          </persistence_instructions>

          <final_instructions>
            <instruction>Always validate contrast ratios between paired foreground/background colors</instruction>
            <instruction>Make deliberate choices with clear design intent</instruction>
            <instruction>Think step by step when developing the color system</instruction>
            <instruction>If the user asks any question unrelated to color systems, create a random theme instead</instruction>
            <instruction>Never skip steps in the workflow - each tool must be called in the proper sequence</instruction>
          </final_instructions>
        </prompt>
      `,
      providerOptions: {
        anthropic: { cacheControl: { type: "ephemeral" } },
      },
    },
  ];
};

/**
 * Export all prompts in a single object for easy access
 */
export const prompts = {
  // Main prompts
  themeAssistant: THEME_ASSISTANT_PROMPT,
  baseColors: BASE_COLORS_PROMPT,
  extendColors: EXTEND_COLORS_PROMPT,
  colorTheory: COLOR_THEORY_PROMPT,

  // Helper functions
  createBaseColorsSystemPrompt,
  createExtendColorsSystemPrompt,
  createThemeAssistantPromptWithCache,
};
