import { createAnthropic } from "@ai-sdk/anthropic";
import { createDeepSeek } from "@ai-sdk/deepseek";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { createOpenAI } from "@ai-sdk/openai";
import { LanguageModelV1 } from "ai";

const openaiClient = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  compatibility: "strict", // strict mode, enable when using the OpenAI API
});
const deepseekClient = createDeepSeek({
  apiKey: process.env.DEEPSEEK_API_KEY ?? "",
});

const googleClient = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_API_KEY,
  // Optional safety settings
  // safetySettings: [
  //   { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
  // ],
});

const anthropicClient = createAnthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

const CLAUDE_MODEL = "claude-3-7-sonnet-latest";
const CLAUDE_HAIKU = "claude-3-5-haiku-latest";

export const deepseek: LanguageModelV1 = deepseekClient("deepseek-chat");
export const google = googleClient("gemini-2.5-pro-preview-03-25", {
  useSearchGrounding: true,
});
export const claude: LanguageModelV1 = anthropicClient(CLAUDE_MODEL);
export const claudeHaiku: LanguageModelV1 = anthropicClient(CLAUDE_HAIKU);
export const gpt: LanguageModelV1 = openaiClient("gpt-4.1");
