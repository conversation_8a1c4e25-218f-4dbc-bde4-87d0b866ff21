/**
 * Type definitions for theme action responses
 */

/**
 * Base response type for all theme actions
 */
export interface BaseActionResponse {
  success: boolean;
  error?: string;
}

/**
 * Response for creating a theme request
 */
export interface CreateThemeResponse extends BaseActionResponse {
  id?: string;
}

/**
 * Response for toggling theme public status
 */
export interface ToggleThemePublicResponse extends BaseActionResponse {
  isPublic?: boolean;
  sharedId?: string | null;
}

/**
 * Response for toggling theme favorite status
 */
export interface ToggleThemeFavoriteResponse extends BaseActionResponse {
  isFavorite?: boolean;
}

/**
 * Response for updating theme name
 */
export interface UpdateThemeNameResponse extends BaseActionResponse {}

/**
 * Response for regenerating a theme
 */
export interface RegenerateThemeResponse extends BaseActionResponse {
  id?: string;
}

/**
 * Response for regenerating a shared ID
 */
export interface RegenerateSharedIdResponse extends BaseActionResponse {
  sharedId?: string;
}

/**
 * Response for deleting a theme
 */
export interface DeleteThemeResponse extends BaseActionResponse {}
