"use server";

import { revalidatePath } from "next/cache";
import { auth } from "@clerk/nextjs/server";

import { ThemeFormValues } from "@/schema/theme";
import { createServerSupabaseClient } from "../db/ssr-client";
import {
  CreateThemeResponse,
  DeleteThemeResponse,
  RegenerateSharedIdResponse,
  RegenerateThemeResponse,
  ToggleThemeFavoriteResponse,
  ToggleThemePublicResponse,
  UpdateThemeNameResponse,
} from "./theme-types";

// app/actions/theme.ts (continued)
/**
 * Creates a new theme request based on user input
 */
export async function createThemeRequest(
  userInput: ThemeFormValues
): Promise<CreateThemeResponse> {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Authentication required");
  }

  const client = createServerSupabaseClient();
  const description = userInput.description;
  const format = userInput.format;
  const includeDarkMode = userInput.includeDarkMode;
  const includeSidebar = userInput.includeSidebar;
  const includeChart = userInput.includeChart;

  const borderRadius = userInput.borderRadius;
  try {
    // Call the database function to create a theme request
    const { data } = await client.rpc("create_theme", {
      p_user_id: userId,
      p_user_prompt: description, // Updated parameter name
      p_format: format,
      p_include_dark_mode: includeDarkMode,
      p_include_sidebar: includeSidebar,
      p_include_chart: includeChart,
      p_image_path: null,
      p_model: "claude",
      p_border_radius: borderRadius,
    });
    return { success: true, id: data };
  } catch (error) {
    return { success: false, error: "Failed to create theme" };
  }
}

/**
 * Toggles the public status of a theme
 * @param themeId - The ID of the theme to toggle
 * @param path - The path to revalidate after toggling
 * @returns An object with success status and the new public status
 */
export async function toggleThemePublic(
  themeId: string
): Promise<ToggleThemePublicResponse> {
  const { userId } = await auth();

  if (!userId) {
    throw new Error("Authentication required");
  }

  try {
    const client = createServerSupabaseClient();

    // Call the database function to toggle the public status
    const { data: isPublic, error } = await client.rpc("toggle_theme_public", {
      p_theme_id: themeId,
    });

    if (error) {
      console.error("Error toggling theme public status:", error);
      return { success: false, error: error.message };
    }

    // If the theme is now public, fetch the shared_id
    let sharedId = null;
    if (isPublic) {
      const { data: theme } = await client
        .from("themes")
        .select("shared_id")
        .eq("id", themeId)
        .single();

      sharedId = theme?.shared_id;
    }

    // Revalidate the path if provided
    revalidatePath("/dashboard");
    revalidatePath("/theme/history");
    revalidatePath("/theme/starred");
    revalidatePath(`/theme/${themeId}`);
    return { success: true, isPublic, sharedId };
  } catch (error) {
    console.error("Error toggling theme public status:", error);
    return { success: false, error: "Failed to toggle theme public status" };
  }
}

/**
 * Toggles the favorite status of a theme
 * @param themeId - The ID of the theme to toggle
 * @param path - The path to revalidate after toggling
 * @returns An object with success status and the new favorite status
 */
export async function toggleThemeFavorite(
  themeId: string
): Promise<ToggleThemeFavoriteResponse> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();

    // Call the database function to toggle the favorite status
    const { data, error } = await client.rpc("toggle_theme_favorite", {
      p_theme_id: themeId,
    });

    if (error) {
      console.error("Error toggling theme favorite status:", error);
      return { success: false, error: error.message };
    }

    // Revalidate the path if provided
    revalidatePath("/dashboard");
    revalidatePath("/theme/history");
    revalidatePath("/theme/stars");
    revalidatePath(`/theme/${themeId}`);

    return { success: true, isFavorite: data };
  } catch (error) {
    console.error("Error toggling theme favorite status:", error);
    return { success: false, error: "Failed to toggle theme favorite status" };
  }
}

/**
 * Updates the name of a theme
 * @param themeId - The ID of the theme to update
 * @param newName - The new name for the theme
 * @param path - The path to revalidate after updating
 * @returns An object with success status
 */
export async function updateThemeName(
  themeId: string,
  newName: string,
  path?: string
): Promise<UpdateThemeNameResponse> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();

    // Check if the user owns the theme
    const { data: theme, error: themeError } = await client
      .from("themes")
      .select("id")
      .eq("id", themeId)
      .eq("user_id", userId)
      .single();

    if (themeError || !theme) {
      return { success: false, error: "Theme not found or not authorized" };
    }

    // Update the theme name
    const { error } = await client
      .from("themes")
      .update({ name: newName, updated_at: new Date().toISOString() })
      .eq("id", themeId);

    if (error) {
      console.error("Error updating theme name:", error);
      return { success: false, error: error.message };
    }

    // Revalidate the path if provided
    if (path) {
      revalidatePath(path);
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating theme name:", error);
    return { success: false, error: "Failed to update theme name" };
  }
}

/**
 * Regenerates a theme based on an existing theme request
 * @param themeId - The ID of the theme to use as a base
 * @param modifiedPrompt - Optional modified prompt to use instead of the original
 * @param options - Optional modified options
 * @param path - The path to revalidate after regeneration
 * @returns An object with success status and the new request ID
 */
export async function regenerateTheme(
  themeId: string,
  modifiedPrompt?: string,
  options?: {
    format?: "hsl" | "oklch";
    includeDarkMode?: boolean;
    includeSidebar?: boolean;
    includeChart?: boolean;
    model?: "deepseek-chat" | "anthropic" | "google";
    borderRadius?: "0" | "0.3" | "0.5" | "0.75" | "1";
  },
  path?: string
): Promise<RegenerateThemeResponse> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();
    let themeDetails;

    // First try to get the theme details directly
    const { data: themeData, error: themeError } = await client
      .from("theme_details")
      .select("*")
      .eq("id", themeId)
      .eq("user_id", userId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors

    if (themeData) {
      // We found the theme, use its details
      themeDetails = themeData;
    } else {
      // If not found as a theme ID, try as a request ID
      const { data: requestData, error: requestError } = await client
        .from("theme_requests")
        .select("*")
        .eq("id", themeId)
        .eq("user_id", userId)
        .maybeSingle();

      if (requestError || !requestData) {
        console.error(
          "Error finding theme or request:",
          themeError || requestError
        );
        return {
          success: false,
          error: "Theme or request not found or not authorized",
        };
      }

      // Use the request data directly
      themeDetails = {
        user_prompt: requestData.user_prompt,
        format: requestData.format,
        include_dark_mode: requestData.include_dark_mode,
        include_sidebar: requestData.include_sidebar,
        include_chart: requestData.include_chart,
        model: requestData.model,
        border_radius: requestData.border_radius,
      };
    }

    // Create a new theme request based on the original or modified parameters
    const { data, error } = await client.rpc("create_theme", {
      p_user_id: userId,
      p_user_prompt: modifiedPrompt || themeDetails.user_prompt,
      p_format: options?.format || themeDetails.format,
      p_include_dark_mode:
        options?.includeDarkMode ?? themeDetails.include_dark_mode,
      p_include_sidebar:
        options?.includeSidebar ?? themeDetails.include_sidebar,
      p_include_chart: options?.includeChart ?? themeDetails.include_chart,
      p_image_path: null,
      p_model: options?.model || themeDetails.model,
      p_border_radius:
        options?.borderRadius || themeDetails.border_radius || "0.5",
    });

    if (error) {
      console.error("Error regenerating theme:", error);
      return { success: false, error: error.message };
    }

    // Revalidate the path if provided
    if (path) {
      revalidatePath(path);
    }

    return { success: true, id: data };
  } catch (error) {
    console.error("Error regenerating theme:", error);
    return { success: false, error: "Failed to regenerate theme" };
  }
}

/**
 * Deletes a theme and its associated request
 * @param themeId - The ID of the theme to delete
 * @param path - The path to revalidate after deletion
 * @returns An object with success status
 */
export async function deleteTheme(
  themeId: string
): Promise<DeleteThemeResponse> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();

    // Call the database function to delete the theme
    const { error } = await client.rpc("delete_theme", {
      p_theme_id: themeId,
    });

    if (error) {
      console.error("Error deleting theme:", error);
      return { success: false, error: error.message };
    }

    revalidatePath("/theme/history");
    revalidatePath("/theme/stars");
    revalidatePath("/dashboard");

    return { success: true };
  } catch (error) {
    console.error("Error deleting theme:", error);
    return { success: false, error: "Failed to delete theme" };
  }
}

/**
 * Regenerates the shared ID for a public theme
 * @param themeId - The ID of the theme
 * @param path - The path to revalidate after regeneration
 * @returns An object with success status and the new shared ID
 */
export async function regenerateSharedId(
  themeId: string,
  path?: string
): Promise<RegenerateSharedIdResponse> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();

    // Call the database function to regenerate the shared_id
    const { data: newSharedId, error } = await client.rpc(
      "regenerate_theme_shared_id",
      {
        p_theme_id: themeId,
      }
    );

    if (error) {
      console.error("Error regenerating shared ID:", error);
      return { success: false, error: error.message };
    }

    // Revalidate the path if provided
    if (path) {
      revalidatePath(path);
    }

    return { success: true, sharedId: newSharedId };
  } catch (error) {
    console.error("Error regenerating shared ID:", error);
    return { success: false, error: "Failed to regenerate shared ID" };
  }
}

/**
 * Fetches the theme ID and metadata associated with a request ID
 * @param requestId - The ID of the theme request
 * @returns Object containing theme ID and metadata if found
 */
export async function getThemeIdByRequestId(requestId: string): Promise<{
  themeId: string | null;
  isFavorite?: boolean;
  isPublic?: boolean;
  sharedId?: string | null;
  error?: string;
}> {
  const { userId } = await auth();
  if (!userId) {
    return { themeId: null, error: "Authentication required" };
  }

  try {
    const client = createServerSupabaseClient();

    // Query the themes table for the theme associated with this request
    // Don't use .single() to avoid the "no rows returned" error
    const { data, error } = await client
      .from("themes")
      .select("id, is_favorite, is_public, shared_id")
      .eq("request_id", requestId);

    if (error) {
      console.error("Error fetching theme ID:", error);
      return { themeId: null, error: error.message };
    }

    // If no data or empty array, return null without an error
    if (!data || data.length === 0) {
      return { themeId: null };
    }

    // Use the first result if multiple are returned (should be only one)
    const themeData = data[0];
    revalidatePath(`/theme/${themeData?.id}`);
    return {
      themeId: themeData?.id,
      isFavorite: themeData?.is_favorite,
      isPublic: themeData?.is_public,
      sharedId: themeData?.shared_id,
    };
  } catch (error) {
    console.error("Error fetching theme ID:", error);
    return { themeId: null, error: "Failed to fetch theme ID" };
  }
}
