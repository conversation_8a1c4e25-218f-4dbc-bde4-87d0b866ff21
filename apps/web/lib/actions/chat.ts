"use server";

import { revalidatePath } from "next/cache";
import { Message } from "ai";

import {
  createChatSession,
  deleteChatSession,
  linkChatToTheme,
  saveChatMessage,
  toggleChatPinned,
  updateChatTitle,
} from "@/services/chat";

/**
 * Creates a new chat session
 * @param title Optional title for the chat session
 * @returns The ID of the newly created chat session
 */
export async function createChat(title?: string): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  try {
    const sessionId = await createChatSession(title);

    return { success: true, data: sessionId };
  } catch (error) {
    console.error("Error creating chat:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create chat",
    };
  }
}

/**
 * Saves a chat message
 * @param sessionId The ID of the chat session
 * @param message The message to save
 * @returns The ID of the saved message
 */
export async function saveMessage(
  sessionId: string,
  message: Message
): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  try {
    const messageId = await saveChatMessage(sessionId, message);
    return { success: true, data: messageId };
  } catch (error) {
    console.error("Error saving message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save message",
    };
  }
}

/**
 * Updates the title of a chat session
 * @param sessionId The ID of the chat session
 * @param title The new title
 * @returns Success status
 */
export async function updateChatSessionTitle(
  sessionId: string,
  title: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await updateChatTitle(sessionId, title);
    revalidatePath(`/chat/${sessionId}`);
    revalidatePath("/chat");
    revalidatePath("/chats");
    return { success: true };
  } catch (error) {
    console.error("Error updating chat title:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update chat title",
    };
  }
}

/**
 * Toggles the pinned status of a chat session
 * @param sessionId The ID of the chat session
 * @returns The new pinned status
 */
export async function toggleChatSessionPinned(sessionId: string): Promise<{
  success: boolean;
  data?: boolean;
  error?: string;
}> {
  try {
    const isPinned = await toggleChatPinned(sessionId);
    revalidatePath(`/chat/${sessionId}`);
    revalidatePath("/chat");
    revalidatePath("/chats");
    return { success: true, data: isPinned };
  } catch (error) {
    console.error("Error toggling chat pinned status:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to toggle chat pinned status",
    };
  }
}

/**
 * Deletes a chat session
 * @param sessionId The ID of the chat session
 * @returns Success status
 */
export async function deleteChatSessionAction(sessionId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await deleteChatSession(sessionId);
    revalidatePath("/chat");
    revalidatePath("/chats");
    return { success: true };
  } catch (error) {
    console.error("Error deleting chat session:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete chat session",
    };
  }
}

/**
 * Deletes multiple chat sessions
 * @param sessionIds Array of chat session IDs to delete
 * @returns Success status
 */
export async function deleteMultipleChatSessionsAction(
  sessionIds: string[]
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Delete each chat session one by one
    for (const sessionId of sessionIds) {
      await deleteChatSession(sessionId);
    }

    // Revalidate paths
    revalidatePath("/chat");
    revalidatePath("/chats");

    return { success: true };
  } catch (error) {
    console.error("Error deleting multiple chat sessions:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete chat sessions",
    };
  }
}

/**
 * Links a chat session to a theme
 * @param sessionId The ID of the chat session
 * @param themeId The ID of the theme
 * @returns The ID of the link
 */
export async function linkChatSessionToTheme(
  sessionId: string,
  themeId: string
): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  try {
    const linkId = await linkChatToTheme(sessionId, themeId);
    revalidatePath(`/chat/${sessionId}`);
    return { success: true, data: linkId };
  } catch (error) {
    console.error("Error linking chat to theme:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to link chat to theme",
    };
  }
}
