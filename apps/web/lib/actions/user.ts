"use server";

import { auth, clerkClient } from "@clerk/nextjs/server";

import { UserProfile } from "@/services/user";
import { createServerSupabaseClient } from "../db/ssr-client";

export async function createUserProfile(options: {
  name?: string;
  subscription_tier?: "free" | "pro" | "enterprise";
}): Promise<{
  success: boolean;
  data?: UserProfile;
  error?: string;
}> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Authentication required" };
  }
  try {
    const client = createServerSupabaseClient();
    const clerk = await clerkClient();

    await clerk.users.updateUser(userId, {
      publicMetadata: {
        onboardingComplete: true,
      },
    });
    // Call the database function to create or update the user profile
    const { error } = await client.rpc("create_user_profile", {
      p_user_id: userId,
      p_name: options.name,
      p_subscription_tier: options.subscription_tier,
    });

    if (error) {
      console.error("Error creating user profile:", error);
      return { success: false, error: error.message };
    }

    // Fetch the updated user profile
    const { data: profileData, error: profileError } = await client
      .from("user_profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (profileError) {
      console.error("Error fetching updated user profile:", profileError);
      return {
        success: true,
        error: "Profile created but failed to fetch updated data",
      };
    }

    return { success: true, data: profileData };
  } catch (error) {
    console.error("Error in createUserProfile:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Unknown error creating user profile",
    };
  }
}
