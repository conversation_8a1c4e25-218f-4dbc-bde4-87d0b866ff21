import {
  Brush,
  Building,
  Clock,
  Cloud,
  <PERSON>,
  <PERSON>ather,
  Layers,
  <PERSON><PERSON>,
  Spark<PERSON>,
} from "lucide-react";

const colorThemeData = {
  // Categories with their metadata
  categories: {
    minimalist: {
      id: "minimalist",
      name: "Minimalist & Elegant",
      icon: Brush,
    },
    nature: {
      id: "nature",
      name: "Nature-Inspired",
      icon: Feather,
    },
    contemporary: {
      id: "contemporary",
      name: "Bold & Contemporary",
      icon: Sparkles,
    },
    vintage: {
      id: "vintage",
      name: "Vintage & Nostalgic",
      icon: Clock,
    },
    luxury: {
      id: "luxury",
      name: "Luxury & Sophistication",
      icon: Palette,
    },
    tech: {
      id: "tech",
      name: "Tech & Digital",
      icon: Code,
    },
    playful: {
      id: "playful",
      name: "Playful & Creative",
      icon: Sparkles,
    },
    atmospheric: {
      id: "atmospheric",
      name: "Atmospheric & Moody",
      icon: Cloud,
    },
    professional: {
      id: "professional",
      name: "Professional & Corporate",
      icon: Building,
    },
    techniques: {
      id: "techniques",
      name: "Color Techniques",
      icon: Layers,
    },
  },

  // Array of all categories for easy mapping
  categoryList: [
    "minimalist",
    "nature",
    "contemporary",
    "vintage",
    "luxury",
    "tech",
    "playful",
    "atmospheric",
    "professional",
    "techniques",
  ],

  // Templates organized by category
  templatesByCategory: {
    minimalist: [
      {
        id: "minimal-elegant",
        title: "Minimal & Elegant",
        description:
          "Sophisticated, understated design with careful attention to typography and spacing",
        prompt:
          "Generate a minimal, elegant theme with sophisticated color choices and understated design",
        icon: Brush,
        colors: {
          primary: "#111827",
          secondary: "#4B5563",
          accent: "#D1D5DB",
          background: "#F3F4F6",
        },
      },
      {
        id: "minimalist-accent",
        title: "Minimalist Accent",
        description:
          "Clean grayscale foundation with one strategic color accent for impact",
        prompt:
          "Build a minimalist grayscale foundation with one strategic color accent",
        icon: Brush,
        colors: {
          primary: "#0F766E",
          secondary: "#374151",
          accent: "#06B6D4",
          background: "#F9FAFB",
        },
      },
      {
        id: "architectural-clarity",
        title: "Architectural Clarity",
        description:
          "Clean, architectural palette with precise proportions and intentional spacing",
        prompt:
          "Create a clean, architectural palette with precise proportions and intentional spacing",
        icon: Brush,
        colors: {
          primary: "#1F2937",
          secondary: "#9CA3AF",
          accent: "#E5E7EB",
          background: "#FFFFFF",
        },
      },
    ],
    nature: [
      {
        id: "serene-nature",
        title: "Serene Nature",
        description:
          "Calm, nature-inspired palette with muted tones and organic harmony",
        prompt:
          "Create a serene, nature-inspired palette with muted tones and organic harmony",
        icon: Feather,
        colors: {
          primary: "#4B5563",
          secondary: "#047857",
          accent: "#D1D5DB",
          background: "#F3F4F6",
        },
      },
      {
        id: "earthy-textures",
        title: "Earthy Textures",
        description:
          "Earthy, textural palette drawn from natural materials and weathered surfaces",
        prompt:
          "Design an earthy, textural palette drawn from natural materials and weathered surfaces",
        icon: Feather,
        colors: {
          primary: "#92400E",
          secondary: "#78350F",
          accent: "#D97706",
          background: "#FEF3C7",
        },
      },
      {
        id: "botanical-depth",
        title: "Botanical Depth",
        description:
          "Botanical color story with verdant depths and subtle seasonal influences",
        prompt:
          "Craft a botanical color story with verdant depths and subtle seasonal influences",
        icon: Feather,
        colors: {
          primary: "#065F46",
          secondary: "#047857",
          accent: "#10B981",
          background: "#ECFDF5",
        },
      },
      {
        id: "natural-earth",
        title: "Natural Earth",
        description:
          "Nature-inspired theme with earthy tones and forest greens",
        prompt:
          "Create a nature-inspired theme with earthy tones and forest greens",
        icon: Brush,
        colors: {
          primary: "#228B22",
          secondary: "#8B4513",
          accent: "#DAA520",
          background: "#F5F5DC",
        },
      },
      {
        id: "tropical-sunset",
        title: "Tropical Sunset",
        description:
          "Theme inspired by a tropical sunset with warm oranges and cool blues",
        prompt:
          "Create a theme inspired by a tropical sunset with warm oranges and cool blues",
        icon: Palette,
        colors: {
          primary: "#FF8C00",
          secondary: "#FF5733",
          accent: "#4682B4",
          background: "#FFF8DC",
        },
      },
    ],
    contemporary: [
      {
        id: "bold-contemporary",
        title: "Bold Contemporary",
        description:
          "Strong, impactful design with strategic accent colors for visual hierarchy",
        prompt:
          "Design a bold, contemporary color scheme with strategic accent colors and visual impact",
        icon: Sparkles,
        colors: {
          primary: "#4F46E5",
          secondary: "#7C3AED",
          accent: "#EC4899",
          background: "#FFFFFF",
        },
      },
      {
        id: "vibrant-expression",
        title: "Vibrant Expression",
        description:
          "Energetic, expressive palette with confident color statements",
        prompt:
          "Generate a vibrant, expressive palette with confident color statements and dynamic energy",
        icon: Sparkles,
        colors: {
          primary: "#DB2777",
          secondary: "#7C3AED",
          accent: "#FBBF24",
          background: "#F3F4F6",
        },
      },
      {
        id: "urban-street",
        title: "Urban Street",
        description:
          "Street-inspired color collection with cultural relevance and graphic punch",
        prompt:
          "Create an urban, street-inspired color collection with cultural relevance and graphic punch",
        icon: Sparkles,
        colors: {
          primary: "#DC2626",
          secondary: "#2563EB",
          accent: "#FBBF24",
          background: "#111827",
        },
      },
    ],
    vintage: [
      {
        id: "vintage-warmth",
        title: "Vintage Warmth",
        description:
          "Nostalgic color palette with warm tones and subtle patina effects",
        prompt:
          "Develop a vintage-inspired palette with nostalgic warmth and subtle patina",
        icon: Clock,
        colors: {
          primary: "#B45309",
          secondary: "#9D174D",
          accent: "#1E40AF",
          background: "#FEF3C7",
        },
      },
      {
        id: "mid-century-modern",
        title: "Mid-Century Modern",
        description:
          "Retro color scheme inspired by mid-century design aesthetics",
        prompt:
          "Craft a mid-century modern color scheme with retro charm and historical accuracy",
        icon: Clock,
        colors: {
          primary: "#EA580C",
          secondary: "#0369A1",
          accent: "#A16207",
          background: "#FAFAF9",
        },
      },
      {
        id: "timeworn-heritage",
        title: "Timeworn Heritage",
        description:
          "Aged, storied palette suggesting heritage and collected memories",
        prompt:
          "Design a timeworn, storied palette suggesting heritage and collected memories",
        icon: Clock,
        colors: {
          primary: "#78350F",
          secondary: "#44403C",
          accent: "#A8A29E",
          background: "#FAFAF9",
        },
      },
    ],
    luxury: [
      {
        id: "luxury-depth",
        title: "Luxury Depth",
        description:
          "Rich, textural palette with refined darkness and premium feel",
        prompt:
          "Generate a luxurious, depth-rich palette with textural qualities and refined darkness",
        icon: Palette,
        colors: {
          primary: "#18181B",
          secondary: "#4C1D95",
          accent: "#D4D4D8",
          background: "#FAFAFA",
        },
      },
      {
        id: "opulent-jewels",
        title: "Opulent Jewels",
        description:
          "Jewel-toned collection with sumptuous depth and rich saturation",
        prompt:
          "Create an opulent, jewel-toned collection with sumptuous depth and rich saturation",
        icon: Palette,
        colors: {
          primary: "#4C1D95",
          secondary: "#9D174D",
          accent: "#059669",
          background: "#18181B",
        },
      },
      {
        id: "premium-exclusive",
        title: "Premium Exclusive",
        description:
          "High-end color palette with nuanced undertones and exclusive feeling",
        prompt:
          "Design a high-end, premium palette with nuanced undertones and exclusive feeling",
        icon: Palette,
        colors: {
          primary: "#1E293B",
          secondary: "#334155",
          accent: "#94A3B8",
          background: "#F8FAFC",
        },
      },
      {
        id: "luxury-gold",
        title: "Luxury Gold",
        description:
          "Luxury brand theme with gold accents and elegant typography",
        prompt:
          "Design a luxury brand theme with gold accents and elegant typography",
        icon: Palette,
        colors: {
          primary: "#D4AF37",
          secondary: "#000000",
          accent: "#8B7D6B",
          background: "#FFFFFF",
        },
      },
    ],
    tech: [
      {
        id: "tech-dashboard",
        title: "Tech Dashboard",
        description:
          "Modern tech-inspired theme with dark mode and vibrant accents",
        prompt:
          "Craft a tech-forward color system with purposeful contrast and digital clarity",
        icon: Code,
        colors: {
          primary: "#2563EB",
          secondary: "#7C3AED",
          accent: "#06B6D4",
          background: "#0F172A",
        },
      },
      {
        id: "ui-optimized",
        title: "UI Optimized",
        description:
          "Screen-optimized palette designed for digital interfaces and accessibility",
        prompt:
          "Generate a future-facing UI palette optimized for screen display and accessibility",
        icon: Code,
        colors: {
          primary: "#0284C7",
          secondary: "#0F766E",
          accent: "#2DD4BF",
          background: "#F1F5F9",
        },
      },
      {
        id: "data-visualization",
        title: "Data Visualization",
        description:
          "Color set optimized for data visualization with clear hierarchical relationships",
        prompt:
          "Design a data-visualization friendly color set with clear hierarchical relationships",
        icon: Code,
        colors: {
          primary: "#0369A1",
          secondary: "#15803D",
          accent: "#CA8A04",
          background: "#FFFFFF",
        },
      },
      {
        id: "cyberpunk-neon",
        title: "Cyberpunk Neon",
        description:
          "Dark mode theme with neon accents for a cyberpunk aesthetic",
        prompt:
          "Design a dark mode theme with neon accents for a cyberpunk aesthetic",
        icon: Code,
        colors: {
          primary: "#00FF9F",
          secondary: "#FF00FF",
          accent: "#00FFFF",
          background: "#0D0D0D",
        },
      },
    ],
    playful: [
      {
        id: "playful-interface",
        title: "Playful Interface",
        description:
          "Vibrant, energetic colors for creative applications and youth-oriented products",
        prompt:
          "Compose a playful yet sophisticated color arrangement with unexpected pairings",
        icon: Sparkles,
        colors: {
          primary: "#EC4899",
          secondary: "#8B5CF6",
          accent: "#FBBF24",
          background: "#FFFBEB",
        },
      },
      {
        id: "whimsical-story",
        title: "Whimsical Story",
        description: "Storytelling palette with character-driven color choices",
        prompt:
          "Create a whimsical, storytelling palette with character-driven color choices",
        icon: Sparkles,
        colors: {
          primary: "#C026D3",
          secondary: "#2563EB",
          accent: "#F59E0B",
          background: "#F5F3FF",
        },
      },
      {
        id: "creative-studio",
        title: "Creative Studio",
        description:
          "Artistic palette with expressive range and creative depth",
        prompt:
          "Design a creative, studio-inspired scheme with artistic depth and expressive range",
        icon: Sparkles,
        colors: {
          primary: "#DB2777",
          secondary: "#4F46E5",
          accent: "#0D9488",
          background: "#FAFAFA",
        },
      },
      {
        id: "kids-education",
        title: "Kids Education",
        description:
          "Playful theme for a children's educational app with bright primary colors",
        prompt:
          "Create a playful theme for a children's educational app with bright primary colors",
        icon: Brush,
        colors: {
          primary: "#FF0000",
          secondary: "#FFFF00",
          accent: "#0000FF",
          background: "#FFFFFF",
        },
      },
    ],
    atmospheric: [
      {
        id: "dawn-atmosphere",
        title: "Dawn Atmosphere",
        description:
          "Atmospheric palette inspired by early morning light and mood",
        prompt:
          "Create an atmospheric, mood-driven palette inspired by dawn lighting",
        icon: Cloud,
        colors: {
          primary: "#475569",
          secondary: "#9D174D",
          accent: "#FB923C",
          background: "#F8FAFC",
        },
      },
      {
        id: "cinematic-mood",
        title: "Cinematic Mood",
        description:
          "Film-inspired color story with emotional resonance and visual narrative",
        prompt:
          "Generate a cinematic color story with emotional resonance and visual narrative",
        icon: Cloud,
        colors: {
          primary: "#1E293B",
          secondary: "#0F766E",
          accent: "#FB923C",
          background: "#0F172A",
        },
      },
      {
        id: "contemplative-space",
        title: "Contemplative Space",
        description:
          "Low-light palette evoking intimate spaces and quiet moments",
        prompt:
          "Design a contemplative, low-light palette evoking intimate spaces and quiet moments",
        icon: Cloud,
        colors: {
          primary: "#334155",
          secondary: "#7E22CE",
          accent: "#94A3B8",
          background: "#1E293B",
        },
      },
    ],
    professional: [
      {
        id: "professional-authority",
        title: "Professional Authority",
        description:
          "Balanced corporate palette with authoritative primary colors and supportive neutrals",
        prompt:
          "Design a professional palette with authoritative primary colors and supportive neutrals",
        icon: Building,
        colors: {
          primary: "#0F766E",
          secondary: "#334155",
          accent: "#94A3B8",
          background: "#F8FAFC",
        },
      },
      {
        id: "corporate-trust",
        title: "Corporate Trust",
        description:
          "Trustworthy identity system with sector-appropriate color choices",
        prompt:
          "Create a trustworthy, corporate identity system with sector-appropriate color choices",
        icon: Building,
        colors: {
          primary: "#1E40AF",
          secondary: "#1F2937",
          accent: "#60A5FA",
          background: "#FFFFFF",
        },
      },
      {
        id: "business-balance",
        title: "Business Balance",
        description:
          "Versatile business palette balancing tradition with contemporary relevance",
        prompt:
          "Generate a versatile business palette balancing tradition with contemporary relevance",
        icon: Building,
        colors: {
          primary: "#1E3A8A",
          secondary: "#374151",
          accent: "#3B82F6",
          background: "#F9FAFB",
        },
      },
      {
        id: "modern-dashboard",
        title: "Modern Dashboard",
        description:
          "Clean, professional dashboard with subtle gradients and clear hierarchy",
        prompt:
          "Create a modern dashboard theme with subtle gradients, professional look, and clear visual hierarchy",
        icon: Layers,
        colors: {
          primary: "#3B82F6",
          secondary: "#6366F1",
          accent: "#10B981",
          background: "#F9FAFB",
        },
      },
      {
        id: "financial-pro",
        title: "Financial Pro",
        description:
          "Professional theme with accessible color contrast for financial applications",
        prompt:
          "Generate a professional theme with accessible color contrast for financial applications",
        icon: Layers,
        colors: {
          primary: "#0047AB",
          secondary: "#6082B6",
          accent: "#36454F",
          background: "#F5F5F5",
        },
      },
    ],
    techniques: [
      {
        id: "monochromatic-gradient",
        title: "Monochromatic Gradient",
        description:
          "Gradient series with subtle tonal shifts and cohesive progression",
        prompt:
          "Form a monochromatic gradient series with subtle tonal shifts and cohesive progression",
        icon: Layers,
        colors: {
          primary: "#1E40AF",
          secondary: "#3B82F6",
          accent: "#93C5FD",
          background: "#EFF6FF",
        },
      },
      {
        id: "harmonious-balance",
        title: "Harmonious Balance",
        description:
          "Carefully calibrated color relationships for visual harmony",
        prompt:
          "Create a harmonious palette with carefully calibrated color relationships and balance",
        icon: Layers,
        colors: {
          primary: "#0F766E",
          secondary: "#4F46E5",
          accent: "#F59E0B",
          background: "#F0FDFA",
        },
      },
      {
        id: "psychological-intent",
        title: "Psychological Intent",
        description:
          "Color system with psychological intentionality and strategic emotional impact",
        prompt:
          "Design a color system with psychological intentionality and strategic emotional impact",
        icon: Layers,
        colors: {
          primary: "#4338CA",
          secondary: "#059669",
          accent: "#F59E0B",
          background: "#FFFFFF",
        },
      },
    ],
  },

  // All templates in a single array (useful for search or filtering)
  allTemplates: [
    // The templates will be added programmatically below
  ],
};

// Populate the allTemplates array from the templatesByCategory
colorThemeData.categoryList.forEach((categoryId) => {
  // @ts-expect-error fix this
  colorThemeData.templatesByCategory[categoryId].forEach((template) => {
    // @ts-expect-error fix this
    colorThemeData.allTemplates.push({
      ...template,
      category: categoryId,
    });
  });
});

export default colorThemeData;
