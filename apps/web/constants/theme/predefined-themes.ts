import { AITheme } from "@/schema/theme";

export const DEFAULT_THEME: Omit<
  AITheme,
  "analysis" | "description" | "primaryHue"
> = {
  name: "<PERSON><PERSON>",
  format: "oklch",
  colors: {
    background: "oklch(0.985 0.005 200)",
    foreground: "oklch(0.25 0.01 200)",
    card: "oklch(1 0 0)",
    "card-foreground": "oklch(0.25 0.01 200)",
    popover: "oklch(1 0 0)",
    "popover-foreground": "oklch(0.25 0.01 200)",
    primary: "oklch(0.68 0.15 198)",
    "primary-foreground": "oklch(0.99 0.005 198)",
    secondary: "oklch(0.88 0.06 190)",
    "secondary-foreground": "oklch(0.30 0.02 190)",
    muted: "oklch(0.95 0.01 200)",
    "muted-foreground": "oklch(0.55 0.01 200)",
    accent: "oklch(0.92 0.04 195)",
    "accent-foreground": "oklch(0.28 0.02 195)",
    destructive: "oklch(0.65 0.18 25)",
    border: "oklch(0.90 0.01 200)",
    input: "oklch(0.90 0.01 200)",
    ring: "oklch(0.70 0.16 198)",
    radius: "0.5rem",
    sidebar: "oklch(0.97 0.01 200)",
    "sidebar-foreground": "oklch(0.25 0.01 200)",
    "sidebar-primary": "oklch(0.68 0.15 198)",
    "sidebar-primary-foreground": "oklch(0.99 0.005 198)",
    "sidebar-accent": "oklch(0.92 0.04 195)",
    "sidebar-accent-foreground": "oklch(0.28 0.02 195)",
    "sidebar-border": "oklch(0.90 0.01 200)",
    "sidebar-ring": "oklch(0.70 0.16 198)",
    "chart-1": "oklch(0.70 0.15 198)",
    "chart-2": "oklch(0.70 0.14 245)",
    "chart-3": "oklch(0.70 0.13 145)",
    "chart-4": "oklch(0.70 0.15 50)",
    "chart-5": "oklch(0.70 0.14 290)",
  },
  dark_colors: {
    background: "oklch(0.18 0.02 200)",
    foreground: "oklch(0.96 0.005 200)",
    card: "oklch(0.22 0.02 200)",
    "card-foreground": "oklch(0.96 0.005 200)",
    popover: "oklch(0.20 0.02 200)",
    "popover-foreground": "oklch(0.96 0.005 200)",
    primary: "oklch(0.75 0.16 198)",
    "primary-foreground": "oklch(0.20 0.02 198)",
    secondary: "oklch(0.45 0.08 190)",
    "secondary-foreground": "oklch(0.98 0.005 190)",
    muted: "oklch(0.30 0.02 200)",
    "muted-foreground": "oklch(0.70 0.01 200)",
    accent: "oklch(0.35 0.05 195)",
    "accent-foreground": "oklch(0.98 0.005 195)",
    destructive: "oklch(0.72 0.19 25)",
    border: "oklch(0.32 0.02 200)",
    input: "oklch(0.32 0.02 200)",
    ring: "oklch(0.75 0.16 198)",
    sidebar: "oklch(0.15 0.025 200)",
    "sidebar-foreground": "oklch(0.96 0.005 200)",
    "sidebar-primary": "oklch(0.75 0.16 198)",
    "sidebar-primary-foreground": "oklch(0.20 0.02 198)",
    "sidebar-accent": "oklch(0.35 0.05 195)",
    "sidebar-accent-foreground": "oklch(0.98 0.005 195)",
    "sidebar-border": "oklch(0.32 0.02 200)",
    "sidebar-ring": "oklch(0.75 0.16 198)",
    "chart-1": "oklch(0.78 0.16 198)",
    "chart-2": "oklch(0.78 0.15 245)",
    "chart-3": "oklch(0.78 0.14 145)",
    "chart-4": "oklch(0.78 0.16 50)",
    "chart-5": "oklch(0.78 0.15 290)",
  },
};

export const ArcticCyan: Omit<
  AITheme,
  "analysis" | "description" | "primaryHue"
> = {
  name: "Arctic Cyan",
  format: "hsl",
  colors: {
    background: "hsl(180 50% 98%)",
    foreground: "hsl(180 10% 10%)",
    card: "hsl(180 50% 96%)",
    "card-foreground": "hsl(180 10% 10%)",
    popover: "hsl(180 50% 97%)",
    "popover-foreground": "hsl(180 10% 10%)",
    primary: "hsl(180 70% 50%)",
    "primary-foreground": "hsl(180 100% 98%)",
    secondary: "hsl(180 30% 75%)",
    "secondary-foreground": "hsl(180 10% 20%)",
    muted: "hsl(180 20% 92%)",
    "muted-foreground": "hsl(180 10% 45%)",
    accent: "hsl(180 60% 90%)",
    "accent-foreground": "hsl(180 10% 15%)",
    destructive: "hsl(0 84% 60%)",
    border: "hsl(180 20% 88%)",
    input: "hsl(180 20% 80%)",
    ring: "hsl(180 70% 60%)",
    radius: "0.5rem",
    sidebar: "hsl(180 30% 95%)",
    "sidebar-foreground": "hsl(180 10% 10%)",
    "sidebar-primary": "hsl(180 70% 50%)",
    "sidebar-primary-foreground": "hsl(180 100% 98%)",
    "sidebar-accent": "hsl(180 40% 88%)",
    "sidebar-accent-foreground": "hsl(180 10% 15%)",
    "sidebar-border": "hsl(180 20% 88%)",
    "sidebar-ring": "hsl(180 70% 60%)",
    "chart-1": "hsl(180 70% 50%)",
    "chart-2": "hsl(160 60% 45%)",
    "chart-3": "hsl(200 75% 55%)",
    "chart-4": "hsl(35 80% 60%)",
    "chart-5": "hsl(260 60% 60%)",
  },
  dark_colors: {
    background: "hsl(180 10% 10%)",
    foreground: "hsl(180 10% 95%)",
    card: "hsl(180 10% 15%)",
    "card-foreground": "hsl(180 10% 95%)",
    popover: "hsl(180 10% 14%)",
    "popover-foreground": "hsl(180 10% 95%)",
    primary: "hsl(180 70% 60%)",
    "primary-foreground": "hsl(180 10% 10%)",
    secondary: "hsl(180 15% 30%)",
    "secondary-foreground": "hsl(180 5% 90%)",
    muted: "hsl(180 10% 20%)",
    "muted-foreground": "hsl(180 5% 60%)",
    accent: "hsl(180 20% 25%)",
    "accent-foreground": "hsl(180 5% 90%)",
    destructive: "hsl(0 70% 70%)",
    border: "hsl(180 10% 25%)",
    input: "hsl(180 10% 30%)",
    ring: "hsl(180 70% 60%)",
    sidebar: "hsl(180 15% 13%)",
    "sidebar-foreground": "hsl(180 10% 95%)",
    "sidebar-primary": "hsl(180 70% 60%)",
    "sidebar-primary-foreground": "hsl(180 10% 10%)",
    "sidebar-accent": "hsl(180 15% 28%)",
    "sidebar-accent-foreground": "hsl(180 5% 90%)",
    "sidebar-border": "hsl(180 10% 25%)",
    "sidebar-ring": "hsl(180 70% 60%)",
    "chart-1": "hsl(180 70% 60%)",
    "chart-2": "hsl(160 60% 55%)",
    "chart-3": "hsl(200 75% 65%)",
    "chart-4": "hsl(35 80% 70%)",
    "chart-5": "hsl(260 60% 70%)",
  },
};

export const SpendWiseOrange: Omit<
  AITheme,
  "analysis" | "description" | "primaryHue"
> = {
  name: "SpendWise Orange",
  format: "oklch",
  colors: {
    background: "oklch(0.99 0.01 240)",
    foreground: "oklch(0.20 0.02 240)",
    card: "oklch(1.0 0 0)",
    "card-foreground": "oklch(0.20 0.02 240)",
    popover: "oklch(1.0 0 0)",
    "popover-foreground": "oklch(0.20 0.02 240)",
    primary: "oklch(0.60 0.16 27)",
    "primary-foreground": "oklch(0.98 0.01 27)",
    secondary: "oklch(0.85 0.03 240)",
    "secondary-foreground": "oklch(0.30 0.03 240)",
    muted: "oklch(0.95 0.01 240)",
    "muted-foreground": "oklch(0.50 0.02 240)",
    accent: "oklch(0.96 0.04 27)",
    "accent-foreground": "oklch(0.40 0.10 27)",
    destructive: "oklch(0.65 0.18 25)",
    border: "oklch(0.90 0.02 240)",
    input: "oklch(0.88 0.02 240)",
    ring: "oklch(0.75 0.16 27)",
    radius: "0.5rem",
    sidebar: "oklch(0.97 0.01 240)",
    "sidebar-foreground": "oklch(0.20 0.02 240)",
    "sidebar-primary": "oklch(0.60 0.16 27)",
    "sidebar-primary-foreground": "oklch(0.98 0.01 27)",
    "sidebar-accent": "oklch(0.92 0.03 27)",
    "sidebar-accent-foreground": "oklch(0.40 0.10 27)",
    "sidebar-border": "oklch(0.90 0.02 240)",
    "sidebar-ring": "oklch(0.75 0.16 27)",
  },
  dark_colors: {
    background: "oklch(0.15 0.02 240)",
    foreground: "oklch(0.95 0.01 240)",
    card: "oklch(0.18 0.02 240)",
    "card-foreground": "oklch(0.95 0.01 240)",
    popover: "oklch(0.18 0.02 240)",
    "popover-foreground": "oklch(0.95 0.01 240)",
    primary: "oklch(0.65 0.17 27)",
    "primary-foreground": "oklch(0.20 0.05 27)",
    secondary: "oklch(0.30 0.03 240)",
    "secondary-foreground": "oklch(0.90 0.02 240)",
    muted: "oklch(0.25 0.02 240)",
    "muted-foreground": "oklch(0.65 0.02 240)",
    accent: "oklch(0.35 0.05 27)",
    "accent-foreground": "oklch(0.96 0.04 27)",
    destructive: "oklch(0.68 0.19 25)",
    border: "oklch(0.30 0.02 240)",
    input: "oklch(0.32 0.02 240)",
    ring: "oklch(0.65 0.17 27)",
    sidebar: "oklch(0.12 0.02 240)",
    "sidebar-foreground": "oklch(0.95 0.01 240)",
    "sidebar-primary": "oklch(0.65 0.17 27)",
    "sidebar-primary-foreground": "oklch(0.20 0.05 27)",
    "sidebar-accent": "oklch(0.28 0.04 27)",
    "sidebar-accent-foreground": "oklch(0.96 0.04 27)",
    "sidebar-border": "oklch(0.30 0.02 240)",
    "sidebar-ring": "oklch(0.65 0.17 27)",
  },
};

export const predefinedThemes: Omit<
  AITheme,
  "analysis" | "description" | "primaryHue"
>[] = [DEFAULT_THEME, ArcticCyan, SpendWiseOrange];
