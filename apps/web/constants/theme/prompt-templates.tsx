import {
  Brush,
  Building,
  Clock,
  Cloud,
  <PERSON>,
  <PERSON>,
  <PERSON>ather,
  Layers,
  Palette,
  Sparkles,
  Tag,
} from "lucide-react";

export const templates = [
  {
    title: "Modern Dashboard",
    description:
      "Clean, professional dashboard with subtle gradients and clear hierarchy",
    prompt:
      "Create a modern dashboard theme with subtle gradients, professional look, and clear visual hierarchy",
    icon: Layers,
    category: "Business",
    colors: {
      primary: "#3B82F6",
      secondary: "#6366F1",
      accent: "#10B981",
      background: "#F9FAFB",
    },
  },
  {
    title: "Playful Interface",
    description:
      "Vibrant, energetic colors for creative applications and youth-oriented products",
    prompt:
      "Design a playful, vibrant interface with energetic colors suitable for creative applications",
    icon: Palette,
    category: "Creative",
    colors: {
      primary: "#EC4899",
      secondary: "#8B5CF6",
      accent: "#FBBF24",
      background: "#FFFBEB",
    },
  },
  {
    title: "Minimal & Elegant",
    description:
      "Sophisticated, understated design with careful attention to typography and spacing",
    prompt:
      "Generate a minimal, elegant theme with sophisticated color choices and understated design",
    icon: Brush,
    category: "Minimal",
    colors: {
      primary: "#111827",
      secondary: "#4B5563",
      accent: "#D1D5DB",
      background: "#F3F4F6",
    },
  },
  {
    title: "Tech Dashboard",
    description:
      "Modern tech-inspired theme with dark mode and vibrant accents",
    prompt:
      "Create a tech-inspired dashboard theme with dark mode support and vibrant accent colors",
    icon: Code,
    category: "Technical",
    colors: {
      primary: "#2563EB",
      secondary: "#7C3AED",
      accent: "#06B6D4",
      background: "#0F172A",
    },
  },
];

// Core template categories with example prompts
export const categories = [
  {
    id: "minimalist",
    name: "Minimalist & Elegant",
    icon: Brush,
  },
  {
    id: "nature",
    name: "Nature-Inspired",
    icon: Feather,
  },
  {
    id: "contemporary",
    name: "Bold & Contemporary",
    icon: Sparkles,
  },
  {
    id: "vintage",
    name: "Vintage & Nostalgic",
    icon: Clock,
  },
  {
    id: "luxury",
    name: "Luxury & Sophistication",
    icon: Palette,
  },
  {
    id: "tech",
    name: "Tech & Digital",
    icon: Code,
  },
  {
    id: "playful",
    name: "Playful & Creative",
    icon: Sparkles,
  },
  {
    id: "atmospheric",
    name: "Atmospheric & Moody",
    icon: Cloud,
  },
  {
    id: "professional",
    name: "Professional & Corporate",
    icon: Building,
  },
  {
    id: "techniques",
    name: "Color Techniques",
    icon: Layers,
  },
  // Adding two new categories to enhance offerings
  {
    id: "brandIdentity",
    name: "Brand Identity",
    icon: Tag, // Assuming you have this icon
  },
  {
    id: "accessibility",
    name: "Accessibility-Focused",
    icon: Eye, // Assuming you have this icon
  },
];

// Enhanced template collection
export const enhancedTemplates = [
  // Minimalist & Elegant
  {
    title: "Minimal Sophistication",
    description: "Refined minimal design with subtle sophistication",
    prompt:
      "Create a minimal yet sophisticated color system with intentional restraint and elegant neutrals. Focus on subtle tonal relationships and refined contrast that feels luxurious yet understated.",
    icon: Brush,
    category: "minimalist",
    colors: {
      primary: "#111827",
      secondary: "#4B5563",
      accent: "#D1D5DB",
      background: "#F3F4F6",
    },
  },
  {
    title: "Monochromatic Depth",
    description: "Single-color palette with rich tonal variations",
    prompt:
      "Design a monochromatic color system with rich tonal variations. Create visual interest through subtle shifts in saturation and luminance rather than hue changes, maintaining a cohesive and sophisticated appearance.",
    icon: Brush,
    category: "minimalist",
    colors: {
      primary: "#0F766E",
      secondary: "#374151",
      accent: "#06B6D4",
      background: "#F9FAFB",
    },
  },

  // Nature-Inspired
  {
    title: "Botanical Harmony",
    description:
      "Organic palette inspired by plant life and natural environments",
    prompt:
      "Create an organic color system inspired by botanical environments. Draw from the rich greens of diverse foliage, complemented by the subtle colors of flowers, stems, and soil to create a harmonious natural palette that feels alive and refreshing.",
    icon: Feather,
    category: "nature",
    colors: {
      primary: "#065F46",
      secondary: "#047857",
      accent: "#10B981",
      background: "#ECFDF5",
    },
  },
  {
    title: "Ocean Depths",
    description:
      "Immersive blues and teals inspired by underwater environments",
    prompt:
      "Design a color system inspired by ocean depths. Capture the rich blues and teals of deep water, complemented by the organic colors of coral, marine life, and seafloor textures, creating an immersive underwater aesthetic.",
    icon: Feather,
    category: "nature",
    colors: {
      primary: "#0E7490",
      secondary: "#0369A1",
      accent: "#06B6D4",
      background: "#ECFEFF",
    },
  },

  // Bold & Contemporary
  {
    title: "Bold Expressions",
    description: "Confident, expressive palette with striking contrasts",
    prompt:
      "Design a bold, expressive color system with confident color statements. Create striking contrasts and unexpected combinations that feel contemporary, vibrant, and visually impactful without being garish or overwhelming.",
    icon: Sparkles,
    category: "contemporary",
    colors: {
      primary: "#4F46E5",
      secondary: "#7C3AED",
      accent: "#EC4899",
      background: "#FFFFFF",
    },
  },
  {
    title: "Neon Nights",
    description:
      "Electric, high-energy palette for impactful digital experiences",
    prompt:
      "Create an electric, high-energy color system inspired by neon lights in an urban nightscape. Blend vibrant, luminous colors that appear to glow against darker backgrounds, creating a futuristic and energetic visual experience.",
    icon: Sparkles,
    category: "contemporary",
    colors: {
      primary: "#DB2777",
      secondary: "#7C3AED",
      accent: "#FBBF24",
      background: "#0F172A",
    },
  },

  // Vintage & Nostalgic
  {
    title: "Film Photography",
    description:
      "Nostalgic palette inspired by analog film and vintage photography",
    prompt:
      "Create a nostalgic color system inspired by analog film photography. Incorporate the warm, slightly desaturated tones of vintage photos, with subtle color shifts that evoke specific film stocks and development processes from past decades.",
    icon: Clock,
    category: "vintage",
    colors: {
      primary: "#78350F",
      secondary: "#44403C",
      accent: "#A8A29E",
      background: "#FAFAF9",
    },
  },
  {
    title: "Retro Computing",
    description: "Palette inspired by early digital interfaces and computing",
    prompt:
      "Design a retro computing color system inspired by early digital interfaces. Draw from the limited color palettes of 8-bit and 16-bit computing eras, CRT monitors, and vintage software interfaces, creating a nostalgic yet functional digital aesthetic.",
    icon: Clock,
    category: "vintage",
    colors: {
      primary: "#2563EB",
      secondary: "#16A34A",
      accent: "#EAB308",
      background: "#0C0A09",
    },
  },

  // Luxury & Sophistication
  {
    title: "Opulent Luxury",
    description: "Rich, premium palette with sumptuous textures and depth",
    prompt:
      "Create an opulent luxury color system with rich, premium tonality. Incorporate deep jewel tones, metallic accents, and sophisticated neutrals that convey exclusivity and sumptuous materiality reminiscent of high fashion and fine craftsmanship.",
    icon: Palette,
    category: "luxury",
    colors: {
      primary: "#18181B",
      secondary: "#4C1D95",
      accent: "#D4D4D8",
      background: "#FAFAFA",
    },
  },
  {
    title: "Sophisticated Neutrals",
    description: "High-end palette of complex, nuanced neutral tones",
    prompt:
      "Design a sophisticated system of complex neutral tones. Create a palette of nuanced blacks, grays, taupes, and off-whites that appear simple at first glance but reveal subtle undertones and rich depth that evolves in different lighting conditions.",
    icon: Palette,
    category: "luxury",
    colors: {
      primary: "#1E293B",
      secondary: "#334155",
      accent: "#94A3B8",
      background: "#F8FAFC",
    },
  },

  // Tech & Digital
  {
    title: "Data Visualization",
    description:
      "Functional palette optimized for clarity in data presentation",
    prompt:
      "Create a functional color system optimized for data visualization and information hierarchy. Design a palette that maintains distinction between elements across charts and graphs while ensuring accessibility and clear visual relationships between data points.",
    icon: Code,
    category: "tech",
    colors: {
      primary: "#0369A1",
      secondary: "#15803D",
      accent: "#CA8A04",
      background: "#FFFFFF",
    },
  },
  {
    title: "Developer Interface",
    description: "Code-optimized palette with semantic color associations",
    prompt:
      "Design a developer-friendly color system with semantic color associations. Create a palette that supports extended screen use with appropriate contrast, distinct state indicators, and color meanings that align with programming conventions and mental models.",
    icon: Code,
    category: "tech",
    colors: {
      primary: "#2563EB",
      secondary: "#7C3AED",
      accent: "#06B6D4",
      background: "#0F172A",
    },
  },

  // Playful & Creative
  {
    title: "Playful Innovation",
    description: "Unexpected color combinations with creative energy",
    prompt:
      "Create a playfully innovative color system with unexpected combinations and creative energy. Design a palette that feels both sophisticated and experimental, with surprising color juxtapositions that maintain harmony while challenging conventional pairings.",
    icon: Sparkles,
    category: "playful",
    colors: {
      primary: "#EC4899",
      secondary: "#8B5CF6",
      accent: "#FBBF24",
      background: "#FFFBEB",
    },
  },
  {
    title: "Creative Workspace",
    description: "Energizing palette that stimulates creative thinking",
    prompt:
      "Design an energizing color system for creative workspaces and ideation environments. Create a palette that stimulates creative thinking and encourages exploration, with colors that feel inspirational yet don't overwhelm or distract from creative activities.",
    icon: Sparkles,
    category: "playful",
    colors: {
      primary: "#C026D3",
      secondary: "#2563EB",
      accent: "#F59E0B",
      background: "#F5F3FF",
    },
  },

  // Atmospheric & Moody
  {
    title: "Foggy Dawn",
    description: "Ethereal palette inspired by early morning mist and light",
    prompt:
      "Create an ethereal color system inspired by foggy dawn landscapes. Capture the diffused light, muted saturation, and atmospheric depth of misty mornings, with subtle color transitions that evoke the liminal quality of daybreak.",
    icon: Cloud,
    category: "atmospheric",
    colors: {
      primary: "#475569",
      secondary: "#9D174D",
      accent: "#FB923C",
      background: "#F8FAFC",
    },
  },
  {
    title: "Twilight Mood",
    description: "Emotive palette inspired by the transition from day to night",
    prompt:
      "Design an emotive color system inspired by twilight transitions. Blend the rich blues of approaching night with the last warm glows of sunset, creating a contemplative palette with emotional depth and narrative quality.",
    icon: Cloud,
    category: "atmospheric",
    colors: {
      primary: "#1E293B",
      secondary: "#0F766E",
      accent: "#FB923C",
      background: "#0F172A",
    },
  },

  // Professional & Corporate
  {
    title: "Corporate Trust",
    description:
      "Reliable palette that communicates professionalism and stability",
    prompt:
      "Create a trustworthy corporate color system that communicates professionalism and stability. Design a palette that balances authority with approachability, using colors that convey reliability and competence while remaining fresh and contemporary.",
    icon: Building,
    category: "professional",
    colors: {
      primary: "#1E40AF",
      secondary: "#1F2937",
      accent: "#60A5FA",
      background: "#FFFFFF",
    },
  },
  {
    title: "Financial Clarity",
    description:
      "Precise palette optimized for financial data and applications",
    prompt:
      "Design a color system optimized for financial interfaces and data visualization. Create a palette that supports clear information hierarchy, helps users quickly distinguish between different data categories, and maintains a professional, trustworthy appearance.",
    icon: Building,
    category: "professional",
    colors: {
      primary: "#0F766E",
      secondary: "#334155",
      accent: "#94A3B8",
      background: "#F8FAFC",
    },
  },

  // Color Techniques
  {
    title: "Analogous Harmony",
    description: "Harmonious palette using adjacent colors on the color wheel",
    prompt:
      "Create a harmonious color system using analogous relationships. Design a palette based on adjacent hues on the color wheel, creating smooth transitions and natural harmony while maintaining sufficient contrast for functional interfaces.",
    icon: Layers,
    category: "techniques",
    colors: {
      primary: "#1E40AF",
      secondary: "#3B82F6",
      accent: "#93C5FD",
      background: "#EFF6FF",
    },
  },
  {
    title: "Split Complementary",
    description:
      "Dynamic palette using split complementary color relationships",
    prompt:
      "Design a dynamic color system using split complementary relationships. Create a palette that pairs a base color with the two colors adjacent to its complement on the color wheel, producing vibrant contrasts with sophisticated balance.",
    icon: Layers,
    category: "techniques",
    colors: {
      primary: "#0F766E",
      secondary: "#4F46E5",
      accent: "#F59E0B",
      background: "#F0FDFA",
    },
  },

  // Brand Identity (New Category)
  {
    title: "Brand Evolution",
    description:
      "Refreshed palette that modernizes an established brand identity",
    prompt:
      "Create a refreshed color system that evolves an established brand identity. Design a palette that maintains brand recognition while introducing contemporary relevance, with strategic color updates that respect brand heritage while moving forward.",
    icon: Tag,
    category: "brandIdentity",
    colors: {
      primary: "#0891B2",
      secondary: "#164E63",
      accent: "#06B6D4",
      background: "#ECFEFF",
    },
  },
  {
    title: "Brand Differentiation",
    description:
      "Distinctive palette that stands out within a competitive market",
    prompt:
      "Design a distinctive color system that differentiates a brand within its competitive landscape. Create a palette that carves out unique visual territory within the market context while still feeling appropriate for the industry and target audience.",
    icon: Tag,
    category: "brandIdentity",
    colors: {
      primary: "#9333EA",
      secondary: "#4F46E5",
      accent: "#EC4899",
      background: "#F5F3FF",
    },
  },

  // Accessibility-Focused (New Category)
  {
    title: "Universal Accessibility",
    description:
      "Inclusive palette designed for users with various visual abilities",
    prompt:
      "Create an inclusive color system designed for users with diverse visual abilities. Design a palette with sufficient contrast ratios for all critical interface elements, avoiding problematic color combinations for common forms of color vision deficiency.",
    icon: Eye,
    category: "accessibility",
    colors: {
      primary: "#1D4ED8",
      secondary: "#0F766E",
      accent: "#B91C1C",
      background: "#F9FAFB",
    },
  },
  {
    title: "Low Vision Focus",
    description: "High-contrast palette optimized for users with low vision",
    prompt:
      "Design a high-contrast color system optimized for users with low vision. Create a palette with pronounced luminance differences between foreground and background elements, clear focus states, and distinctive interactive elements that don't rely solely on color.",
    icon: Eye,
    category: "accessibility",
    colors: {
      primary: "#000000",
      secondary: "#1D4ED8",
      accent: "#F59E0B",
      background: "#FFFFFF",
    },
  },

  // AI & Vibe Coding (New Category to emphasize app focus)
  {
    title: "AI-Enhanced UI",
    description:
      "Forward-looking palette optimized for AI-generated interfaces",
    prompt:
      "Create a forward-looking color system optimized for AI-generated interfaces. Design a palette that enhances AI-produced components with visual coherence and polish, elevating machine-generated layouts to appear intentionally designed and professionally refined.",
    icon: Code,
    category: "tech",
    colors: {
      primary: "#2563EB",
      secondary: "#059669",
      accent: "#8B5CF6",
      background: "#F9FAFB",
    },
  },
  {
    title: "Vibe Coding Harmony",
    description: "Versatile palette that enhances AI-generated components",
    prompt:
      "Design a versatile color system that enhances AI-generated components. Create a palette with deliberate relationships between colors that maintain coherence across diverse component patterns, providing a professional polish to rapidly developed interfaces.",
    icon: Code,
    category: "tech",
    colors: {
      primary: "#0F766E",
      secondary: "#7C3AED",
      accent: "#F97316",
      background: "#FFFFFF",
    },
  },

  // Image-Based (New template to highlight this feature)
  {
    title: "Image Extraction",
    description: "Extract and refine a color palette from your uploaded image",
    prompt:
      "Extract a refined color system from the uploaded image. Identify key colors and create harmonious relationships between them, translating the visual essence of the image into a coherent and functional color palette for interfaces.",
    icon: Image, // Assuming you have this icon
    category: "techniques",
    colors: {
      primary: "#3B82F6",
      secondary: "#10B981",
      accent: "#F59E0B",
      background: "#F9FAFB",
    },
  },
];

// Export a combined list of all templates for backwards compatibility
export const allTemplates = [...enhancedTemplates];

// Export the promptTemplates array with a curated selection for the "Try These Prompts" section
export const promptTemplates = enhancedTemplates
  .filter((template, index) => index % 3 === 0)
  .slice(0, 6);
