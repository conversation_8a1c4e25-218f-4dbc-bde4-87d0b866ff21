// Define a minimal type for themes in the store that includes only essential data
type StoreTheme = {
  id?: string; // Database ID
  name: string; // Theme name for display
  format: "hsl" | "oklch"; // Color format
  colors: Record<string, string>; // Light mode colors
  dark_colors?: Record<string, string>; // Optional dark mode colors
};

export const defaultLightTheme: StoreTheme = {
  id: "default-light",
  name: "Default Light",
  format: "oklch",
  colors: {
    background: "oklch(1 0 0)",
    foreground: "oklch(0.145 0 0)",
    card: "oklch(1 0 0)",
    "card-foreground": "oklch(0.145 0 0)",
    popover: "oklch(1 0 0)",
    "popover-foreground": "oklch(0.145 0 0)",
    primary: "oklch(0.7 0.12 178)",
    "primary-foreground": "oklch(0.985 0 0)",
    secondary: "oklch(0.97 0 0)",
    "secondary-foreground": "oklch(0.205 0 0)",
    muted: "oklch(0.97 0 0)",
    "muted-foreground": "oklch(0.556 0 0)",
    accent: "oklch(0.85 0.08 180)",
    "accent-foreground": "oklch(0.205 0 0)",
    destructive: "oklch(0.577 0.245 27.325)",
    border: "oklch(0.922 0 0)",
    input: "oklch(0.922 0 0)",
    ring: "oklch(0.7 0.12 178)",
    radius: "0.625rem",
    // Chart colors
    "chart-1": "oklch(0.7 0.12 178)",
    "chart-2": "oklch(0.6 0.14 160)",
    "chart-3": "oklch(0.65 0.1 195)",
    "chart-4": "oklch(0.75 0.15 145)",
    "chart-5": "oklch(0.68 0.13 210)",
    // Sidebar colors
    sidebar: "oklch(0.985 0 0)",
    "sidebar-foreground": "oklch(0.145 0 0)",
    "sidebar-primary": "oklch(0.7 0.12 178)",
    "sidebar-primary-foreground": "oklch(0.985 0 0)",
    "sidebar-accent": "oklch(0.85 0.08 180)",
    "sidebar-accent-foreground": "oklch(0.205 0 0)",
    "sidebar-border": "oklch(0.922 0 0)",
  },
  dark_colors: {
    background: "oklch(0.145 0 0)",
    foreground: "oklch(0.985 0 0)",
    card: "oklch(0.205 0 0)",
    "card-foreground": "oklch(0.985 0 0)",
    popover: "oklch(0.205 0 0)",
    "popover-foreground": "oklch(0.985 0 0)",
    primary: "oklch(0.75 0.14 178)",
    "primary-foreground": "oklch(0.205 0 0)",
    secondary: "oklch(0.269 0 0)",
    "secondary-foreground": "oklch(0.985 0 0)",
    muted: "oklch(0.269 0 0)",
    "muted-foreground": "oklch(0.708 0 0)",
    accent: "oklch(0.65 0.1 180)",
    "accent-foreground": "oklch(0.985 0 0)",
    destructive: "oklch(0.704 0.191 22.216)",
    border: "oklch(1 0 0 / 10%)",
    input: "oklch(1 0 0 / 15%)",
    ring: "oklch(0.75 0.14 178)",
    // Chart colors
    "chart-1": "oklch(0.75 0.16 178)",
    "chart-2": "oklch(0.7 0.17 150)",
    "chart-3": "oklch(0.72 0.13 200)",
    "chart-4": "oklch(0.8 0.18 140)",
    "chart-5": "oklch(0.75 0.15 215)",
    // Sidebar colors
    sidebar: "oklch(0.205 0 0)",
    "sidebar-foreground": "oklch(0.985 0 0)",
    "sidebar-primary": "oklch(0.75 0.14 178)",
    "sidebar-primary-foreground": "oklch(0.985 0 0)",
    "sidebar-accent": "oklch(0.65 0.1 180)",
    "sidebar-accent-foreground": "oklch(0.985 0 0)",
    "sidebar-border": "oklch(1 0 0 / 10%)",
  },
};

export const defaultDarkTheme: StoreTheme = {
  id: "default-dark",
  name: "Default Dark",
  format: "oklch",
  // For the dark theme, we use the dark_colors as the primary colors
  colors: defaultLightTheme.dark_colors || {},
  dark_colors: defaultLightTheme.dark_colors,
};

export const predefinedThemes: StoreTheme[] = [
  defaultLightTheme,
  defaultDarkTheme,
];
