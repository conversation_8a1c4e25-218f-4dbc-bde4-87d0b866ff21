import { History, Info, Palette, Plus } from "lucide-react";

export const SITE_THEME_STORE_KEY = "chromify-theme-storage";
export const HEADER_NAVIGATION_ITEMS = [
  {
    title: "Generate Colors",
    href: "/theme/create",
    icon: <Palette className="w-4 h-4 mr-2" />,
  },
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: <History className="w-4 h-4 mr-2" />,
    authRequired: true,
  },
  {
    title: "About Us",
    href: "/about",
    icon: <Info className="w-4 h-4 mr-2" />,
  },
  {
    title: "Documentation",
    href: "/docs",
    icon: <Plus className="w-4 h-4 mr-2" />,
  },
];

export const SITE_EMAIL = "<EMAIL>";
