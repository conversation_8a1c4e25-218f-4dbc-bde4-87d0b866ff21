"use client";

import React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import z from "zod";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { Card, CardContent } from "@chromify/ui/components/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@chromify/ui/components/form";
import { Label } from "@chromify/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@chromify/ui/components/radio-group";
import { Separator } from "@chromify/ui/components/separator";
import { Switch } from "@chromify/ui/components/switch";
import { Textarea } from "@chromify/ui/components/textarea";
import { cn } from "@chromify/ui/lib/utils";
import { ColorFormatSchema } from "@/schema/theme";

export type FormSchema = z.infer<typeof formSchema>;

export const formSchema = z.object({
  description: z.string().min(1, "Please provide a theme description"),
  format: ColorFormatSchema,
  includeDarkMode: z.boolean(),
  includeSidebar: z.boolean(),
  includeChart: z.boolean(),
  borderRadius: z.enum(["0", "0.3", "0.5", "0.75", "1"]),
});

interface CreateThemeFormProps {
  onSubmit: (v: FormSchema) => void;
  defaultValues?: Partial<FormSchema>;
  onCancel: () => void;
}

function CreateThemeForm({
  onSubmit,
  defaultValues,
  onCancel,
}: CreateThemeFormProps) {
  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: "",
      format: "hsl",
      includeDarkMode: true,
      includeSidebar: true,
      includeChart: true,
      borderRadius: "0.5",
      ...defaultValues,
    },
  });

  const handleSubmit = async (values: FormSchema) => {
    onSubmit(values);
  };

  return (
    <Card className="w-full relative border-none">
      <CardContent>
        <Form {...form}>
          <form
            className="flex w-full gap-4 flex-col"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            <div className="space-y-4">
              {/* Theme Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Theme Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your theme (mood, colors, industry, etc.)"
                        className="resize-none min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      This description will be used to generate your theme
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Separator />
              {/* Format options - RadioGroup */}
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel>Tailwind Version</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="hsl" id="hsl-format" />
                          <Label
                            htmlFor="hsl-format"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            V3 (HSL Format)
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="oklch" id="oklch-format" />
                          <Label
                            htmlFor="oklch-format"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            V4 (OKLCH Format)
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />
              <Separator />
              <FormField
                control={form.control}
                name="borderRadius"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Border Radius</FormLabel>
                    <div className="flex items-center gap-2 mt-2">
                      {["0", "0.3", "0.5", "0.75", "1"].map((radius) => (
                        <Button
                          key={radius}
                          type="button"
                          variant={
                            field.value === radius ? "default" : "outline"
                          }
                          size="sm"
                          className={cn("flex-1")}
                          style={{
                            borderRadius: `${radius}rem`,
                          }}
                          onClick={() => field.onChange(radius)}
                        >
                          {radius}
                        </Button>
                      ))}
                    </div>
                    <FormDescription className="text-xs mt-2">
                      Controls the roundness of UI elements in the generated
                      theme
                    </FormDescription>
                  </FormItem>
                )}
              />
              <Separator />
              {/* Toggle switches */}
              <FormField
                control={form.control}
                name="includeDarkMode"
                render={({ field }) => (
                  <div className="flex items-center justify-between">
                    <label htmlFor="dark-mode" className="text-sm">
                      Include Dark Mode
                    </label>
                    <Switch
                      id="dark-mode"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="includeSidebar"
                render={({ field }) => (
                  <div className="flex items-center justify-between">
                    <label htmlFor="sidebar" className="text-sm">
                      Include Sidebar
                    </label>
                    <Switch
                      id="sidebar"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name="includeChart"
                render={({ field }) => (
                  <div className="flex items-center justify-between">
                    <label htmlFor="chart" className="text-sm">
                      Include Chart
                    </label>
                    <Switch
                      id="chart"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </div>
                )}
              />
            </div>

            <Button type="submit" aria-label="Create theme">
              submit
            </Button>
            <Button
              type="button"
              variant="outline"
              aria-label="Cancel"
              onClick={onCancel}
            >
              cancel
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

export default CreateThemeForm;
