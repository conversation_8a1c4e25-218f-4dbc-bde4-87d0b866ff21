import { Fragment } from "react";
import type { ChatRequestOptions, Message } from "ai";

import { MessagePart } from "./message-part";

interface ChatMessageProps {
  message: Message;
  addToolResult: (params: { toolCallId: string; result: unknown }) => void;
  toolsRequiringConfirmation: string[];
  handleEditMessage: (messageId: string, newContent: string) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  chatId: string;
}

export const ChatMessage = ({
  message,
  addToolResult,
  toolsRequiringConfirmation,
  handleEditMessage,
  reload,
  chatId,
}: ChatMessageProps) => {
  return (
    <>
      {/* <div className="font-semibold text-sm mb-1">
        {message.role === "user" ? "You" : "AI Assistant"}
      </div> */}
      {message.parts?.map((part, index) => {
        // Create a more unique key that doesn't rely solely on the index
        const uniqueKey = `${message.id}-${part.type}-${index}`;

        return (
          <Fragment key={uniqueKey}>
            <MessagePart
              chatId={chatId}
              reload={reload}
              messageId={message.id}
              role={message.role}
              part={part}
              addToolResult={addToolResult}
              toolsRequiringConfirmation={toolsRequiringConfirmation}
              handleEditMessage={handleEditMessage}
            />
          </Fragment>
        );
      })}
    </>
  );
};
