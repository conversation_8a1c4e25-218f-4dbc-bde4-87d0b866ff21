import { useState } from "react";
import type { ChatRequestOptions, UIMessage } from "ai";

import { MemoizedMarkdown } from "../memoized-markdown";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../tools/tool-handler";
import { EditButton, MessageEditor } from "./edit-message";

interface MessagePartProps {
  part: UIMessage["parts"][number];
  addToolResult: (params: { toolCallId: string; result: unknown }) => void;
  toolsRequiringConfirmation: string[];
  role: "system" | "user" | "assistant" | "data";
  messageId: string;
  handleEditMessage: (messageId: string, newContent: string) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  chatId: string;
}

export const MessagePart = ({
  part,
  addToolResult,
  role,
  messageId,
  handleEditMessage,
  reload,
  chatId,
}: MessagePartProps) => {
  const [activeMessageId, setActiveMessageId] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  switch (part.type) {
    case "text":
      return (
        <>
          {role === "user" && (
            <div
              className="bg-card shadow p-4 rounded-lg relative"
              onMouseEnter={() => setActiveMessageId(messageId)}
              onMouseLeave={() => setActiveMessageId(null)}
            >
              {isEditing ? (
                <MessageEditor
                  messageId={messageId || ""}
                  chatId={chatId}
                  initialText={part.type === "text" ? part.text : ""}
                  onCancel={() => {
                    setIsEditing(false);
                    setActiveMessageId(null);
                  }}
                  onSuccess={(newContent) => {
                    handleEditMessage(messageId || "", newContent);
                    setIsEditing(false);
                    setActiveMessageId(null);
                  }}
                  reload={reload}
                />
              ) : (
                <>
                  <p className="whitespace-no-wrap text-left break-words">
                    {part.text}
                  </p>
                  {activeMessageId === messageId && (
                    <EditButton onClick={() => setIsEditing(true)} />
                  )}
                </>
              )}
            </div>
          )}
          {role === "assistant" && (
            <div className="relative p-4 space-y-4 [&_pre>div]:bg-background [&_pre>div]:rounded-lg [&_pre>div]:border-0.5 [&_pre>div]:border-border [&_.ignore-pre-bg>div]:bg-transparent  [&>div>div>:is(p,blockquote,h1,h2,h3,h4,h5,h6)]:pl-2  [&>div>div>:is(p,blockquote,ul,ol,h1,h2,h3,h4,h5,h6)]:pr-8">
              <MemoizedMarkdown content={part.text} />
            </div>
          )}
        </>
      );

    // for tool invocations, use the ToolHandler component
    case "tool-invocation":
      return (
        <ToolHandler
          toolInvocation={part.toolInvocation}
          addToolResult={addToolResult}
        />
      );

    default:
      return null;
  }
};
