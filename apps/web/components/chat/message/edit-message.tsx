"use client";

import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { Textarea } from "@chromify/ui/components/textarea";

//import { updateMessage } from "@/actions";

interface MessageEditorProps {
  messageId: string;
  chatId: string;
  initialText: string;
  onCancel: () => void;
  onSuccess: (newContent: string) => void;
  reload: () => Promise<string | null | undefined>;
}

export const MessageEditor = ({
  initialText,
  onCancel,
}: MessageEditorProps) => {
  const [editInput, setEditInput] = useState<string>(initialText);
  //const [isPending, startTransition] = useTransition();
  const isPending = false;
  const handleSubmit = () => {
    // startTransition(async () => {
    //   // Update the message in the database and delete subsequent messages
    //   const result = await updateMessage(messageId, editInput, chatId);
    //   if (result.success) {
    //     // Update the message locally
    //     onSuccess(editInput);
    //     // Regenerate AI response
    //     reload();
    //   } else {
    //     console.error("Error updating message:", result.error);
    //     // Show error toast
    //     toast.error("Failed to update message", {
    //       description: result.error,
    //       icon: <AlertCircle className="h-4 w-4 text-red-500" />,
    //       position: "bottom-right",
    //     });
    //   }
    // });
  };

  return (
    <motion.div
      className="space-y-2 p-2"
      initial={{ opacity: 0.8, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Textarea
        autoFocus
        value={editInput}
        onChange={(e) => {
          setEditInput(e.target.value);
        }}
        className="min-h-[100px] resize-none focus-visible:ring-1 focus-visible:ring-primary shadow-sm"
        placeholder="Edit your message..."
        disabled={isPending}
      />
      <div className="flex gap-2 justify-end">
        <Button
          variant={"outline"}
          onClick={onCancel}
          size="sm"
          disabled={isPending}
          className="transition-all"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          size="sm"
          disabled={isPending}
          className="transition-all relative"
        >
          Submit
          {isPending && <Loader2 className="h-3 w-3 animate-spin" />}
        </Button>
      </div>
    </motion.div>
  );
};

export const EditButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{
          duration: 0.2,
          type: "spring",
          stiffness: 500,
          damping: 30,
        }}
        className="absolute -bottom-4 right-2 z-10"
      >
        <Button onClick={onClick} size="sm" className="group/edit">
          Edit
        </Button>
      </motion.div>
    </AnimatePresence>
  );
};
