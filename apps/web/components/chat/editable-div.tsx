import { forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import type { ClipboardEvent, KeyboardEvent } from "react";
import { ArrowUp, Circle } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";
import { FileUploadButton } from "./upload-button";

interface EditableDivProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  placeholder?: string;
  maxHeight?: number;
  className?: string;
  onFileUpload?: (files: File[]) => void;
  onImageUpload?: (files: File[]) => void;
  status: "streaming" | "submitted" | "ready" | "error";
  stop: () => void;
}

export const EditableDiv = forwardRef<HTMLDivElement, EditableDivProps>(
  (
    {
      value,
      onChange,
      onSubmit,
      disabled = false,
      placeholder = "Type a message...",
      maxHeight = 200,
      className,
      onFileUpload,
      onImageUpload,

      status,
      stop,
    },
    ref
  ) => {
    const divRef = useRef<HTMLDivElement>(null);

    // Forward the ref to the parent component
    useImperativeHandle(ref, () => divRef.current as HTMLDivElement);

    // Focus the div when it's mounted
    useEffect(() => {
      if (!disabled && divRef.current) {
        // Only focus if the document doesn't already have focus
        // This prevents stealing focus from other elements
        if (
          !document.activeElement ||
          document.activeElement === document.body
        ) {
          divRef.current.focus();
        }
      }
    }, [disabled]);

    // Sync the content with the value prop
    useEffect(() => {
      if (divRef.current && divRef.current.textContent !== value) {
        divRef.current.textContent = value;
      }
    }, [value]);

    // Handle input changes
    const handleInput = () => {
      if (divRef.current && !disabled) {
        onChange(divRef.current.textContent || "");
      }
    };

    // Handle key presses (Enter to submit, Shift+Enter for new line)
    const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
      if (disabled) return;

      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        onSubmit?.();
      }
    };

    // Handle paste to strip formatting
    const handlePaste = (e: ClipboardEvent<HTMLDivElement>) => {
      if (disabled) return;

      e.preventDefault();
      const text = e.clipboardData.getData("text/plain");
      // Use a safer alternative to execCommand
      if (divRef.current) {
        // Insert text at cursor position
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          selection.deleteFromDocument();
          const textNode = document.createTextNode(text);
          selection.getRangeAt(0).insertNode(textNode);

          // Move cursor to the end of inserted text
          const range = document.createRange();
          range.selectNodeContents(divRef.current);
          range.collapse(false);
          selection.removeAllRanges();
          selection.addRange(range);

          // Trigger input event to update state
          handleInput();
        }
      }
    };

    // Handle file uploads
    const handleFileUpload = (fileList: FileList) => {
      const newFiles = Array.from(fileList);
      if (onFileUpload) {
        onFileUpload(newFiles);
      }
    };

    // Handle image uploads
    const handleImageUpload = (fileList: FileList) => {
      const newFiles = Array.from(fileList);
      if (onImageUpload) {
        onImageUpload(newFiles);
      }
    };

    return (
      <div className="flex flex-col w-full">
        <div
          ref={divRef}
          contentEditable={!disabled}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          className={cn(
            "outline-none w-full min-h-[40px] overflow-y-auto",
            "empty:before:content-[attr(data-placeholder)] empty:before:text-muted-foreground empty:before:opacity-60",
            "focus:ring-0 focus:ring-offset-0",
            "whitespace-pre-wrap break-words",
            "bg-transparent",
            disabled && "opacity-70 cursor-not-allowed",
            className
          )}
          data-placeholder={placeholder}
          style={{ maxHeight }}
          role="textbox"
          aria-multiline="true"
          aria-label="Message input"
          tabIndex={disabled ? -1 : 0}
        />

        {/* Action buttons */}
        <div className="flex items-center justify-between mt-1 px-2">
          <div className="flex items-center gap-2">
            <FileUploadButton
              type="image"
              onUpload={handleImageUpload}
              disabled={disabled}
            />
            <FileUploadButton
              type="file"
              onUpload={handleFileUpload}
              disabled={disabled}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              size="icon"
              onClick={() => onSubmit?.()}
              disabled={disabled || !value.trim()}
              className="h-9 w-9"
            >
              <ArrowUp className="h-4 w-4" />
            </Button>

            <Button
              disabled={status === "ready"}
              type="button"
              variant="destructive"
              size="icon"
              onClick={stop}
            >
              <Circle className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }
);
