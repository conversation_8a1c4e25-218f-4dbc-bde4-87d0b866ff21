"use client";

import { useState, type FormEvent } from "react";
import type { ChatRequestOptions } from "ai";

import { cn } from "@chromify/ui/lib/utils";
import { ACCEPT_FILE_TYPES } from "@/constants/chat";
import { EditableDiv } from "./editable-div";
import { FileContainer } from "./file-container";

interface ChatInputProps {
  input: string;
  handleInputChange: (value: string) => void;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions
  ) => void;
  status: "streaming" | "submitted" | "ready" | "error";
  stop: () => void;
  disabled?: boolean;
  className?: string;
}

function ChatInput({
  input,
  handleInputChange,
  handleSubmit,
  status,
  stop,
  disabled,
  className,
}: ChatInputProps) {
  const isLoading = status === "streaming" || status === "submitted";
  const [files, setFiles] = useState<File[]>([]);
  const [fileListRef, setFileListRef] = useState<FileList | undefined>(
    undefined
  );

  const onSubmit = (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault?.();

    if (!isLoading && (input.trim() || files.length > 0)) {
      // Create a synthetic event if none was provided
      const syntheticEvent =
        e || ({ preventDefault: () => {} } as FormEvent<HTMLFormElement>);

      // Submit with file attachments
      handleSubmit(syntheticEvent, {
        experimental_attachments: fileListRef,
      });

      // Clear uploaded files after submission
      setFiles([]);
      setFileListRef(undefined);

      // Files have been cleared
    }
  };

  // Handle file removal
  const handleRemoveFile = (fileToRemove: File) => {
    // Remove the file from the array
    const updatedFiles = files.filter((file) => file !== fileToRemove);
    setFiles(updatedFiles);

    // Update FileList
    if (updatedFiles.length === 0) {
      setFileListRef(undefined);
    } else {
      const dataTransfer = new DataTransfer();
      for (const file of updatedFiles) {
        dataTransfer.items.add(file);
      }
      setFileListRef(dataTransfer.files);
    }
  };

  // We've removed the direct file input since we're using the buttons in EditableDiv

  // Handle file uploads from EditableDiv
  const handleFileUpload = (newFiles: File[]) => {
    // Filter files based on accepted types
    const acceptedExtensions = ACCEPT_FILE_TYPES.split(",");

    const filteredFiles = newFiles.filter((file) => {
      // Check if file type is in the accepted list
      const fileExt = `.${file.name.split(".").pop()?.toLowerCase()}`;
      return acceptedExtensions.includes(fileExt);
    });

    if (filteredFiles.length > 0) {
      // Update the files array
      const updatedFiles = [...files, ...filteredFiles];
      setFiles(updatedFiles);

      // Convert to FileList
      const dataTransfer = new DataTransfer();
      for (const file of updatedFiles) {
        dataTransfer.items.add(file);
      }
      setFileListRef(dataTransfer.files);
    }
  };

  // Handle image uploads from EditableDiv
  const handleImageUpload = (newFiles: File[]) => {
    // Filter for image files only
    const imageFiles = newFiles.filter((file) =>
      file.type.startsWith("image/")
    );

    if (imageFiles.length > 0) {
      // Update the files array
      const updatedFiles = [...files, ...imageFiles];
      setFiles(updatedFiles);

      // Convert to FileList
      const dataTransfer = new DataTransfer();
      for (const file of updatedFiles) {
        dataTransfer.items.add(file);
      }
      setFileListRef(dataTransfer.files);
    }
  };

  return (
    <form onSubmit={onSubmit} className={cn("flex w-full flex-col", className)}>
      <div className="border border-input bg-background rounded-lg flex flex-col gap-2 w-full shadow-sm">
        <div className="p-4">
          <EditableDiv
            value={input}
            onChange={handleInputChange}
            disabled={isLoading || disabled}
            placeholder={isLoading ? "AI is thinking..." : "Type a message..."}
            className="mb-2"
            onSubmit={() => {
              if (!isLoading && (input.trim() || files.length > 0)) {
                onSubmit();
              }
            }}
            onFileUpload={handleFileUpload}
            onImageUpload={handleImageUpload}
            status={status}
            stop={stop}
          />

          {/* File container */}
          <FileContainer
            files={files}
            onRemoveFile={handleRemoveFile}
            maxFiles={5}
          />
        </div>
      </div>
    </form>
  );
}

export default ChatInput;
