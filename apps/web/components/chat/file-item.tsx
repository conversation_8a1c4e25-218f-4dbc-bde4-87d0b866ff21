import { useEffect, useState } from "react";
import Image from "next/image";
import { Eye, FileText, X } from "lucide-react";

import { cn } from "@chromify/ui/lib/utils";

interface FileItemProps {
  file: File;
  onRemove: (file: File) => void;
  onPreview: (file: File) => void;
  className?: string;
}

export function FileItem({
  file,
  onRemove,
  onPreview,
  className,
}: FileItemProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const isImage = file.type.startsWith("image/");

  // Simulate loading delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Create preview URL for images
  useEffect(() => {
    if (!isImage) return;

    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  }, [file, isImage]);

  return (
    <div
      className={cn(
        "relative group rounded-md border bg-background",
        "h-24 w-24 flex flex-col overflow-hidden",
        isLoading && "animate-pulse",
        className
      )}
    >
      {/* Preview overlay button */}
      <button
        type="button"
        onClick={() => onPreview(file)}
        className="absolute inset-0 bg-black/0 group-hover:bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all z-10"
        aria-label="Preview file"
      >
        <Eye className="h-5 w-5 text-white" />
      </button>

      {/* Remove button */}
      <button
        type="button"
        onClick={() => onRemove(file)}
        className="absolute top-1 right-1 rounded-full bg-background/80 p-1 opacity-0 group-hover:opacity-100 transition-opacity z-20"
        aria-label="Remove file"
      >
        <X className="h-3 w-3" />
      </button>

      {/* File preview */}
      <div className="flex-1 flex items-center justify-center overflow-hidden">
        {isImage && previewUrl ? (
          <div className="relative h-full w-full">
            <Image
              src={previewUrl}
              alt={file.name}
              fill
              className="object-cover"
            />
            {isLoading && (
              <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
                <div className="h-5 w-5 rounded-full border-2 border-primary border-t-transparent animate-spin" />
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-muted/30">
            {isLoading ? (
              <div className="h-5 w-5 rounded-full border-2 border-primary border-t-transparent animate-spin" />
            ) : (
              <FileText className="h-8 w-8 text-muted-foreground" />
            )}
          </div>
        )}
      </div>

      {/* File info */}
      <div className="p-1 bg-muted/20 border-t text-center">
        <p className="text-xs font-medium truncate">
          {file.name.split(".").pop()?.toUpperCase() || "FILE"}
        </p>
      </div>
    </div>
  );
}
