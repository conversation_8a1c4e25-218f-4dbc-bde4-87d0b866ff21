import { useEffect, useState } from "react";
import Image from "next/image";

import { Button } from "@chromify/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@chromify/ui/components/dialog";

interface FilePreviewDialogProps {
  file: File | null;
  isOpen: boolean;
  onClose: () => void;
}

export function FilePreviewDialog({
  file,
  isOpen,
  onClose,
}: FilePreviewDialogProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const isImage = file?.type.startsWith("image/");
  const isPdf = file?.type === "application/pdf";

  useEffect(() => {
    if (!file) return;

    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  }, [file]);

  if (!file || !previewUrl) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="truncate max-w-[90%]">{file.name}</span>
          </DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          {isImage ? (
            <div className="relative w-full h-[60vh] bg-muted/30 rounded-md overflow-hidden">
              <Image
                src={previewUrl}
                alt={file.name}
                fill
                className="object-contain"
              />
            </div>
          ) : isPdf ? (
            <iframe
              src={previewUrl}
              className="w-full h-[70vh] rounded-md"
              title={file.name}
            />
          ) : (
            <div className="flex flex-col items-center justify-center p-8 bg-muted/30 rounded-md">
              <div className="text-4xl font-bold mb-2">
                {file.name.split(".").pop()?.toUpperCase()}
              </div>
              <p className="text-muted-foreground">
                {file.type || "Unknown file type"}
              </p>
              <Button className="mt-4" asChild>
                <a href={previewUrl} download={file.name}>
                  Download
                </a>
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
