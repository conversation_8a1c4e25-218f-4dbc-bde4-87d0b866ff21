import { BaseColors } from "@/schema/theme";
import ColorSwatch from "./color-swatch";

function BaseColorsPalette(props: BaseColors) {
  const colors = props.colors;
  const darkColors = props.dark_colors;
  if (!colors || !darkColors) return null;
  return (
    <div>
      <div className="font-bold text-primary mb-4">Base Colors</div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-10">
        <div>
          <div className="text-sm font-medium mb-2">Light</div>
          <div className="grid grid-cols-2 gap-4">
            <ColorSwatch
              colorName="background"
              colorValue={colors.background}
            />
            <ColorSwatch
              colorName="foreground"
              colorValue={colors.foreground}
            />
            <ColorSwatch colorName="primary" colorValue={colors.primary} />
            <ColorSwatch
              colorName="primary-foreground"
              colorValue={colors["primary-foreground"]}
            />
          </div>
        </div>
        {darkColors && (
          <div>
            <div className="font-medium text-sm mb-2">Dark</div>
            <div className="grid grid-cols-2 gap-4">
              <ColorSwatch
                colorName="background"
                colorValue={darkColors.background}
              />
              <ColorSwatch
                colorName="foreground"
                colorValue={darkColors.foreground}
              />
              <ColorSwatch
                colorName="primary"
                colorValue={darkColors.primary}
              />
              <ColorSwatch
                colorName="primary-foreground"
                colorValue={darkColors["primary-foreground"]}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default BaseColorsPalette;
