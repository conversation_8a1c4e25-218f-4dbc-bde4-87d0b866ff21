const ColorSwatch = ({
  colorName,
  colorValue,
}: {
  colorName: string;
  colorValue: string;
}) => {
  return (
    <div className="border rounded-md">
      <div
        className="h-12 w-full border-b border-border rounded-t-md"
        style={{ backgroundColor: colorValue }}
      />
      <div className="p-1.5 space-y-1.5 font-mono text-xs">
        <div className="font-medium">{colorName}</div>
        <div className="text-muted-foreground">{colorValue}</div>
      </div>
    </div>
  );
};
export default ColorSwatch;
