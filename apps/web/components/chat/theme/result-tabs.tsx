"use client";

import { useState } from "react";
import { MoonIcon, SunIcon } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@chromify/ui/components/tabs";
import GeneratedCSS from "@/components/css-copy";
import { useTheme } from "@/components/theme-provider";
import { generateThemeCss } from "@/lib/utils";
import { AITheme } from "@/schema/theme";
import ColorSwatch from "./color-swatch";

// Colors tab content
const ColorsTab = ({ colors }: { colors: Record<string, string> }) => {
  if (!colors) return null;
  // Filter out radius as it's not a color
  const colorEntries = Object.entries(colors).filter(
    ([key]) => key !== "radius"
  );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {colorEntries.map(([key, value]) => (
          <ColorSwatch key={key} colorName={key} colorValue={value} />
        ))}
      </div>
    </div>
  );
};

// Main component
function ResultTabs({ theme }: { theme: AITheme }) {
  const [activeTab, setActiveTab] = useState("colors");
  const { applyTheme } = useTheme();
  const colors = theme?.colors;
  const dark_colors = theme?.dark_colors;
  // Check if dark colors exist
  const hasDarkColors = !!dark_colors && Object.keys(dark_colors).length > 0;

  return (
    <Tabs
      defaultValue="colors"
      className="w-full relative"
      onValueChange={setActiveTab}
      value={activeTab}
    >
      <div className="flex justify-between mb-4">
        <TabsList>
          <TabsTrigger value="colors">Light Colors</TabsTrigger>
          {hasDarkColors && (
            <TabsTrigger value="dark-colors">Dark Colors</TabsTrigger>
          )}
          <TabsTrigger value="css">CSS</TabsTrigger>
        </TabsList>
        <div className="flex space-x-2">
          <Button size="icon" onClick={() => applyTheme(theme, "light")}>
            <SunIcon />
          </Button>
          {hasDarkColors && (
            <Button size="icon" onClick={() => applyTheme(theme, "dark")}>
              <MoonIcon />
            </Button>
          )}
        </div>
      </div>

      <TabsContent value="colors" className="space-y-4">
        <ColorsTab colors={colors} />
      </TabsContent>

      {hasDarkColors && (
        <TabsContent value="dark-colors" className="space-y-4">
          <ColorsTab colors={dark_colors} />
        </TabsContent>
      )}

      <TabsContent value="css" className="space-y-4">
        <GeneratedCSS
          css={generateThemeCss({
            format: theme.format,
            colors: theme.colors,
            dark_colors: theme.dark_colors,
          })}
        />
      </TabsContent>
    </Tabs>
  );
}

export default ResultTabs;
