import { useRef } from "react";
import { FileUp, Image } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

interface FileUploadButtonProps {
  type: "image" | "file";
  onUpload: (files: FileList) => void;
  className?: string;
  disabled?: boolean;
}

export function FileUploadButton({
  type,
  onUpload,
  className,
  disabled = false,
}: FileUploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onUpload(e.target.files);
      // Reset the input value so the same file can be uploaded again
      e.target.value = "";
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("relative", className)}>
      <input
        ref={fileInputRef}
        type="file"
        className="sr-only" // Use sr-only instead of absolute positioning
        onChange={handleFileChange}
        accept={type === "image" ? "image/*" : undefined}
        disabled={disabled}
        aria-label={`Upload ${type}`}
        id={`upload-${type}`}
        multiple
      />
      <Button
        type="button"
        size="icon"
        className="size-8 p-0.5 bg-transparent text-foreground hover:bg-transparent border cursor-pointer"
        //   disabled={disabled}
        disabled
        onClick={handleButtonClick} // Trigger file input click
        aria-controls={`upload-${type}`}
        title={`Upload ${
          type === "image" ? "images" : "files"
        } (multiple allowed)`}
      >
        {type === "image" ? (
          <Image className="h-4 w-4" />
        ) : (
          <FileUp className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
