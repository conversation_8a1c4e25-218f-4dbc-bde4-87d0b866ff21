import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle } from "lucide-react";

import { cn } from "@chromify/ui/lib/utils";
import { FileItem } from "./file-item";
import { FilePreviewDialog } from "./file-preview";

interface FileContainerProps {
  files: File[];
  onRemoveFile: (file: File) => void;
  className?: string;
  maxFiles?: number;
}

export function FileContainer({
  files,
  onRemoveFile,
  className,
  maxFiles = 5,
}: FileContainerProps) {
  const [previewFile, setPreviewFile] = useState<File | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const handlePreview = (file: File) => {
    setPreviewFile(file);
    setIsPreviewOpen(true);
  };

  const exceedsLimit = files.length > maxFiles;

  return (
    <>
      <FilePreviewDialog
        file={previewFile}
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
      />

      {files.length > 0 && (
        <motion.div
          className={cn("border-t mt-2 pt-2", className)}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          {exceedsLimit && (
            <div className="flex items-center gap-2 text-xs text-amber-500 mb-2">
              <AlertCircle className="h-3 w-3" />
              <span>
                Maximum {maxFiles} files allowed. Some files won't be uploaded.
              </span>
            </div>
          )}

          <div className="flex flex-wrap gap-2 px-2">
            <AnimatePresence>
              {files.slice(0, maxFiles).map((file, index) => (
                <motion.div
                  key={`${file.name}-${index}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  <FileItem
                    file={file}
                    onRemove={onRemoveFile}
                    onPreview={handlePreview}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </>
  );
}
