"use client";

import type { ToolInvocation } from "ai";
import { Check, X } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { GlowingEffect } from "@chromify/ui/components/glowing-effect";
import { TOOL_RESPONSE } from "@/lib/ai/tools/client-utils";

interface ConfirmBaseColorsToolProps {
  toolInvocation: ToolInvocation;
  addToolResult: (params: { toolCallId: string; result: unknown }) => void;
}

export const ConfirmBaseColorsTool = ({
  toolInvocation,
  addToolResult,
}: ConfirmBaseColorsToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "call":
      return (
        <div className="p-4 my-2 border rounded-lg relative">
          <div className="flex gap-2">
            <Button
              onClick={() => {
                // Send structured approval response
                addToolResult({
                  toolCallId: callId,
                  result: {
                    status: "approved",
                    message:
                      "User approved the base colors and wants to proceed with theme generation.",
                    baseColors: toolInvocation.args?.baseColors || {},
                    themeConfig: toolInvocation.args?.themeConfig || {},
                  },
                });
              }}
              className="flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Yes, continue
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                addToolResult({
                  toolCallId: callId,
                  result: TOOL_RESPONSE.DENIED,
                });
              }}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              No, let's refine
            </Button>
          </div>
          <GlowingEffect />
        </div>
      );
    case "result":
      if (toolInvocation.result === "user_declined") {
        return (
          <div className="p-4 my-2 border rounded-lg relative">
            <p className="text-sm">User declined to confirm the base colors.</p>
            <GlowingEffect />
          </div>
        );
      }
      if (toolInvocation.result === "approved") {
        return (
          <div className="p-4 my-2 border rounded-lg relative">
            <p className="text-sm">User confirmed the base colors.</p>
            <GlowingEffect />
          </div>
        );
      }
      break;
    default:
      return null;
  }
};
