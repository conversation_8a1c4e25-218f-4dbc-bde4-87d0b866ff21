import type { ToolInvocation } from "ai";

interface GenericToolProps {
  toolInvocation: ToolInvocation;
}

export const GenericTool = ({ toolInvocation }: GenericToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div key={callId} className="p-2 my-1 border rounded-md">
          <div className="font-medium">
            Tool Call: {toolInvocation.toolName}
          </div>
          <pre className="text-xs overflow-x-auto p-1 rounded mt-1">
            {JSON.stringify(toolInvocation, null, 2)}
          </pre>
        </div>
      );
    case "call":
      return (
        <div key={callId} className="p-2 my-1 border rounded-md">
          <div className="font-medium">
            Tool Call: {toolInvocation.toolName}
          </div>
          <pre className="text-xs overflow-x-auto p-1 rounded mt-1">
            {JSON.stringify(toolInvocation.args, null, 2)}
          </pre>
        </div>
      );
    case "result":
      return (
        <div key={callId} className="p-2 my-1 border rounded-md">
          <div className="font-medium">
            Tool Result: {toolInvocation.toolName}
          </div>
          <pre className="text-xs overflow-x-auto p-1 rounded mt-1">
            {JSON.stringify(toolInvocation.result, null, 2)}
          </pre>
        </div>
      );
    default:
      return null;
  }
};
