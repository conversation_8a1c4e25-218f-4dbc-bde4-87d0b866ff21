"use client";

import { useEffect } from "react";
import type { ToolInvocation } from "ai";

import { AnimatedBorder } from "@chromify/ui/components/animated-border";
import { GlowingEffect } from "@chromify/ui/components/glowing-effect";
import ShimmerText from "@/components/shimmer-text";
import { useThemeStore } from "@/store/theme-store";
import ResultTabs from "../theme/result-tabs";

interface ExtendBaseColorsToolProps {
  toolInvocation: ToolInvocation;
}

export const ExtendBaseColorsTool = ({
  toolInvocation,
}: ExtendBaseColorsToolProps) => {
  const callId = toolInvocation.toolCallId;
  const { addCustomTheme } = useThemeStore();

  // Store the theme in the theme store when it's generated
  useEffect(() => {
    if (toolInvocation.state === "result" && "result" in toolInvocation) {
      // Store the full theme in the theme store
      // This will be used by the preview tool
      if (toolInvocation.result) {
        const themeData = toolInvocation.result;
        // Create a minimal store theme with only essential data
        const storeTheme = {
          id: `theme-${Date.now()}`, // Generate a unique ID
          name: themeData.name || "Extended Theme",
          format: themeData.format,
          colors: themeData.colors,
          dark_colors: themeData.dark_colors,
        };

        // Add to custom themes
        addCustomTheme(storeTheme);
        // toast.success("Full theme generated!");
      }
    }
  }, [toolInvocation.state, toolInvocation, addCustomTheme]);

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div className="p-4 rounded-md relative flex items-center">
          <ShimmerText key={callId}>
            Preparing to extend base colors...
          </ShimmerText>
          <AnimatedBorder />
        </div>
      );
    case "call":
      return (
        <div className="p-4 rounded-md relative flex items-center">
          <ShimmerText key={callId}>
            Creating full theme from base colors...
          </ShimmerText>
          <AnimatedBorder
            spread={60}
            borderWidth={1.2}
            animationDuration={3.6}
            variant="default"
            glow={true}
            className="h-full"
          />
        </div>
      );
    case "result":
      return (
        <div className="relative rounded-lg border p-4">
          <ResultTabs
            theme={toolInvocation.result.object ?? toolInvocation.result}
          />
          <GlowingEffect />
        </div>
      );
    default:
      return null;
  }
};
