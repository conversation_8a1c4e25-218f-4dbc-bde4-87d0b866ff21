import type { ToolInvocation } from "ai";

import { AnimatedBorder } from "@chromify/ui/components/animated-border";
import { GlowingEffect } from "@chromify/ui/components/glowing-effect";
import ShimmerText from "@/components/shimmer-text";
import CreateThemeForm from "../message/create-form";

interface FillFormToolProps {
  toolInvocation: ToolInvocation;
  addToolResult: (params: {
    toolCallId: string;
    result: Record<string, unknown>;
  }) => void;
}

export const FillFormTool = ({
  toolInvocation,
  addToolResult,
}: FillFormToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div className="p-4 rounded-md relative flex items-center my-4">
          <ShimmerText key={callId}>Preparing theme config form...</ShimmerText>
          <AnimatedBorder
            spread={60}
            borderWidth={1.2}
            animationDuration={3.6}
            variant="default"
            glow={true}
            className="h-full"
          />
        </div>
      );
    case "call": {
      // Get the theme description from the args if provided, or extract from message
      let initialDescription = "";

      if (toolInvocation.args.description) {
        // Use the description provided directly in the tool args
        initialDescription = toolInvocation.args.description;
      } else {
        // Try to extract description from the message
        const messageText = toolInvocation.args.message || "";
        const descriptionMatch = messageText.match(/Description:\s*([^\n]+)/i);
        if (descriptionMatch) {
          initialDescription = descriptionMatch[1].trim();
        }
      }

      // Create a defaultValues object with the extracted description
      const defaultValues = {
        description: initialDescription,
        format: "hsl" as const,
        includeDarkMode: true,
        includeSidebar: true,
        includeChart: true,
        borderRadius: "0.5" as const,
      };

      return (
        <div
          key={callId}
          className="my-4 p-4 space-y-2 rounded-md border relative"
        >
          <div className="font-bold text-lg">Theme Config</div>
          <p className="text-sm mb-3">{toolInvocation.args.message}</p>
          <CreateThemeForm
            onSubmit={(formValues) => {
              // Send a more comprehensive result to ensure the AI gets all the data
              addToolResult({
                toolCallId: callId,
                result: {
                  status: "form_submitted",
                  data: formValues,
                  message: "User submitted the form successfully",
                  // Include a formatted summary for the AI to reference
                  summary: `Theme configuration: ${
                    formValues.description
                  }, Format: ${formValues.format}, Dark Mode: ${
                    formValues.includeDarkMode ? "Yes" : "No"
                  }, Border Radius: ${formValues.borderRadius}rem`,
                  // Include a flag to indicate this should proceed to the next step
                  shouldCreateBaseColors: true,
                  // Include individual fields for easier access by the AI
                  description: formValues.description,
                  format: formValues.format,
                  includeDarkMode: formValues.includeDarkMode,
                  includeSidebar: formValues.includeSidebar,
                  includeChart: formValues.includeChart,
                  borderRadius: formValues.borderRadius,
                },
              });
            }}
            defaultValues={defaultValues}
            onCancel={() =>
              addToolResult({
                toolCallId: callId,
                result: {
                  status: "form_cancelled",
                  message: "User declined to fill the form",
                },
              })
            }
          />
          <GlowingEffect />
        </div>
      );
    }
    case "result":
      // Handle different result statuses
      if (
        toolInvocation.result?.status === "form_cancelled" ||
        toolInvocation.result?.status === "user_declined"
      ) {
        return (
          <div
            className="relative rounded-lg border border-destructive p-4 my-4"
            key={callId}
          >
            <div className="font-bold mb-4 text-destructive">Form Declined</div>
            <p className="text-sm text-destructive/70">
              {toolInvocation.result?.message ||
                "User declined to fill the form"}
            </p>
          </div>
        );
      }

      // For completed forms, show the form data
      {
        const formData = toolInvocation.result?.data || toolInvocation.result;

        return (
          <div className="relative rounded-lg border p-4 my-4">
            <div className="font-bold mb-4 text-primary">
              Theme Configuration
            </div>
            <div className="my-1">
              <div className="text-sm space-y-1">
                <div className="border-b pb-2 mb-2">
                  <span>Description:</span>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {formData?.description}
                  </p>
                </div>
                <div className="flex flex-wrap gap-5">
                  <div className="border-r pr-3">
                    Format:{" "}
                    <span className="text-muted-foreground">
                      {JSON.stringify(formData?.format)}
                    </span>
                  </div>

                  <div className="border-r pr-3">
                    Dark Mode:{" "}
                    <span className="text-muted-foreground">
                      {formData?.includeDarkMode ? "Yes" : "No"}
                    </span>
                  </div>

                  <div className="border-r pr-3">
                    Sidebar:{" "}
                    <span className="text-muted-foreground">
                      {formData?.includeSidebar ? "Yes" : "No"}
                    </span>
                  </div>

                  <div className="border-r pr-3">
                    Chart:{" "}
                    <span className="text-muted-foreground">
                      {formData?.includeChart ? "Yes" : "No"}
                    </span>
                  </div>

                  <div>
                    Border Radius:{" "}
                    <span className="text-muted-foreground">
                      {formData?.borderRadius}rem
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <GlowingEffect />
          </div>
        );
      }
    default:
      return null;
  }
};
