import type { ToolInvocation } from "ai";

import { Button } from "@chromify/ui/components/button";

interface ConfirmationToolProps {
  toolInvocation: ToolInvocation;
  addToolResult: (params: { toolCallId: string; result: string }) => void;
}

export const ConfirmationTool = ({
  toolInvocation,
  addToolResult,
}: ConfirmationToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div key={callId} className="p-2 my-1 border rounded-md">
          <div className="font-medium text-yellow-800">
            Preparing confirmation request...
          </div>
          <pre className="text-xs overflow-x-auto p-1 rounded mt-1">
            {JSON.stringify(toolInvocation, null, 2)}
          </pre>
        </div>
      );
    case "call":
      return (
        <div key={callId} className="p-2 my-1 border rounded-md">
          <div className="font-medium">Confirmation Required</div>
          <p className="my-1">{toolInvocation.args.message}</p>
          <div className="flex gap-2 mt-2">
            <Button
              onClick={() =>
                addToolResult({
                  toolCallId: callId,
                  result: "Yes, confirmed.",
                })
              }
            >
              Yes
            </Button>
            <Button
              onClick={() =>
                addToolResult({
                  toolCallId: callId,
                  result: "No, denied",
                })
              }
            >
              No
            </Button>
          </div>
        </div>
      );
    case "result":
      return (
        <div
          key={callId}
          className="p-2 my-1 border border-green-200 rounded-md"
        >
          <div className="font-medium">Confirmation Response</div>
          <p className="my-1">User response: {toolInvocation.result}</p>
        </div>
      );
    default:
      return null;
  }
};
