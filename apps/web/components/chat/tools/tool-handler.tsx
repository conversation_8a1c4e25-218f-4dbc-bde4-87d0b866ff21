import type { ToolInvocation } from "ai";

import { BaseColorTool } from "./base-color-tool";
import { ConfirmBaseColorsTool } from "./confirm-base-colors-tool";
import { ExtendBaseColorsTool } from "./extend-base-colors-tool";
import { FillFormTool } from "./fill-form-tool";
import { GenericTool } from "./generic-tool";

interface ToolHandlerProps {
  toolInvocation: ToolInvocation;
  addToolResult: (params: { toolCallId: string; result: unknown }) => void;
}

export const ToolHandler = ({
  toolInvocation,
  addToolResult,
}: ToolHandlerProps) => {
  switch (toolInvocation.toolName) {
    case "askToFillForm":
      return (
        <FillFormTool
          toolInvocation={toolInvocation}
          addToolResult={addToolResult}
        />
      );

    case "createBaseColors":
      return <BaseColorTool toolInvocation={toolInvocation} />;

    case "confirmBaseColors":
      return (
        <ConfirmBaseColorsTool
          toolInvocation={toolInvocation}
          addToolResult={addToolResult}
        />
      );
    case "extendBaseColors":
      return <ExtendBaseColorsTool toolInvocation={toolInvocation} />;

    default:
      return <GenericTool toolInvocation={toolInvocation} />;
  }
};
