import type { ToolInvocation } from "ai";

import { AnimatedBorder } from "@chromify/ui/components/animated-border";
import { GlowingEffect } from "@chromify/ui/components/glowing-effect";
import ShimmerText from "@/components/shimmer-text";
import BaseColorsPalette from "../theme/base-colors-palette";

interface BaseColorToolProps {
  toolInvocation: ToolInvocation;
}

export const BaseColorTool = ({ toolInvocation }: BaseColorToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div className="p-4 rounded-md relative flex items-center my-4">
          <ShimmerText key={callId}>
            Preparing to generate base colors...
          </ShimmerText>
          <AnimatedBorder />
        </div>
      );
    case "call":
      return (
        <div className="p-4 rounded-md relative flex items-center my-4">
          <ShimmerText key={callId}>Designing base colors...</ShimmerText>
          <AnimatedBorder />
        </div>
      );
    case "result": {
      if (toolInvocation.result.error) {
        return (
          <div className="relative rounded-lg border p-4 my-4">
            <p className="text-sm text-destructive/70">
              Error: {toolInvocation.result.message}
            </p>
          </div>
        );
      }

      return (
        <div className="relative rounded-lg border p-4 my-4">
          <BaseColorsPalette {...toolInvocation.result.object} />
          <GlowingEffect />
        </div>
      );
    }

    default:
      return null;
  }
};
