import type { ToolInvocation } from "ai";

import { AnimatedBorder } from "@chromify/ui/components/animated-border";
import { GlowingEffect } from "@chromify/ui/components/glowing-effect";
import ShimmerText from "@/components/shimmer-text";
import ResultTabs from "../theme/result-tabs";

interface CreateThemeProps {
  toolInvocation: ToolInvocation;
}

export const CreateThemeTool = ({ toolInvocation }: CreateThemeProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "partial-call":
      return (
        <div key={callId}>
          Building theme request...
          <pre className="text-xs overflow-x-auto p-1 rounded">
            {JSON.stringify(toolInvocation, null, 2)}
          </pre>
        </div>
      );
    case "call":
      return (
        <div className="p-4 rounded-md relative flex items-center">
          <ShimmerText>Crafting your custom theme...</ShimmerText>
          <AnimatedBorder
            spread={60}
            borderWidth={1.2}
            animationDuration={3.6}
            variant="default"
            glow={true}
            className="h-full"
          />
        </div>
      );
    case "result":
      return (
        <div className="relative rounded-lg border p-4 my-4">
          <ResultTabs
            theme={toolInvocation.result.object ?? toolInvocation.result}
          />
          <GlowingEffect />
        </div>
      );
    default:
      return null;
  }
};
