import type { ToolInvocation } from "ai";

import { AnimatedBorder } from "@chromify/ui/components/animated-border";
import ShimmerText from "@/components/shimmer-text";

interface WebSearchToolProps {
  toolInvocation: ToolInvocation;
}

export const WebSearchTool = ({ toolInvocation }: WebSearchToolProps) => {
  const callId = toolInvocation.toolCallId;

  switch (toolInvocation.state) {
    case "call":
      return (
        <div className="p-4 rounded-md relative flex items-center">
          <ShimmerText key={callId}>
            Searching web about {toolInvocation.args.query}...
          </ShimmerText>
          <AnimatedBorder
            spread={60}
            borderWidth={1.2}
            animationDuration={3.6}
            variant="default"
            glow={true}
            className="h-full"
          />
        </div>
      );

    default:
      return null;
  }
};
