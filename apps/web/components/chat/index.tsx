"use client";

import { useCallback, useEffect, type ChangeEvent } from "react";
import { useChat, type Message } from "@ai-sdk/react";
import { createIdGenerator } from "ai";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { StickToBottom } from "use-stick-to-bottom";

import { CHAT_MAX_STEPS } from "@/constants/chat";
import { tools } from "@/lib/ai/tools";
import { getToolsRequiringConfirmation } from "@/lib/ai/tools/client-utils";
import { useChatStore } from "@/store/chat-store";
import ChatInput from "./chat-input";
import { ChatMessage } from "./message/chat-message";
import ScrollToBottom from "./scroll-bottom";

export const Chat = ({
  id,
  initialMessages,
}: {
  id?: string | undefined;
  initialMessages?: Message[];
}) => {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    // error,
    reload,
    stop,
    addToolResult,
    setMessages,
    data,
  } = useChat({
    api: "/api/chat",
    id, // use the provided chat ID
    initialMessages, // initial messages if provided
    maxSteps: CHAT_MAX_STEPS,
    sendExtraMessageFields: true, // send id and createdAt for each message
    // id format for client-side messages:
    generateId: createIdGenerator({
      prefix: "msgc",
      size: 16,
    }),
    // only send the last message to the server:
    experimental_prepareRequestBody({ messages, id }) {
      return { message: messages[messages.length - 1], id };
    },
    experimental_throttle: 100,
    onError: (error) => {
      // Check if it's a timeout or model overload error
      const isTimeoutError =
        error.message?.includes("timeout") ||
        error.message?.includes("overloaded") ||
        error.message?.includes("AI_RetryError");

      toast.error(
        isTimeoutError
          ? "The AI service is currently busy. Your conversation will be recovered automatically."
          : "Error occurred, please try again.",
        {
          description: error.message,
        }
      );

      // The recovery will be handled by the useEffect that watches status
    },
    onFinish: () => {},
  });

  const toolsRequiringConfirmation = getToolsRequiringConfirmation(tools);

  // used to disable input while confirmation is pending
  const pendingToolCallConfirmation = messages.some((m: Message) =>
    m.parts?.some(
      (part) =>
        part.type === "tool-invocation" &&
        part.toolInvocation.state === "call" &&
        toolsRequiringConfirmation.includes(part.toolInvocation.toolName)
    )
  );

  // Get chat store actions
  const { updateChatTitle, setCurrentChatId } = useChatStore();

  // Set current chat ID when component mounts or ID changes
  useEffect(() => {
    if (id) {
      setCurrentChatId(id);
    }
  }, [id, setCurrentChatId, data]);
  console.log(data);

  // Update chat title in store when received from API
  useEffect(() => {
    if (data && id) {
      updateChatTitle(id, data[0]?.chatTitle);
    }
  }, [data, id, updateChatTitle]);

  // Function to recover from stuck tool calls
  const recoverFromStuckToolCalls = useCallback(() => {
    setMessages((currentMessages) => {
      // Check if there are any stuck tool calls in the last message
      const lastMessage =
        currentMessages.length > 0
          ? currentMessages[currentMessages.length - 1]
          : null;
      if (!lastMessage?.parts) return currentMessages;

      const hasStuckToolCalls = lastMessage.parts.some(
        (part) =>
          part.type === "tool-invocation" &&
          part.toolInvocation.state === "call"
      );

      if (!hasStuckToolCalls) return currentMessages;

      console.log("Recovering from stuck tool calls");

      // Fix stuck tool calls by updating them to have error results
      const updatedParts: Message["parts"] = lastMessage.parts.map((part) => {
        if (
          part.type === "tool-invocation" &&
          part.toolInvocation.state === "call"
        ) {
          console.log(
            `Resolving stuck tool call: ${part.toolInvocation.toolName}`
          );
          return {
            ...part,
            toolInvocation: {
              ...part.toolInvocation,
              state: "result",
              result: {
                error: true,
                message: "Tool execution failed due to timeout or server error",
                errorType: "client_recovery",
                success: false,
              },
            },
          };
        }
        return part;
      });

      // Update the last message with resolved tool calls
      const updatedMessages = [...currentMessages];
      updatedMessages[updatedMessages.length - 1] = {
        ...lastMessage,
        parts: updatedParts,
      };

      // Add a system message explaining the recovery
      updatedMessages.push({
        id: `recovery-${Date.now()}`,
        role: "system",
        content:
          "The previous operation timed out or encountered an error. You can continue the conversation now.",
        createdAt: new Date(),
      });

      return updatedMessages;
    });
  }, [setMessages]);

  // Call recovery function when an error occurs
  useEffect(() => {
    if (status === "error") {
      recoverFromStuckToolCalls();
    }
  }, [status, recoverFromStuckToolCalls]);

  // Also call it once when the component mounts to recover from any stuck state after refresh
  useEffect(() => {
    // Small delay to ensure messages are loaded first
    const timer = setTimeout(() => {
      recoverFromStuckToolCalls();
    }, 500);
    return () => clearTimeout(timer);
  }, [recoverFromStuckToolCalls]);

  const handleEditMessage = (messageId: string, newContent: string) => {
    // Find the index of the message being edited
    const messageIndex = messages.findIndex((m) => m.id === messageId);

    if (messageIndex !== -1) {
      // Update the message locally and remove all subsequent messages
      setMessages((currentMessages) => {
        // Get all messages up to and including the edited message
        const messagesBeforeEdit = currentMessages.slice(0, messageIndex + 1);

        // Update the edited message
        return messagesBeforeEdit.map((message) =>
          message.id === messageId
            ? {
                ...message,
                content: newContent,
                // Update parts array if it exists
                parts: message.parts
                  ? message.parts.map((part) =>
                      part.type === "text"
                        ? { ...part, text: newContent }
                        : part
                    )
                  : undefined,
              }
            : message
        );
      });
    }
  };

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col">
      <StickToBottom
        className="flex-1 flex flex-col overflow-hidden"
        resize="smooth"
        initial="smooth"
      >
        <StickToBottom.Content className="flex flex-col overflow-y-auto">
          <div className="flex-1 py-8 pb-12 container mx-auto">
            <div className="grid-cols-1 grid gap-2.5 [&_>_*]:min-w-0 mx-auto w-full max-w-[75ch]">
              {messages?.map((message) => (
                <ChatMessage
                  chatId={id || ""}
                  reload={reload}
                  handleEditMessage={handleEditMessage}
                  toolsRequiringConfirmation={toolsRequiringConfirmation}
                  key={message.id}
                  message={message}
                  addToolResult={addToolResult}
                />
              ))}
              {/* <div className="h-4 w-min bg-background">
                {(status === "streaming" || status === "submitted") && (
                  <div>
                    <Loader2 className="animate-spin size-4 text-muted-foreground" />
                  </div>
                )}
              </div> */}
            </div>
          </div>
        </StickToBottom.Content>

        <div className="sticky -bottom-32 left-0 right-0 z-10 px-4">
          <ScrollToBottom className="absolute bottom-full left-1/2 -translate-x-1/2 mb-4" />
          <div className="max-w-[75ch] mx-auto w-full shadow-lg">
            <ChatInput
              disabled={pendingToolCallConfirmation}
              input={input}
              handleInputChange={(value) =>
                handleInputChange({
                  target: { value },
                } as ChangeEvent<HTMLInputElement>)
              }
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
            />
          </div>
        </div>
      </StickToBottom>
    </div>
  );
};

export default Chat;
