"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  siblingCount?: number;
}

export function Pagination({
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  siblingCount = 1,
}: PaginationProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Create a new URLSearchParams instance for manipulation
  const createQueryString = (params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    
    // Update each parameter
    Object.entries(params).forEach(([key, value]) => {
      newSearchParams.set(key, value);
    });
    
    return newSearchParams.toString();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    router.push(`${pathname}?${createQueryString({ page: page.toString() })}`);
  };

  // Handle page size change
  const handlePageSizeChange = (size: string) => {
    router.push(
      `${pathname}?${createQueryString({ pageSize: size, page: "1" })}`
    );
  };

  // Calculate range of pages to display
  const range = (start: number, end: number) => {
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(startItem + pageSize - 1, totalItems);

  // Generate page numbers to display
  const generatePagination = () => {
    // If total pages is less than 7, show all pages
    if (totalPages <= 7) {
      return range(1, totalPages);
    }

    // Calculate start and end of sibling range
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);

    // Determine whether to show ellipsis
    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 1;

    // Always show first and last page
    if (shouldShowLeftDots && shouldShowRightDots) {
      // Show first, last, current and siblings
      return [
        1,
        "...",
        ...range(leftSiblingIndex, rightSiblingIndex),
        "...",
        totalPages,
      ];
    }

    if (shouldShowLeftDots && !shouldShowRightDots) {
      // Show first, dots, and right range
      return [1, "...", ...range(totalPages - 4, totalPages)];
    }

    if (!shouldShowLeftDots && shouldShowRightDots) {
      // Show left range, dots, and last
      return [...range(1, 5), "...", totalPages];
    }

    // Default case
    return range(1, totalPages);
  };

  const pages = generatePagination();

  return (
    <div className="flex flex-col sm:flex-row items-center gap-4">
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <div>
          Showing {startItem}-{endItem} of {totalItems} items
        </div>
        <div className="flex items-center gap-1">
          <span className="text-muted-foreground">|</span>
          <span>Page {currentPage} of {totalPages}</span>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="hidden sm:flex items-center gap-1">
          {pages.map((page, i) => {
            if (page === "...") {
              return (
                <div key={`ellipsis-${i}`} className="px-3 py-2">
                  ...
                </div>
              );
            }

            return (
              <Button
                key={`page-${page}`}
                variant={currentPage === page ? "default" : "outline"}
                size="icon"
                onClick={() => handlePageChange(page as number)}
              >
                {page}
              </Button>
            );
          })}
        </div>

        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>

        <Select
          value={pageSize.toString()}
          onValueChange={handlePageSizeChange}
        >
          <SelectTrigger className="w-[100px]">
            <SelectValue placeholder={pageSize} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="20">20 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
