import Link from "next/link";
import { MessageSquare } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { ChatSelectionManager } from "@/components/chats/chat-selection";
import { Pagination } from "@/components/ui/pagination";
import {
  ChatSessionSortOption,
  getFilteredChatSessions,
} from "@/services/chat";

interface ChatsPageContentProps {
  page: number;
  pageSize: number;
  sort: string;
  query: string;
  pinned?: boolean;
}

export async function ChatsPageContent({
  page,
  pageSize,
  sort,
  query,
  pinned,
}: ChatsPageContentProps) {
  // Fetch chat sessions with filters
  const { sessions, totalSessions, totalPages } = await getFilteredChatSessions(
    page,
    pageSize,
    sort as ChatSessionSortOption,
    {
      query: query || undefined,
      isPinned: pinned,
    }
  );

  // If no sessions found
  if (sessions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[50vh] text-center">
        <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold mb-2">No chats found</h2>
        <p className="text-muted-foreground mb-6 max-w-md">
          {query || pinned
            ? "Try adjusting your search or filters to find what you're looking for."
            : "You haven't started any chats yet. Start a new conversation to see it here."}
        </p>
        <Button asChild>
          <Link href="/chat">Start a new chat</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Client component for selection and deletion */}
      <ChatSelectionManager sessions={sessions} />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={page}
            totalPages={totalPages}
            pageSize={pageSize}
            totalItems={totalSessions}
          />
        </div>
      )}
    </div>
  );
}
