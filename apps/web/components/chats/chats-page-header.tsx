"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Search, X } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Input } from "@chromify/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";
import { Badge } from "@chromify/ui/components/badge";

export function ChatsPageHeader() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Get current search params
  const currentQuery = searchParams.get("query") || "";
  const currentSort = searchParams.get("sort") || "newest";
  const currentPinned = searchParams.get("pinned") === "true";
  
  // Create a new URLSearchParams instance for manipulation
  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    
    // Update or delete each parameter
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });
    
    return newSearchParams.toString();
  };
  
  // Handle search input
  const handleSearch = (query: string) => {
    router.push(`${pathname}?${createQueryString({ query: query || null, page: "1" })}`);
  };
  
  // Handle sort change
  const handleSortChange = (value: string) => {
    router.push(`${pathname}?${createQueryString({ sort: value, page: "1" })}`);
  };
  
  // Toggle pinned filter
  const togglePinned = () => {
    router.push(`${pathname}?${createQueryString({ 
      pinned: currentPinned ? null : "true", 
      page: "1" 
    })}`);
  };
  
  // Clear all filters
  const clearFilters = () => {
    router.push(pathname);
  };
  
  // Check if any filters are applied
  const hasFilters = currentQuery || currentPinned || currentSort !== "newest";
  
  return (
    <div className="border-b pb-4 mb-4">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Chat History</h1>
          {hasFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear all filters
            </Button>
          )}
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search input */}
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search chats..."
              className="pl-8"
              value={currentQuery}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          
          {/* Sort dropdown */}
          <Select value={currentSort} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest first</SelectItem>
              <SelectItem value="oldest">Oldest first</SelectItem>
              <SelectItem value="name-asc">Name (A-Z)</SelectItem>
              <SelectItem value="name-desc">Name (Z-A)</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Pinned filter */}
          <Button
            variant={currentPinned ? "default" : "outline"}
            onClick={togglePinned}
            className="whitespace-nowrap"
          >
            {currentPinned ? (
              <Badge variant="secondary" className="mr-2">
                Pinned only
              </Badge>
            ) : (
              "Show pinned only"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
