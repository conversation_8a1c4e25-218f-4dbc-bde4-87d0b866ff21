"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Pin, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { Checkbox } from "@chromify/ui/components/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@chromify/ui/components/dialog";
import { ChatActionDropdown } from "@/components/chats/chat-action-dropdown";
import { deleteMultipleChatSessionsAction } from "@/lib/actions/chat";
import { ChatSession } from "@/services/chat";

interface ChatSelectionProps {
  sessions: ChatSession[];

  selectedChats: Set<string>;
  onDelete: () => void;
  onSelectAll: () => void;
}

export function ChatSelectionHeader({
  sessions,
  selectedChats,
  onSelectAll,
  onDelete,
}: ChatSelectionProps) {
  if (selectedChats.size === 0) return null;

  return (
    <div className="flex items-center justify-between bg-muted p-3 rounded-md mb-2">
      <div className="flex items-center gap-2">
        <Checkbox
          checked={selectedChats.size === sessions.length}
          onCheckedChange={onSelectAll}
          id="select-all"
        />
        <label htmlFor="select-all" className="text-sm font-medium">
          {selectedChats.size} chat{selectedChats.size > 1 ? "s" : ""} selected
        </label>
      </div>
      <Button
        variant="destructive"
        size="sm"
        onClick={onDelete}
        className="flex items-center gap-1"
      >
        <Trash2 className="h-4 w-4" />
        Delete Selected
      </Button>
    </div>
  );
}

export function ChatSelectionCheckbox({
  chatId,
  isSelected,
  onSelect,
}: {
  chatId: string;
  isSelected: boolean;
  onSelect: (chatId: string, isSelected: boolean) => void;
}) {
  return (
    <Checkbox
      checked={isSelected}
      onCheckedChange={(checked) => onSelect(chatId, !!checked)}
      className="mr-1"
      onClick={(e) => e.stopPropagation()}
    />
  );
}

export function ChatSelectionManager({
  sessions,
}: {
  sessions: ChatSession[];
}) {
  const router = useRouter();
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Toggle chat selection
  const toggleChatSelection = (chatId: string, isSelected: boolean) => {
    setSelectedChats((prev) => {
      const newSelection = new Set(prev);
      if (isSelected) {
        newSelection.add(chatId);
      } else {
        newSelection.delete(chatId);
      }
      return newSelection;
    });
  };

  // Select all chats
  const selectAllChats = () => {
    if (selectedChats.size === sessions.length) {
      // If all are selected, deselect all
      setSelectedChats(new Set());
    } else {
      // Otherwise, select all
      setSelectedChats(new Set(sessions.map((chat) => chat.id)));
    }
  };

  // Delete selected chats
  const deleteSelectedChats = async () => {
    if (selectedChats.size === 0) return;

    setIsLoading(true);
    try {
      const chatIds = Array.from(selectedChats);
      const result = await deleteMultipleChatSessionsAction(chatIds);

      if (result.success) {
        toast.success(
          `${chatIds.length} chat${chatIds.length > 1 ? "s" : ""} deleted successfully`
        );
        setSelectedChats(new Set());
        setIsDeleteDialogOpen(false);
        router.refresh();
      } else {
        toast.error(result.error || "Failed to delete chats");
      }
    } catch (error) {
      toast.error("An error occurred while deleting chats");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mb-4">
      <ChatSelectionHeader
        sessions={sessions}
        selectedChats={selectedChats}
        onSelectAll={selectAllChats}
        onDelete={() => setIsDeleteDialogOpen(true)}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Selected Chats</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedChats.size} selected chat
              {selectedChats.size > 1 ? "s" : ""}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              disabled={isLoading}
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              disabled={isLoading}
              variant="destructive"
              onClick={deleteSelectedChats}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sessions.map((chat) => (
          <Card
            key={chat.id}
            className={`flex flex-col group hover:shadow-md transition-all ${selectedChats.has(chat.id) ? "ring-2 ring-primary" : ""}`}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <ChatSelectionCheckbox
                    chatId={chat.id}
                    isSelected={selectedChats.has(chat.id)}
                    onSelect={toggleChatSelection}
                  />
                  {chat.is_pinned && (
                    <Badge variant="outline" className="px-1 py-0 h-5 gap-1">
                      <Pin className="h-3 w-3" />
                      <span className="text-xs">Pinned</span>
                    </Badge>
                  )}
                  <CardTitle className="text-lg line-clamp-1">
                    <Link href={`/chat/${chat.id}`}> {chat.title}</Link>
                  </CardTitle>
                </div>
                <ChatActionDropdown chat={chat} />
              </div>
              <CardDescription>
                Last updated:{" "}
                {format(new Date(chat.last_message_at), "MMM d, yyyy h:mm a")}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow pb-2">
              {/* We could add a preview of the last message here if needed */}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
