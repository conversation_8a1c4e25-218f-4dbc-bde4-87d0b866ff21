"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Edit, MoreHorizontal, Pin, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@chromify/ui/components/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import { Input } from "@chromify/ui/components/input";
import { cn } from "@chromify/ui/lib/utils";
import {
  deleteChatSessionAction,
  toggleChatSessionPinned,
  updateChatSessionTitle,
} from "@/lib/actions/chat";
import { ChatSession } from "@/services/chat";
import { useChatStore } from "@/store/chat-store";

interface ChatActionDropdownProps {
  chat: ChatSession;
}

export function ChatActionDropdown({ chat }: ChatActionDropdownProps) {
  const router = useRouter();
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newTitle, setNewTitle] = useState(chat.title);
  const [isLoading, setIsLoading] = useState(false);

  // Get chat store actions
  const { updateChatTitle: updateStoreChatTitle, removeChatTitle } =
    useChatStore();

  // Handle rename chat
  const handleRename = async () => {
    if (!newTitle.trim()) return;

    setIsLoading(true);
    try {
      const result = await updateChatSessionTitle(chat.id, newTitle);
      if (result.success) {
        // Update the store for immediate UI update
        updateStoreChatTitle(chat.id, newTitle);

        toast.success("Chat renamed successfully");
        setIsRenameDialogOpen(false);
        router.refresh();
      } else {
        toast.error(result.error || "Failed to rename chat");
      }
    } catch (error) {
      toast.error("An error occurred while renaming the chat");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle toggle pin
  const handleTogglePin = async () => {
    setIsLoading(true);
    try {
      const result = await toggleChatSessionPinned(chat.id);
      if (result.success) {
        toast.success(
          result.data
            ? "Chat pinned successfully"
            : "Chat unpinned successfully"
        );
        router.refresh();
      } else {
        toast.error(result.error || "Failed to update pin status");
      }
    } catch (error) {
      toast.error("An error occurred while updating pin status");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete chat
  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const result = await deleteChatSessionAction(chat.id);
      if (result.success) {
        // Remove from store
        removeChatTitle(chat.id);

        toast.success("Chat deleted successfully");
        setIsDeleteDialogOpen(false);
        router.refresh();
      } else {
        toast.error(result.error || "Failed to delete chat");
      }
    } catch (error) {
      toast.error("An error occurred while deleting the chat");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn("h-7 w-7 rounded-full transition-opacity relative")}
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={() => setIsRenameDialogOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Rename
          </DropdownMenuItem>

          <DropdownMenuItem onClick={handleTogglePin}>
            <Pin className="mr-2 h-4 w-4" />
            {chat.is_pinned ? "Unpin chat" : "Pin chat"}
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Rename Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Rename Chat</DialogTitle>
            <DialogDescription>
              Enter a new name for this chat.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newTitle}
              onChange={(e) => setNewTitle(e.target.value)}
              className="w-full"
              placeholder="Enter chat title"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleRename();
                }
              }}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              disabled={isLoading}
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              disabled={isLoading || !newTitle.trim()}
              onClick={handleRename}
            >
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Chat</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{chat.title}"? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              disabled={isLoading}
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              disabled={isLoading}
              variant="destructive"
              onClick={handleDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
