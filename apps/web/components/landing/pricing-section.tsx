"use client";

import Link from "next/link";
import { Check, <PERSON>rk<PERSON> } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

// Feature item with check icon
const FeatureItem = ({
  text,
  included = true,
}: {
  text: string;
  included?: boolean;
}) => {
  return (
    <div className="flex items-start gap-2 mb-3">
      <div className="mt-1 flex-shrink-0">
        <Check
          className={cn(
            "h-4 w-4",
            included ? "text-primary" : "text-muted-foreground/50"
          )}
        />
      </div>
      <span
        className={cn(
          "text-sm",
          included
            ? "text-muted-foreground"
            : "text-muted-foreground/50 line-through"
        )}
      >
        {text}
      </span>
    </div>
  );
};

// Pricing card component
const PricingCard = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonLink,
  popular = false,
  delay = 0,
}: {
  title: string;
  price: string;
  description: string;
  features: Array<{ text: string; included: boolean }>;
  buttonText: string;
  buttonLink: string;
  popular?: boolean;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className={cn(
        "bg-card border rounded-xl overflow-hidden transition-all duration-300 relative",
        popular
          ? "border-primary shadow-lg shadow-primary/10"
          : "border-border hover:border-primary/20 hover:shadow-md"
      )}
    >
      {popular && (
        <div className="absolute top-0 right-0 bg-primary text-primary-foreground text-xs font-medium px-3 py-1 rounded-bl-lg">
          Most Popular
        </div>
      )}

      <div className="p-6">
        <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
        <div className="mb-4">
          <span className="text-3xl font-bold text-foreground">{price}</span>
          {price !== "Free" && (
            <span className="text-muted-foreground ml-1">/month</span>
          )}
        </div>
        <p className="text-muted-foreground mb-6">{description}</p>

        <Link href={buttonLink}>
          <Button
            className={cn(
              "w-full mb-6",
              popular
                ? ""
                : "bg-secondary text-secondary-foreground hover:bg-secondary/90"
            )}
            variant={popular ? "default" : "secondary"}
          >
            {popular && <Sparkles className="mr-2 h-4 w-4" />}
            {buttonText}
          </Button>
        </Link>

        <div className="space-y-1">
          {features.map((feature, index) => (
            <FeatureItem
              key={index}
              text={feature.text}
              included={feature.included}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export function PricingSection() {
  const plans = [
    {
      title: "Free",
      price: "Free",
      description: "Perfect for trying out Chromify and occasional use.",
      features: [
        { text: "5 AI-generated color schemes per month", included: true },
        { text: "Light & dark mode variants", included: true },
        { text: "Basic accessibility checks", included: true },
        { text: "Tailwind CSS export", included: true },
        { text: "Public scheme sharing", included: true },
        { text: "Chart & data visualization colors", included: false },
        { text: "Sidebar & layout colors", included: false },
        { text: "Brand color integration", included: false },
        { text: "Team collaboration", included: false },
        { text: "Priority support", included: false },
      ],
      buttonText: "Sign Up Free",
      buttonLink: "/sign-up",
      popular: false,
    },
    {
      title: "Pro",
      price: "$12",
      description: "For designers and developers who need more power.",
      features: [
        { text: "Unlimited AI-generated color schemes", included: true },
        { text: "Light & dark mode variants", included: true },
        { text: "Advanced accessibility compliance", included: true },
        { text: "Tailwind CSS export", included: true },
        { text: "Public & private scheme sharing", included: true },
        { text: "Chart & data visualization colors", included: true },
        { text: "Sidebar & layout colors", included: true },
        { text: "Brand color integration", included: true },
        { text: "Team collaboration", included: false },
        { text: "Priority support", included: false },
      ],
      buttonText: "Get Started",
      buttonLink: "/sign-up?plan=pro",
      popular: true,
    },
    {
      title: "Team",
      price: "$29",
      description: "For teams working on multiple projects together.",
      features: [
        { text: "Unlimited AI-generated color schemes", included: true },
        { text: "Light & dark mode variants", included: true },
        { text: "Advanced accessibility compliance", included: true },
        { text: "Tailwind CSS export", included: true },
        { text: "Public & private scheme sharing", included: true },
        { text: "Chart & data visualization colors", included: true },
        { text: "Sidebar & layout colors", included: true },
        { text: "Brand color integration", included: true },
        { text: "Team collaboration (up to 5 members)", included: true },
        { text: "Priority support", included: true },
      ],
      buttonText: "Start Team Plan",
      buttonLink: "/sign-up?plan=team",
      popular: false,
    },
  ];

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
              Simple, Transparent Pricing
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Choose the plan that's right for you. All plans include core
            features with different usage limits.
          </p>
        </motion.div>

        {/* Pricing cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <PricingCard
              key={index}
              title={plan.title}
              price={plan.price}
              description={plan.description}
              features={plan.features}
              buttonText={plan.buttonText}
              buttonLink={plan.buttonLink}
              popular={plan.popular}
              delay={0.1 + index * 0.1}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
