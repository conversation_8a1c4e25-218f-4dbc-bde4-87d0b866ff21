"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@chromify/ui/components/tabs";

// Format showcase component
export function FormatShowcase() {
  const [copied, setCopied] = useState<string | null>(null);

  // Copy text to clipboard
  const copyToClipboard = (text: string, format: string) => {
    navigator.clipboard.writeText(text);
    setCopied(format);
    setTimeout(() => setCopied(null), 2000);
  };

  return (
    <section className="py-20 bg-muted/30 relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-5xl mx-auto">
          {/* Section header */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                Multiple Color Formats
              </span>
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Choose between HSL (Tailwind v3) and OKLCH (Tailwind v4) color
              formats to match your project requirements.
            </p>
          </motion.div>

          {/* Format tabs */}
          <Tabs defaultValue="oklch" className="w-full">
            <div className="flex justify-center mb-8">
              <TabsList>
                <TabsTrigger value="oklch">OKLCH Format</TabsTrigger>
                <TabsTrigger value="hsl">HSL Format</TabsTrigger>
              </TabsList>
            </div>

            {/* OKLCH Format */}
            <TabsContent value="oklch" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Light mode */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="bg-card border border-border rounded-xl overflow-hidden"
                >
                  <div className="bg-muted px-4 py-2 border-b border-border flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">Light Mode</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 gap-1"
                      onClick={() =>
                        copyToClipboard(oklchLightCode, "oklch-light")
                      }
                    >
                      {copied === "oklch-light" ? (
                        <Check className="h-3.5 w-3.5" />
                      ) : (
                        <Copy className="h-3.5 w-3.5" />
                      )}
                      <span className="text-xs">Copy</span>
                    </Button>
                  </div>
                  <pre className="p-4 text-sm overflow-x-auto bg-card">
                    <code className="text-xs md:text-sm font-mono">
                      <div className="text-muted-foreground">:root {`{`}</div>
                      <div className="pl-4 text-foreground">
                        <div>
                          --background:{" "}
                          <span className="text-primary">
                            oklch(0.985 0.005 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --foreground:{" "}
                          <span className="text-primary">
                            oklch(0.25 0.01 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --card:{" "}
                          <span className="text-primary">oklch(1 0 0)</span>;
                        </div>
                        <div>
                          --primary:{" "}
                          <span className="text-primary">
                            oklch(0.68 0.15 198)
                          </span>
                          ;
                        </div>
                        <div>
                          --secondary:{" "}
                          <span className="text-primary">
                            oklch(0.88 0.06 190)
                          </span>
                          ;
                        </div>
                        <div>
                          --muted:{" "}
                          <span className="text-primary">
                            oklch(0.95 0.01 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --accent:{" "}
                          <span className="text-primary">
                            oklch(0.92 0.04 195)
                          </span>
                          ;
                        </div>
                        <div>
                          --destructive:{" "}
                          <span className="text-primary">
                            oklch(0.65 0.18 25)
                          </span>
                          ;
                        </div>
                        <div>
                          --border:{" "}
                          <span className="text-primary">
                            oklch(0.90 0.01 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --radius: <span className="text-primary">0.5rem</span>
                          ;
                        </div>
                      </div>
                      <div className="text-muted-foreground">{`}`}</div>
                    </code>
                  </pre>
                </motion.div>

                {/* Dark mode */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-card border border-border rounded-xl overflow-hidden"
                >
                  <div className="bg-muted px-4 py-2 border-b border-border flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MoonStar className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">Dark Mode</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 gap-1"
                      onClick={() =>
                        copyToClipboard(oklchDarkCode, "oklch-dark")
                      }
                    >
                      {copied === "oklch-dark" ? (
                        <Check className="h-3.5 w-3.5" />
                      ) : (
                        <Copy className="h-3.5 w-3.5" />
                      )}
                      <span className="text-xs">Copy</span>
                    </Button>
                  </div>
                  <pre className="p-4 text-sm overflow-x-auto bg-card">
                    <code className="text-xs md:text-sm font-mono">
                      <div className="text-muted-foreground">.dark {`{`}</div>
                      <div className="pl-4 text-foreground">
                        <div>
                          --background:{" "}
                          <span className="text-primary">
                            oklch(0.18 0.02 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --foreground:{" "}
                          <span className="text-primary">
                            oklch(0.96 0.005 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --card:{" "}
                          <span className="text-primary">
                            oklch(0.22 0.02 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --primary:{" "}
                          <span className="text-primary">
                            oklch(0.75 0.16 198)
                          </span>
                          ;
                        </div>
                        <div>
                          --secondary:{" "}
                          <span className="text-primary">
                            oklch(0.45 0.08 190)
                          </span>
                          ;
                        </div>
                        <div>
                          --muted:{" "}
                          <span className="text-primary">
                            oklch(0.30 0.02 200)
                          </span>
                          ;
                        </div>
                        <div>
                          --accent:{" "}
                          <span className="text-primary">
                            oklch(0.35 0.05 195)
                          </span>
                          ;
                        </div>
                        <div>
                          --destructive:{" "}
                          <span className="text-primary">
                            oklch(0.68 0.19 25)
                          </span>
                          ;
                        </div>
                        <div>
                          --border:{" "}
                          <span className="text-primary">
                            oklch(0.30 0.02 200)
                          </span>
                          ;
                        </div>
                      </div>
                      <div className="text-muted-foreground">{`}`}</div>
                    </code>
                  </pre>
                </motion.div>
              </div>
            </TabsContent>

            {/* HSL Format */}
            <TabsContent value="hsl" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Light mode */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="bg-card border border-border rounded-xl overflow-hidden"
                >
                  <div className="bg-muted px-4 py-2 border-b border-border flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">Light Mode</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 gap-1"
                      onClick={() => copyToClipboard(hslLightCode, "hsl-light")}
                    >
                      {copied === "hsl-light" ? (
                        <Check className="h-3.5 w-3.5" />
                      ) : (
                        <Copy className="h-3.5 w-3.5" />
                      )}
                      <span className="text-xs">Copy</span>
                    </Button>
                  </div>
                  <pre className="p-4 text-sm overflow-x-auto bg-card">
                    <code className="text-xs md:text-sm font-mono">
                      <div className="text-muted-foreground">
                        @layer base {`{`}
                      </div>
                      <div className="pl-4 text-muted-foreground">
                        :root {`{`}
                      </div>
                      <div className="pl-8 text-foreground">
                        <div>
                          --background:{" "}
                          <span className="text-primary">180 50% 98%</span>;
                        </div>
                        <div>
                          --foreground:{" "}
                          <span className="text-primary">180 10% 10%</span>;
                        </div>
                        <div>
                          --card:{" "}
                          <span className="text-primary">180 50% 96%</span>;
                        </div>
                        <div>
                          --primary:{" "}
                          <span className="text-primary">180 70% 50%</span>;
                        </div>
                        <div>
                          --secondary:{" "}
                          <span className="text-primary">180 30% 75%</span>;
                        </div>
                        <div>
                          --muted:{" "}
                          <span className="text-primary">180 20% 92%</span>;
                        </div>
                        <div>
                          --accent:{" "}
                          <span className="text-primary">180 60% 90%</span>;
                        </div>
                        <div>
                          --destructive:{" "}
                          <span className="text-primary">0 84% 60%</span>;
                        </div>
                        <div>
                          --border:{" "}
                          <span className="text-primary">180 20% 88%</span>;
                        </div>
                        <div>
                          --radius: <span className="text-primary">0.5rem</span>
                          ;
                        </div>
                      </div>
                      <div className="pl-4 text-muted-foreground">{`}`}</div>
                    </code>
                  </pre>
                </motion.div>

                {/* Dark mode */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="bg-card border border-border rounded-xl overflow-hidden"
                >
                  <div className="bg-muted px-4 py-2 border-b border-border flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MoonStar className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">Dark Mode</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 gap-1"
                      onClick={() => copyToClipboard(hslDarkCode, "hsl-dark")}
                    >
                      {copied === "hsl-dark" ? (
                        <Check className="h-3.5 w-3.5" />
                      ) : (
                        <Copy className="h-3.5 w-3.5" />
                      )}
                      <span className="text-xs">Copy</span>
                    </Button>
                  </div>
                  <pre className="p-4 text-sm overflow-x-auto bg-card">
                    <code className="text-xs md:text-sm font-mono">
                      <div className="pl-4 text-muted-foreground">
                        .dark {`{`}
                      </div>
                      <div className="pl-8 text-foreground">
                        <div>
                          --background:{" "}
                          <span className="text-primary">180 10% 10%</span>;
                        </div>
                        <div>
                          --foreground:{" "}
                          <span className="text-primary">180 10% 95%</span>;
                        </div>
                        <div>
                          --card:{" "}
                          <span className="text-primary">180 10% 15%</span>;
                        </div>
                        <div>
                          --primary:{" "}
                          <span className="text-primary">180 70% 60%</span>;
                        </div>
                        <div>
                          --secondary:{" "}
                          <span className="text-primary">180 15% 30%</span>;
                        </div>
                        <div>
                          --muted:{" "}
                          <span className="text-primary">180 10% 20%</span>;
                        </div>
                        <div>
                          --accent:{" "}
                          <span className="text-primary">180 20% 25%</span>;
                        </div>
                        <div>
                          --destructive:{" "}
                          <span className="text-primary">0 70% 70%</span>;
                        </div>
                        <div>
                          --border:{" "}
                          <span className="text-primary">180 10% 25%</span>;
                        </div>
                      </div>
                      <div className="pl-4 text-muted-foreground">{`}`}</div>
                      <div className="text-muted-foreground">{`}`}</div>
                    </code>
                  </pre>
                </motion.div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Additional info */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-12 text-center"
          >
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Chromify generates complete color systems with all the variables
              you need for your Tailwind CSS and shadcn/ui projects. Choose your
              preferred format and customize your theme with different border
              radius options.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

// Sample code snippets for copying
const oklchLightCode = `:root {
  --background: oklch(0.985 0.005 200);
  --foreground: oklch(0.25 0.01 200);
  --card: oklch(1 0 0);
  --primary: oklch(0.68 0.15 198);
  --secondary: oklch(0.88 0.06 190);
  --muted: oklch(0.95 0.01 200);
  --accent: oklch(0.92 0.04 195);
  --destructive: oklch(0.65 0.18 25);
  --border: oklch(0.90 0.01 200);
  --radius: 0.5rem;
}`;

const oklchDarkCode = `.dark {
  --background: oklch(0.18 0.02 200);
  --foreground: oklch(0.96 0.005 200);
  --card: oklch(0.22 0.02 200);
  --primary: oklch(0.75 0.16 198);
  --secondary: oklch(0.45 0.08 190);
  --muted: oklch(0.30 0.02 200);
  --accent: oklch(0.35 0.05 195);
  --destructive: oklch(0.68 0.19 25);
  --border: oklch(0.30 0.02 200);
}`;

const hslLightCode = `@layer base {
  :root {
    --background: 180 50% 98%;
    --foreground: 180 10% 10%;
    --card: 180 50% 96%;
    --primary: 180 70% 50%;
    --secondary: 180 30% 75%;
    --muted: 180 20% 92%;
    --accent: 180 60% 90%;
    --destructive: 0 84% 60%;
    --border: 180 20% 88%;
    --radius: 0.5rem;
  }
}`;

const hslDarkCode = `@layer base {
  .dark {
    --background: 180 10% 10%;
    --foreground: 180 10% 95%;
    --card: 180 10% 15%;
    --primary: 180 70% 60%;
    --secondary: 180 15% 30%;
    --muted: 180 10% 20%;
    --accent: 180 20% 25%;
    --destructive: 0 70% 70%;
    --border: 180 10% 25%;
  }
}`;
