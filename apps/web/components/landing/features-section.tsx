"use client";

import {
  Accessibility,
  BarChart4,
  <PERSON>,
  Layout,
  <PERSON>,
  <PERSON>hare2,
  Slide<PERSON>,
  Spark<PERSON>,
} from "lucide-react";
import { motion } from "motion/react";

// Feature card component with animation
const FeatureCard = ({
  icon,
  title,
  description,
  delay = 0,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="relative group"
    >
      {/* <div
        className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl -z-10"
        style={{
          backgroundImage: `radial-gradient(circle at center, var(--primary) 0%, transparent 70%)`,
             opacity: 0.05,
        }}
      /> */}

      <div className="bg-card/50 backdrop-blur-sm border border-border hover:border-primary/20 rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
        <div
          className="mb-4 p-3 rounded-lg bg-gradient-to-br w-fit"
          style={{
            backgroundImage: `linear-gradient(to bottom right, var(--primary) 0%, transparent 100%)`,
            opacity: 0.1,
          }}
        >
          {icon}
        </div>
        <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
};

export function FeaturesSection() {
  const features = [
    {
      icon: <Sparkles className="h-6 w-6 text-primary" />,
      title: "AI-Powered Generation",
      description:
        "Describe your vision in natural language, get professional and polished color themes instantly.",
      gradient: "from-primary/20 to-transparent",
    },
    {
      icon: <Moon className="h-6 w-6 text-primary" />,
      title: "Light & Dark Modes",
      description:
        "Every color scheme comes with perfectly balanced light and dark variants that maintain your brand identity.",
      gradient: "from-chart-2/20 to-transparent",
    },
    {
      icon: <Sliders className="h-6 w-6 text-primary" />,
      title: "Border Radius Control",
      description:
        "Customize the roundness of UI elements with precise border radius options to match your design aesthetic.",
      gradient: "from-chart-6/20 to-transparent",
    },
    {
      icon: <Accessibility className="h-6 w-6 text-primary" />,
      title: "Accessibility Built-in",
      description:
        "All color schemes meet WCAG standards for contrast and readability, ensuring your designs are inclusive.",
      gradient: "from-chart-3/20 to-transparent",
    },
    {
      icon: <Code className="h-6 w-6 text-primary" />,
      title: "Tailwind CSS Ready",
      description:
        "Copy Tailwind CSS variables in both HSL (v3) and OKLCH (v4) formats, ready to use in your projects.",
      gradient: "from-chart-4/20 to-transparent",
    },
    {
      icon: <BarChart4 className="h-6 w-6 text-primary" />,
      title: "Chart & Data Visualization",
      description:
        "Specialized color palettes for charts and data visualizations that maintain clarity and distinction.",
      gradient: "from-chart-5/20 to-transparent",
    },
    {
      icon: <Layout className="h-6 w-6 text-primary" />,
      title: "Sidebar & Layout Colors",
      description:
        "Dedicated color sets for navigation and layout elements that complement your main color scheme.",
      gradient: "from-primary/20 to-transparent",
    },
    {
      icon: <Share2 className="h-6 w-6 text-primary" />,
      title: "Share & Discover",
      description:
        "Share your favorite color schemes with the community and discover new themes created by other users.",
      gradient: "from-chart-2/20 to-transparent",
    },
    // {
    //   icon: <Palette className="h-6 w-6 text-primary" />,
    //   title: "Brand Color Integration",
    //   description:
    //     "Incorporate your existing brand colors and let AI build a harmonious palette around them.",
    //   gradient: "from-chart-3/20 to-transparent",
    // },
  ];

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] pointer-events-none" />

      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
              Powerful Features
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Chromify combines the power of AI with expert color theory to
            deliver beautiful, accessible color schemes for any project.
          </p>
        </motion.div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              delay={0.1 + index * 0.05}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
