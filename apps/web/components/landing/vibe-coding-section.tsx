"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON>, C<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Wand2 } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";

// Feature item component
const VibeFeature = ({
  icon,
  title,
  description,
  delay = 0,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="flex gap-4"
    >
      <div className="flex-shrink-0 mt-1">
        <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
          {icon}
        </div>
      </div>
      <div>
        <h3 className="text-lg font-medium mb-1">{title}</h3>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
    </motion.div>
  );
};

// Code snippet with syntax highlighting
const CodeSnippet = ({ delay = 0 }: { delay?: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="bg-card border border-border rounded-lg overflow-hidden shadow-md"
    >
      <div className="bg-muted px-4 py-2 border-b border-border flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-destructive/70" />
          <div className="w-3 h-3 rounded-full bg-yellow-500/70" />
          <div className="w-3 h-3 rounded-full bg-green-500/70" />
        </div>
        <span className="text-xs text-muted-foreground">
          tailwind.config.js
        </span>
      </div>
      <pre className="p-4 text-sm overflow-x-auto">
        <code className="language-javascript">
          <span className="text-blue-400">const</span>{" "}
          <span className="text-yellow-300">colors</span> ={" "}
          <span className="text-purple-400">require</span>(
          <span className="text-green-300">'./colors.js'</span>);
          {"\n\n"}
          <span className="text-blue-400">module</span>.
          <span className="text-yellow-300">exports</span> = {"{"}
          {"\n  "}
          <span className="text-yellow-300">theme</span>: {"{"}
          {"\n    "}
          <span className="text-yellow-300">extend</span>: {"{"}
          {"\n      "}
          <span className="text-yellow-300">colors</span>: colors,
          {"\n      "}
          <span className="text-yellow-300">borderRadius</span>: {"{"}
          {"\n        "}
          <span className="text-yellow-300">DEFAULT</span>:{" "}
          <span className="text-green-300">'0.5rem'</span>,{"\n      "}
          {"}"},{"\n    "}
          {"}"},{"\n  "}
          {"}"},{"\n"}
          {"}"};
        </code>
      </pre>
    </motion.div>
  );
};

export function VibeCodingSection() {
  const features = [
    {
      icon: <Wand2 className="h-4 w-4 text-primary" />,
      title: "AI-Generated Professional Aesthetics",
      description:
        "Elevate AI-generated code with beautiful, coherent color systems that give your projects a polished, professional look.",
    },
    {
      icon: <Code className="h-4 w-4 text-primary" />,
      title: "One-Click Implementation",
      description:
        "Copy generated CSS variables and paste them directly into your AI-generated project for instant visual enhancement.",
    },
    {
      icon: <Github className="h-4 w-4 text-primary" />,
      title: "GitHub Copilot Integration",
      description:
        "Provide complete color systems that work seamlessly with Copilot-assisted projects for visual design guidance.",
    },
    {
      icon: <Cpu className="h-4 w-4 text-primary" />,
      title: "Multiple AI Model Support",
      description:
        "Choose from different AI models including deepseek-chat, Anthropic's Claude, and Google's Gemini for your themes.",
    },
  ];

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] pointer-events-none" />

      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 backdrop-blur-sm border border-primary/20 mb-4">
            <Sparkles className="h-3.5 w-3.5 text-primary" />
            <span className="text-sm font-medium">Vibe Coding Integration</span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
              Perfect for AI-Assisted Development
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Chromify enhances your AI coding workflow by providing professional
            color systems that elevate AI-generated code from functional to
            beautiful.
          </p>
        </motion.div>

        {/* Two column layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left column - Code example */}
          <div>
            <CodeSnippet />
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-center"
            >
              <Link href="/sign-up">
                <Button variant="outline" size="sm" className="group">
                  Learn more about Vibe Coding
                  <ArrowRight className="ml-2 h-3.5 w-3.5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </motion.div>
          </div>

          {/* Right column - Features */}
          <div className="space-y-8">
            {features.map((feature, index) => (
              <VibeFeature
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                delay={0.1 + index * 0.1}
              />
            ))}

            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mt-8 p-4 bg-card/50 border border-border rounded-lg"
            >
              <p className="text-sm text-muted-foreground italic">
                "Chromify has completely transformed my AI coding workflow. The
                border radius customization options make my Copilot-generated
                components look as good as they function."
              </p>
              <div className="mt-2 flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                  <span className="text-xs font-medium text-primary">MJ</span>
                </div>
                <span className="text-xs font-medium">
                  Michael Johnson, Frontend Developer
                </span>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
