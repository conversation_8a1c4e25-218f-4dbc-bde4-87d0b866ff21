"use client";

import Link from "next/link";
import { Pa<PERSON>, Zap } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";

export function FinalCTA() {
  return (
    <section className="py-20 bg-gradient-to-b from-muted/30 to-background relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="bg-card border border-border rounded-2xl p-8 md:p-12 shadow-xl relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 pointer-events-none" />

            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 pointer-events-none" />
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-accent/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 pointer-events-none" />

            <div className="relative z-10">
              <div className="text-center mb-8">
                <motion.h2
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-3xl md:text-4xl font-bold mb-4 tracking-tight"
                >
                  Ready to Transform Your <br />
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                    Color Experience?
                  </span>
                </motion.h2>

                <motion.p
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="text-muted-foreground max-w-2xl mx-auto mb-8"
                >
                  Join thousands of designers and developers who are creating
                  beautiful, accessible color schemes with Chromify. Try our
                  multiple AI models and border radius controls today!
                </motion.p>

                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex flex-wrap justify-center gap-4"
                >
                  <Link href="/sign-up">
                    <Button size="lg" className="group">
                      <Zap className="mr-2 h-4 w-4 transition-transform group-hover:scale-125" />
                      Get Started — It's Free
                    </Button>
                  </Link>

                  <Link href="/market">
                    <Button variant="outline" size="lg">
                      <Palette className="mr-2 h-4 w-4" />
                      Browse Color Schemes
                    </Button>
                  </Link>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="text-center text-sm text-muted-foreground"
              >
                <p>
                  No credit card required. Start with our free plan and upgrade
                  anytime.
                </p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
