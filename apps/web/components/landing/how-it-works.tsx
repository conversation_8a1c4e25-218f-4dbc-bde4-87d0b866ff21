"use client";

import {
  <PERSON><PERSON><PERSON>,
  Check<PERSON>ircle2,
  <PERSON><PERSON>,
  Download,
  MessageSquareTex<PERSON>,
  Palette,
  Sliders,
} from "lucide-react";
import { motion } from "motion/react";

// Step component with animation
const Step = ({
  number,
  icon,
  title,
  description,
  delay = 0,
}: {
  number: number;
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6, delay }}
      className="flex items-start gap-4 relative"
    >
      {/* Step number with icon */}
      <div className="flex-shrink-0">
        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center relative">
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: delay + 0.2,
            }}
            className="absolute inset-0 flex items-center justify-center"
          >
            {icon}
          </motion.div>
          <span className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">
            {number}
          </span>
        </div>

        {/* Connector line */}
        {number < 4 && (
          <motion.div
            initial={{ height: 0 }}
            whileInView={{ height: "100%" }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: delay + 0.4 }}
            className="w-[2px] bg-border h-full ml-6 mt-2"
          />
        )}
      </div>

      {/* Step content */}
      <div className="pt-1.5">
        <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
};

export function HowItWorks() {
  const steps = [
    {
      icon: <MessageSquareText className="h-5 w-5 text-primary" />,
      title: "Describe Your Vision",
      description:
        "Tell our AI what you're looking for in plain language. Mention colors, moods, industries, or styles that inspire you.",
    },
    {
      icon: <Cpu className="h-5 w-5 text-primary" />,
      title: "Choose Your AI Model",
      description:
        "Select from multiple AI models including deepseek-chat, Anthropic's Claude, and Google's Gemini to power your theme generation.",
    },
    {
      icon: <Sliders className="h-5 w-5 text-primary" />,
      title: "Customize Options",
      description:
        "Choose your format (HSL or OKLCH), toggle dark mode, sidebar colors, chart colors, and adjust border radius to match your design.",
    },
    {
      icon: <Palette className="h-5 w-5 text-primary" />,
      title: "Review & Refine",
      description:
        "Our AI generates a complete color scheme with all your selected options. Watch it stream in real-time as it's created.",
    },
    {
      icon: <CheckCircle2 className="h-5 w-5 text-primary" />,
      title: "Preview in Context",
      description:
        "See your colors applied to real UI components. Test accessibility and make sure everything looks great.",
    },
    {
      icon: <Download className="h-5 w-5 text-primary" />,
      title: "Export & Implement",
      description:
        "Export your colors as Tailwind CSS variables, CSS custom properties, or other formats ready to use in your project.",
    },
  ];

  return (
    <section className="py-20 bg-muted/30 relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-5xl mx-auto">
          {/* Section header */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                How Chromify Works
              </span>
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Creating the perfect color scheme has never been easier. Follow
              these simple steps to transform your vision into reality.
            </p>
          </motion.div>

          {/* Steps */}
          <div className="space-y-12 relative">
            {steps.map((step, index) => (
              <Step
                key={index}
                number={index + 1}
                icon={step.icon}
                title={step.title}
                description={step.description}
                delay={0.2 + index * 0.1}
              />
            ))}
          </div>

          {/* Example prompt */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mt-16 bg-card border border-border rounded-xl p-6 shadow-sm"
          >
            <h3 className="text-lg font-medium mb-3 flex items-center">
              <MessageSquareText className="h-5 w-5 mr-2 text-primary" />
              Example Prompt
            </h3>
            <p className="text-muted-foreground italic">
              &quot;Create a modern, professional color scheme for a financial
              technology app. I want it to feel trustworthy but innovative, with
              blue as the primary color. Include dark mode, chart colors, and
              use a medium border radius. Make sure it&apos;s accessible for all
              users.&quot;
            </p>
            <div className="mt-4 flex items-center text-sm text-muted-foreground">
              <ArrowRight className="h-4 w-4 mr-2 text-primary" />
              <span>
                Our AI will generate a complete color scheme based on this
                description
              </span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
