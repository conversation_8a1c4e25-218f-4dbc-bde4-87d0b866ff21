"use client";

import { useState } from "react";
import Link from "next/link";
import { ArrowRight, MoonStar, Sliders, Sun } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

// Color scheme showcase card
const SchemeCard = ({
  name,
  colors,
  dark_colors,
  borderRadius = "0.5",
  delay = 0,
}: {
  name: string;
  colors: string[];
  dark_colors: string[];
  borderRadius?: string;
  delay?: number;
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 hover:border-primary/20 group"
      style={{ borderRadius: `${borderRadius}rem` }} // Apply border radius to card
    >
      {/* Color palette display */}
      <div className="relative aspect-[2/1]">
        {/* Light mode colors */}
        <div
          className={cn(
            "absolute inset-0 grid grid-cols-5 gap-0.5 transition-opacity duration-300",
            isDarkMode ? "opacity-0" : "opacity-100"
          )}
        >
          {colors.map((color, index) => (
            <div
              key={`light-${index}`}
              className="relative"
              style={{ backgroundColor: color }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
          ))}
        </div>

        {/* Dark mode colors */}
        <div
          className={cn(
            "absolute inset-0 grid grid-cols-5 gap-0.5 transition-opacity duration-300",
            isDarkMode ? "opacity-100" : "opacity-0"
          )}
        >
          {dark_colors.map((color, index) => (
            <div
              key={`dark-${index}`}
              className="relative"
              style={{ backgroundColor: color }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(255,255,255,0.05)]"
                aria-hidden="true"
              />
            </div>
          ))}
        </div>

        {/* Mode toggle */}
        <button
          onClick={() => setIsDarkMode(!isDarkMode)}
          className="absolute bottom-2 right-2 bg-background/80 backdrop-blur-sm border border-border rounded-full p-1.5 text-foreground hover:text-primary transition-colors"
        >
          {isDarkMode ? (
            <Sun className="h-3.5 w-3.5" />
          ) : (
            <MoonStar className="h-3.5 w-3.5" />
          )}
        </button>

        {/* No image source indicator needed */}

        {/* Border radius indicator */}
        {borderRadius !== "0.5" && (
          <div className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm border border-border rounded-full px-2 py-1 flex items-center gap-1">
            <Sliders className="h-3 w-3 text-primary" />
            <span className="text-xs">{borderRadius}rem</span>
          </div>
        )}
      </div>

      {/* Card content */}
      <div className="p-4">
        <h3 className="font-medium text-foreground group-hover:text-primary transition-colors">
          {name}
        </h3>
      </div>
    </motion.div>
  );
};

export function ShowcaseSection() {
  // Sample color schemes
  const schemes = [
    {
      name: "Ocean Breeze",
      colors: ["#3b82f6", "#60a5fa", "#f0f9ff", "#bae6fd", "#0ea5e9"],
      dark_colors: ["#1d4ed8", "#3b82f6", "#0f172a", "#1e3a8a", "#0369a1"],
      borderRadius: "0.5",
    },
    {
      name: "Forest Harmony",
      colors: ["#16a34a", "#4ade80", "#f0fdf4", "#bbf7d0", "#15803d"],
      dark_colors: ["#15803d", "#22c55e", "#022c22", "#14532d", "#166534"],
      borderRadius: "0.75",
    },
    {
      name: "Sunset Glow",
      colors: ["#f97316", "#fb923c", "#fff7ed", "#fed7aa", "#c2410c"],
      dark_colors: ["#c2410c", "#f97316", "#27150d", "#9a3412", "#ea580c"],
      borderRadius: "0.3",
    },
    {
      name: "Ocean Blue",
      colors: ["#0891b2", "#22d3ee", "#ecfeff", "#a5f3fc", "#06b6d4"],
      dark_colors: ["#0e7490", "#06b6d4", "#164e63", "#0891b2", "#0284c7"],
    },
    {
      name: "Autumn Gold",
      colors: ["#b45309", "#d97706", "#fffbeb", "#fef3c7", "#92400e"],
      dark_colors: ["#92400e", "#d97706", "#422006", "#78350f", "#b45309"],
      borderRadius: "1",
    },
    {
      name: "Modern Minimal",
      colors: ["#18181b", "#27272a", "#fafafa", "#e4e4e7", "#52525b"],
      dark_colors: ["#18181b", "#27272a", "#09090b", "#3f3f46", "#52525b"],
      borderRadius: "0",
    },
  ];

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
              Polished Shadcn Color Themes
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Browse through some of the stunning color schemes created with
            Chromify. Each one comes with perfectly balanced light and dark
            variants and customizable border radius options.
          </p>
        </motion.div>

        {/* Schemes grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {schemes.map((scheme, index) => (
            <SchemeCard
              key={index}
              name={scheme.name}
              colors={scheme.colors}
              dark_colors={scheme.dark_colors}
              borderRadius={scheme.borderRadius}
              delay={0.1 + index * 0.05}
            />
          ))}
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-center"
        >
          <Link href="/sign-up">
            <Button size="lg" className="group">
              Create Your Own Schemes
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
