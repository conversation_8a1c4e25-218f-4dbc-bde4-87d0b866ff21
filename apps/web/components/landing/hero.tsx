"use client";

import Link from "next/link";
import { Code, Palette, Zap } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";
import { WordAnimation } from "./word-animation";

// Animated color palette component
// const AnimatedColorPalette = () => {
//   const colors = [
//     "var(--primary)", // primary color,
//     "var(--secondary)", // secondary color,
//     "var(--accent)", // cream/beige
//     "var(--border)", // light brown
//     "var(--card)", // darker brown
//   ];

//   return (
//     <motion.div
//       initial={{ opacity: 0 }}
//       animate={{ opacity: 1 }}
//       transition={{ duration: 0.8, delay: 0.5 }}
//       className="flex rounded-lg overflow-hidden shadow-xl w-full max-w-md mx-auto"
//     >
//       {colors.map((color, index) => (
//         <motion.div
//           key={index}
//           style={{ backgroundColor: color }}
//           className={cn("h-16 flex-1")}
//           initial={{ scaleX: 0.7, opacity: 0 }}
//           animate={{ scaleX: 1, opacity: 1 }}
//           transition={{
//             duration: 0.5,
//             delay: 0.7 + index * 0.1,
//             ease: "easeOut",
//           }}
//         >
//           <motion.div
//             className="w-full h-full"
//             whileHover={{ scale: 1.05 }}
//             transition={{ duration: 0.2 }}
//           />
//         </motion.div>
//       ))}
//     </motion.div>
//   );
// };

// Floating elements that add visual interest
const FloatingElement = ({
  delay,
  duration = 20,
  className,
  children,
}: {
  delay: number;
  duration?: number;
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay }}
      className={cn("absolute", className)}
    >
      <motion.div
        animate={{
          x: [0, 10, 0, -10, 0],
          rotate: [0, 2, 0, -2, 0],
        }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "easeInOut",
          times: [0, 0.25, 0.5, 0.75, 1],
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

// Color swatch that animates on hover
// const ColorSwatch = ({ color, delay }: { color: string; delay: number }) => {
//   return (
//     <motion.div
//       initial={{ opacity: 0, scale: 0 }}
//       animate={{ opacity: 1, scale: 1 }}
//       transition={{
//         type: "spring",
//         stiffness: 260,
//         damping: 20,
//         delay,
//       }}
//       whileHover={{ scale: 1.1, rotate: 5 }}
//       className={cn("w-12 h-12 rounded-full shadow-lg")}
//       style={{ backgroundColor: color }}
//     />
//   );
// };

// Main hero component
export function Hero() {
  // Text animation variants
  const titleVariants = {
    hidden: { opacity: 0, scale: 0.98 },
    visible: (i: number) => ({
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: 0.2 + i * 0.1,
        ease: [0.25, 0.4, 0.25, 1],
      },
    }),
  };

  return (
    <div className="relative -mt-10 min-h-screen w-full flex flex-col items-center justify-center overflow-hidden bg-background pt-16">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 blur-3xl" />

      {/* Floating decorative elements */}
      <FloatingElement delay={0.3} className="left-[5%] top-[20%]">
        <div className="w-24 h-24 rounded-full bg-primary/10 backdrop-blur-sm border border-primary/20" />
      </FloatingElement>

      <FloatingElement
        delay={0.5}
        duration={15}
        className="right-[10%] top-[15%]"
      >
        <div className="w-16 h-16 rounded-full bg-secondary/10 backdrop-blur-sm border border-secondary/20" />
      </FloatingElement>

      <FloatingElement
        delay={0.7}
        duration={25}
        className="left-[15%] bottom-[20%]"
      >
        <div className="w-20 h-20 rounded-full bg-accent/10 backdrop-blur-sm border border-accent/20" />
      </FloatingElement>

      <FloatingElement delay={0.9} className="right-[15%] bottom-[25%]">
        <div className="w-32 h-32 rounded-full bg-muted/20 backdrop-blur-sm border border-muted/30" />
      </FloatingElement>

      {/* Main content */}
      <div className="relative z-10 container mx-auto px-4 md:px-6 flex flex-col items-center">
        {/* Badge */}
        <motion.div
          custom={2}
          variants={titleVariants}
          initial="hidden"
          animate="visible"
          className="text-base flex justify-between items-center text-muted-foreground max-w-[600px] font-medium tracking-tight mb-6"
        >
          <span className="inline-block">Shadcn Color themes for</span>

          <span className="inline-flex justify-center min-w-24">
            <WordAnimation />
          </span>
        </motion.div>

        {/* Main title */}
        <div className="text-center mb-6">
          <motion.h1
            custom={0}
            variants={titleVariants}
            initial="hidden"
            animate="visible"
            className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-tight mb-2"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-foreground to-foreground/80">
              Craft Your Perfect
            </span>
          </motion.h1>

          <motion.h1
            custom={1}
            variants={titleVariants}
            initial="hidden"
            animate="visible"
            className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-tight mb-6"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-chart-2 via-primary to-chart-3">
              Color Story
            </span>
          </motion.h1>
        </div>

        {/* Description */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-lg md:text-xl text-muted-foreground mb-8 max-w-4xl text-center"
        >
          Describe your vision to get professional color themes instantly
        </motion.p>

        {/* Color palette preview */}
        {/* <AnimatedColorPalette /> */}

        {/* CTA buttons */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          className="flex flex-wrap justify-center gap-4 my-6"
        >
          <Link href="/sign-up">
            <Button size="lg" className="group">
              <Zap className="mr-2 h-4 w-4 transition-transform group-hover:scale-125" />
              Get Started — It's Free
            </Button>
          </Link>

          <Link href="/sign-in">
            <Button variant="outline" size="lg">
              <Palette className="mr-2 h-4 w-4" />
              Sign In
            </Button>
          </Link>
        </motion.div>

        {/* Features preview */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.1 }}
          className="flex flex-wrap justify-center gap-4 mt-16 text-sm text-muted-foreground"
        >
          <div className="flex items-center px-3 py-1.5 bg-card/50 backdrop-blur-sm border border-border rounded-full">
            <Palette className="h-4 w-4 mr-1.5 text-primary" />
            <span>Built for shadcn</span>
          </div>
          <div className="flex items-center px-3 py-1.5 bg-card/50 backdrop-blur-sm border border-border rounded-full">
            <Zap className="h-4 w-4 mr-1.5 text-primary" />
            <span>AI-Powered</span>
          </div>

          <div className="flex items-center px-3 py-1.5 bg-card/50 backdrop-blur-sm border border-border rounded-full">
            <Code className="h-4 w-4 mr-1.5 text-primary" />
            <span>Tailwind CSS Ready</span>
          </div>
        </motion.div>
      </div>

      {/* Bottom gradient fade */}
      <div className="absolute inset-0 bg-gradient-to-t from-background via-transparent to-transparent pointer-events-none" />
    </div>
  );
}
