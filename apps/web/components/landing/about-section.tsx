"use client";

import Link from "next/link";
import { ArrowR<PERSON>, Code, Sparkles, Users, Zap } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";
import { GridPattern } from "./grid-pattern";

// Team member card component
const TeamMemberCard = ({
  name,
  role,
  image,
  description,
  delay = 0,
}: {
  name: string;
  role: string;
  image: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="bg-card/50 backdrop-blur-sm border border-border hover:border-primary/20 rounded-xl p-6 transition-all duration-300 hover:shadow-lg"
    >
      <div className="flex flex-col items-center text-center">
        <div className="w-24 h-24 rounded-full overflow-hidden mb-4 border-2 border-primary/20">
          <img
            src={image}
            alt={name}
            width={96}
            height={96}
            className="object-cover w-full h-full"
          />
        </div>
        <h3 className="text-xl font-semibold mb-1 text-foreground">{name}</h3>
        <p className="text-primary mb-3 text-sm">{role}</p>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
    </motion.div>
  );
};

// Value card component
const ValueCard = ({
  icon,
  title,
  description,
  delay = 0,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="relative group"
    >
      <div
        className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl -z-10"
        style={{
          backgroundImage: `radial-gradient(circle at center, var(--primary) 0%, transparent 70%)`,
          opacity: 0.05,
        }}
      />

      <div className="bg-card/50 backdrop-blur-sm border border-border hover:border-primary/20 rounded-xl p-6 transition-all duration-300 hover:shadow-lg">
        <div
          className="mb-4 p-3 rounded-lg bg-gradient-to-br w-fit"
          style={{
            backgroundImage: `linear-gradient(to bottom right, var(--primary) 0%, transparent 100%)`,
            opacity: 0.1,
          }}
        >
          {icon}
        </div>
        <h3 className="text-xl font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
};

// Timeline item component
const TimelineItem = ({
  year,
  title,
  description,
  delay = 0,
}: {
  year: string;
  title: string;
  description: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="relative pl-8 pb-8 border-l border-border last:border-l-transparent"
    >
      <div className="absolute left-0 top-0 w-4 h-4 rounded-full bg-primary/20 border-2 border-primary transform -translate-x-1/2"></div>
      <div className="bg-card/50 backdrop-blur-sm border border-border rounded-xl p-6 transition-all duration-300 hover:shadow-md hover:border-primary/20">
        <span className="inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium mb-2">
          {year}
        </span>
        <h3 className="text-lg font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
    </motion.div>
  );
};

// Floating element component (reused from hero.tsx)
const FloatingElement = ({
  delay,
  duration = 20,
  className,
  children,
}: {
  delay: number;
  duration?: number;
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay }}
      className={cn("absolute", className)}
    >
      <motion.div
        animate={{
          y: [0, -15, 0],
          rotate: [0, 5, 0, -5, 0],
        }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "easeInOut",
          times: [0, 0.25, 0.5, 0.75, 1],
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

export function AboutSection() {
  // Team members data
  const teamMembers = [
    {
      name: "Alex Johnson",
      role: "Founder & CEO",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
      description:
        "Alex founded Chromify with a vision to make color theory accessible to everyone. With 15+ years in design, he leads our product strategy.",
    },
    {
      name: "Sarah Chen",
      role: "Lead Designer",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
      description:
        "Sarah brings her expertise in UI/UX design to ensure Chromify delivers beautiful, functional color schemes that work in real-world applications.",
    },
    {
      name: "Michael Rodriguez",
      role: "AI Engineer",
      image: "https://randomuser.me/api/portraits/men/22.jpg",
      description:
        "Michael leads our AI development, creating algorithms that understand color theory and can translate natural language into stunning palettes.",
    },
    {
      name: "Emily Patel",
      role: "Frontend Developer",
      image: "https://randomuser.me/api/portraits/women/17.jpg",
      description:
        "Emily ensures our web application is fast, responsive, and delivers a seamless experience across all devices and platforms.",
    },
  ];

  // Company values data
  const values = [
    {
      icon: <Sparkles className="h-6 w-6 text-primary" />,
      title: "Innovation",
      description:
        "We're constantly pushing the boundaries of what's possible with color theory and AI to deliver cutting-edge solutions.",
    },
    {
      icon: <Users className="h-6 w-6 text-primary" />,
      title: "Accessibility",
      description:
        "We believe great design should be accessible to everyone, which is why we prioritize WCAG compliance in all our color schemes.",
    },
    {
      icon: <Zap className="h-6 w-6 text-primary" />,
      title: "Simplicity",
      description:
        "Complex color theory made simple. We strive to make professional color selection accessible to users of all skill levels.",
    },
    {
      icon: <Code className="h-6 w-6 text-primary" />,
      title: "Developer-First",
      description:
        "Built by developers for developers. We create tools that integrate seamlessly into modern development workflows.",
    },
  ];

  // Company timeline data
  const timeline = [
    {
      year: "2020",
      title: "The Beginning",
      description:
        "Chromify started as a side project to help developers create better color schemes for their applications.",
    },
    {
      year: "2021",
      title: "First Release",
      description:
        "We launched our first public version, focusing on simple color palette generation with basic AI capabilities.",
    },
    {
      year: "2022",
      title: "AI Integration",
      description:
        "Integrated advanced AI models to understand natural language descriptions and generate more accurate color schemes.",
    },
    {
      year: "2023",
      title: "Expansion",
      description:
        "Added support for dark mode, sidebar colors, and chart color generation. Reached 10,000 active users milestone.",
    },
    {
      year: "2024",
      title: "Today",
      description:
        "Continuing to innovate with new features, improved AI, and a growing community of designers and developers.",
    },
  ];

  return (
    <>
      {/* Hero section */}
      <section className="relative py-20 bg-background overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 blur-3xl" />
        <GridPattern className="opacity-[0.02]" width={40} height={40} />

        {/* Floating decorative elements */}
        <FloatingElement delay={0.3} className="left-[5%] top-[20%]">
          <div className="w-24 h-24 rounded-full bg-primary/10 backdrop-blur-sm border border-primary/20" />
        </FloatingElement>

        <FloatingElement
          delay={0.5}
          duration={15}
          className="right-[10%] top-[15%]"
        >
          <div className="w-16 h-16 rounded-full bg-secondary/10 backdrop-blur-sm border border-secondary/20" />
        </FloatingElement>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          {/* Section header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16 max-w-3xl mx-auto"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-secondary/80 backdrop-blur-sm border border-border mb-8"
            >
              <Users className="h-3 w-3 text-primary" />
              <span className="text-sm text-foreground font-medium">
                Our Story
              </span>
            </motion.div>

            <h1 className="text-4xl md:text-5xl font-bold mb-6 tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                About Chromify
              </span>
            </h1>
            <p className="text-lg text-muted-foreground">
              We're on a mission to make beautiful, accessible color schemes
              available to everyone. Our AI-powered platform helps designers and
              developers create stunning color palettes in seconds.
            </p>
          </motion.div>

          {/* Mission statement */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-card border border-border rounded-xl p-8 md:p-10 mb-20 max-w-4xl mx-auto shadow-lg"
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-center">
              Our Mission
            </h2>
            <p className="text-muted-foreground text-center text-lg">
              To democratize color theory and make professional-quality color
              schemes accessible to everyone, regardless of design experience.
              We believe that great design starts with great colors, and great
              colors should be available to all.
            </p>
          </motion.div>

          {/* Team section */}
          <div className="mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                  Meet Our Team
                </span>
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                The passionate people behind Chromify who are dedicated to
                creating the best color tools for designers and developers.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {teamMembers.map((member, index) => (
                <TeamMemberCard
                  key={index}
                  name={member.name}
                  role={member.role}
                  image={member.image}
                  description={member.description}
                  delay={0.1 + index * 0.1}
                />
              ))}
            </div>
          </div>

          {/* Values section */}
          <div className="mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                  Our Values
                </span>
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                The core principles that guide everything we do at Chromify.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {values.map((value, index) => (
                <ValueCard
                  key={index}
                  icon={value.icon}
                  title={value.title}
                  description={value.description}
                  delay={0.1 + index * 0.1}
                />
              ))}
            </div>
          </div>

          {/* Timeline section */}
          <div className="mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold mb-4">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
                  Our Journey
                </span>
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                The story of how Chromify evolved from a simple idea to the
                platform it is today.
              </p>
            </motion.div>

            <div className="max-w-3xl mx-auto">
              {timeline.map((item, index) => (
                <TimelineItem
                  key={index}
                  year={item.year}
                  title={item.title}
                  description={item.description}
                  delay={0.1 + index * 0.1}
                />
              ))}
            </div>
          </div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center"
          >
            <Link href="/sign-up">
              <Button size="lg" className="group">
                Join Our Journey
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  );
}
