"use client";

import { Quote } from "lucide-react";
import { motion } from "motion/react";

// Testimonial card component
const TestimonialCard = ({
  quote,
  author,
  role,
  image,
  delay = 0,
}: {
  quote: string;
  author: string;
  role: string;
  image: string;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5, delay }}
      className="bg-card border border-border rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 hover:border-primary/20"
    >
      <Quote className="h-8 w-8 text-primary/20 mb-4" />
      <p className="text-muted-foreground mb-6 italic">&quot;{quote}&quot;</p>
      <div className="flex items-center">
        <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border border-border">
          <img
            src={image}
            alt={author}
            width={40}
            height={40}
            className="object-cover"
          />
        </div>
        <div>
          <p className="font-medium text-foreground">{author}</p>
          <p className="text-sm text-muted-foreground">{role}</p>
        </div>
      </div>
    </motion.div>
  );
};

export function TestimonialsSection() {
  const testimonials = [
    {
      quote:
        "Chromify has completely transformed our design workflow. We can now generate beautiful, accessible color schemes in seconds instead of hours.",
      author: "Sarah Johnson",
      role: "UI/UX Designer",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
    },
    {
      quote:
        "The AI understands exactly what I'm looking for, even when my descriptions are vague. It's like having a color expert on my team.",
      author: "Michael Chen",
      role: "Frontend Developer",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
    },
    {
      quote:
        "I love how every color scheme comes with dark mode variants. It's saved me countless hours of tweaking and adjusting colors.",
      author: "Emily Rodriguez",
      role: "Product Designer",
      image: "https://randomuser.me/api/portraits/women/68.jpg",
    },
    {
      quote:
        "As someone who struggles with color theory, Chromify has been a game-changer for my projects. The results are always professional and polished.",
      author: "David Kim",
      role: "Indie Developer",
      image: "https://randomuser.me/api/portraits/men/75.jpg",
    },
    {
      quote:
        "The Tailwind CSS integration with both HSL and OKLCH formats is flawless. I can switch between formats depending on whether I'm using Tailwind v3 or v4.",
      author: "Jessica Patel",
      role: "Web Developer",
      image: "https://randomuser.me/api/portraits/women/17.jpg",
    },
    {
      quote:
        "Our agency has standardized on Chromify for all client projects. It ensures consistency while still giving us creative flexibility.",
      author: "Thomas Wright",
      role: "Creative Director",
      image: "https://randomuser.me/api/portraits/men/22.jpg",
    },
  ];

  return (
    <section className="py-20 bg-muted/30 relative overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-chart-3">
              What Our Users Say
            </span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Join thousands of designers and developers who are creating
            beautiful color schemes with Chromify.
          </p>
        </motion.div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              author={testimonial.author}
              role={testimonial.role}
              image={testimonial.image}
              delay={0.1 + index * 0.05}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
