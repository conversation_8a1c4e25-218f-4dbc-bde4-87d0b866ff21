"use client";

import { createContext, useContext, useEffect, useState } from "react";
import <PERSON>rip<PERSON> from "next/script";

import { SITE_THEME_STORE_KEY } from "@/constants/site";
import {
  defaultDarkTheme,
  defaultLightTheme,
} from "@/constants/theme/default-themes";
import { AIThemeMode } from "@/schema/theme";
import { useThemeStore } from "../store/theme-store";

// Define a minimal type for themes in the store that includes only essential data
type StoreTheme = {
  id?: string; // Database ID
  name: string; // Theme name for display
  format: "hsl" | "oklch"; // Color format
  colors: Record<string, string>; // Light mode colors
  dark_colors?: Record<string, string>; // Optional dark mode colors
};

type ThemeProviderProps = {
  children: React.ReactNode;
};

type ThemeContextValue = {
  theme: StoreTheme;
  mode: AIThemeMode | "system";
  resolvedMode: AIThemeMode;
  setMode: (mode: AIThemeMode | "system") => void;
  setTheme: (theme: StoreTheme) => void;
  resetToDefaultTheme: () => void;
  applyTheme: (theme: StoreTheme, mode: AIThemeMode) => void;
};

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

export function ThemeProvider({ children }: ThemeProviderProps) {
  // Get values and actions from our Zustand store
  const {
    mode,
    currentTheme,
    setMode: setStoreMode,
    setCurrentTheme,
    addCustomTheme,
  } = useThemeStore();

  // State to track the resolved mode (either 'light' or 'dark', never 'system')
  const [resolvedMode, setResolvedMode] = useState<AIThemeMode>("light");

  // State to track the resolved theme with proper mode application
  const [resolvedTheme, setResolvedTheme] = useState<StoreTheme>(currentTheme);

  // Effect to handle system preference detection and changes
  useEffect(() => {
    // Skip on server side
    if (typeof window === "undefined") return;

    // Function to set the resolved mode based on system preference
    const updateResolvedMode = () => {
      if (mode === "system") {
        const systemPrefersDark = window.matchMedia(
          "(prefers-color-scheme: dark)"
        ).matches;
        setResolvedMode(systemPrefersDark ? "dark" : "light");
      } else {
        setResolvedMode(mode as AIThemeMode);
      }
    };

    // Initial update
    updateResolvedMode();

    // Listen for system preference changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handler = () => updateResolvedMode();

    // Add event listener (with proper compatibility check)
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener("change", handler);
    } else {
      // For older browsers
      mediaQuery.addListener(handler);
    }

    // Cleanup
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener("change", handler);
      } else {
        // For older browsers
        mediaQuery.removeListener(handler);
      }
    };
  }, [mode]);

  // Effect to update CSS variables when resolved mode or theme changes
  useEffect(() => {
    // Skip on server side
    if (typeof window === "undefined") return;

    // Determine the correct colors based on the resolved mode
    const colors =
      resolvedMode === "dark"
        ? currentTheme.dark_colors || defaultDarkTheme.colors
        : currentTheme.colors;

    // Update the resolved theme
    setResolvedTheme({
      ...currentTheme,
      colors,
    });

    // Apply theme to document
    document.documentElement.classList.toggle("dark", resolvedMode === "dark");

    // Set CSS variables for all colors
    Object.entries(colors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--${key}`, value);
    });

    // Apply data-theme attributes for potential CSS selectors
    document.documentElement.setAttribute("data-theme", currentTheme.name);
    document.documentElement.setAttribute(
      "data-theme-id",
      currentTheme.id || "default"
    );
  }, [resolvedMode, currentTheme]);

  // Wrapper functions to update the store
  const setTheme = (theme: StoreTheme) => {
    setCurrentTheme(theme);
  };

  // Handle save of theme
  const applyTheme = (theme: StoreTheme, mode: AIThemeMode) => {
    // Create a minimal store theme with only essential data
    const storeTheme = {
      id: theme.id || `${theme.name}-${Date.now()}`, // Use ID from database or generate a unique one
      name: theme.name,
      format: theme.format,
      colors: theme.colors,
      dark_colors: theme.dark_colors,
    };

    // Add to custom themes
    addCustomTheme(storeTheme);

    // Apply the theme
    setTheme(storeTheme);
    setStoreMode(mode);
  };

  const resetToDefaultTheme = () => {
    if (mode === "dark") {
      setCurrentTheme(defaultDarkTheme);
    } else if (mode === "light") {
      setCurrentTheme(defaultLightTheme);
    }

    setStoreMode("system");
  };

  // Context value
  const contextValue: ThemeContextValue = {
    theme: resolvedTheme,
    mode,
    resolvedMode,
    setMode: setStoreMode,
    setTheme,
    resetToDefaultTheme,
    applyTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeInitScript />
      {children}
    </ThemeContext.Provider>
  );
}

// Custom hook to use the theme context
// Component to inject a script that sets the initial theme before page render
function ThemeInitScript() {
  return (
    <Script
      id="theme-init"
      strategy="beforeInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          (function() {
            try {
              // Try to get the theme from localStorage
              const storedTheme = localStorage.getItem('${SITE_THEME_STORE_KEY}');

              // First, check if system prefers dark mode and apply a base class
              const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
              if (systemPrefersDark) {
                document.documentElement.classList.add('dark');
              }

              if (storedTheme) {
                const themeData = JSON.parse(storedTheme);
                const mode = themeData.state.mode;
                const currentTheme = themeData.state.currentTheme;

                // Determine if we should use dark mode based on stored preferences
                let isDark = false;
                if (mode === 'dark') {
                  isDark = true;
                } else if (mode === 'system') {
                  isDark = systemPrefersDark;
                }

                // Apply dark mode class if needed
                document.documentElement.classList.toggle('dark', isDark);

                // Apply the appropriate colors
                const colors = isDark && currentTheme.dark_colors
                  ? currentTheme.dark_colors
                  : currentTheme.colors;

                // Set CSS variables for all colors
                Object.entries(colors).forEach(([key, value]) => {
                  document.documentElement.style.setProperty('--' + key, value);
                });

                // Set theme attributes
                document.documentElement.setAttribute('data-theme', currentTheme.name);
                document.documentElement.setAttribute('data-theme-id', currentTheme.id || 'default');

               
              } else {
                // No stored theme, use system preference
               
              }
            } catch (e) {
              // If there's an error, fall back to default theme
              
            }
          })();
        `,
      }}
    />
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
}
