import { Suspense } from "react";

import SidebarChatsHistory from "./chats";
import { ChatHistorySkeleton } from "./chats/skeleton";
import { SidebarClientWrapper } from "./sidebar-client-wrapper";

/**
 * Main sidebar component - Server Component
 * This component is responsible for the overall structure of the sidebar
 * and loading data for different sections using Suspense
 */
export function AppSidebar({ ...props }: React.ComponentProps<any>) {
  return (
    <SidebarClientWrapper {...props}>
      <Suspense fallback={<ChatHistorySkeleton />}>
        <SidebarChatsHistory />
      </Suspense>
    </SidebarClientWrapper>
  );
}

export default AppSidebar;
