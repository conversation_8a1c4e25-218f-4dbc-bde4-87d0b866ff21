"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  AlertCircle,
  Columns3Icon,
  Layers,
  MousePointer,
  PenLineIcon,
  Type,
  type LucideIcon,
} from "lucide-react";

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@chromify/ui/components/sidebar";
import { cn } from "@chromify/ui/lib/utils";

// Component preview group type
interface ComponentGroup {
  name: string;
  icon: LucideIcon;
  href: string;
  description: string;
}

// Define all component groups
const componentGroups: ComponentGroup[] = [
  {
    name: "Input Controls",
    icon: PenLineIcon,
    href: "/theme/preview/input-controls",
    description: "Input, Textarea, Select, Checkbox, Radio, Switch, Slider",
  },
  {
    name: "Action Elements",
    icon: MousePointer,
    href: "/theme/preview/action-elements",
    description: "Button, Button Groups, Dropdown, Context Menu",
  },
  {
    name: "Layout Components",
    icon: Columns3Icon,
    href: "/theme/preview/layout",
    description: "Card, Dialog, Sheet, Popover, Accordion",
  },
  {
    name: "Feedback & Status",
    icon: AlertCircle,
    href: "/theme/preview/feedback",
    description: "Alert, Toast, Progress, Badge, Skeleton",
  },
  {
    name: "Typography",
    icon: Type,
    href: "/theme/preview/typography",
    description: "Headings, Paragraphs, Lists, Blockquote",
  },
  {
    name: "Data Display",
    icon: Layers,
    href: "/theme/preview/data-display",
    description: "Table, Calendar, Date picker, Avatar, Hover card",
  },
];

interface ComponentPreviewGroupsProps {
  open: boolean;
}

export function ComponentPreviewGroups({ open }: ComponentPreviewGroupsProps) {
  const pathname = usePathname();
  return (
    <SidebarMenu>
      {componentGroups.map((group) => (
        <SidebarMenuItem key={group.name}>
          <SidebarMenuButton
            tooltip={group.name}
            asChild
            isActive={pathname.startsWith(group.href)}
          >
            <Link href={group.href} className={cn("flex items-center")}>
              <group.icon className="h-4 w-4" />
              {open && (
                <div className="flex flex-col items-start ml-2 overflow-hidden">
                  <span>{group.name}</span>
                </div>
              )}
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </SidebarMenu>
  );
}
