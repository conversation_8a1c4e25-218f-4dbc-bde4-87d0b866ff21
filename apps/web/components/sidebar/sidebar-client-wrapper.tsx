"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  BookOpen,
  ChevronDown,
  History,
  Info,
  LayoutDashboardIcon,
  LayoutGridIcon,
  Palette,
  Sparkles,
  StarIcon,
  Store,
  User,
} from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@chromify/ui/components/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from "@chromify/ui/components/sidebar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";
import Logo from "@/components/site/logo";
import { createChat } from "@/lib/actions/chat";
import { ComponentPreviewGroups } from "./component-preview-groups";
import { NavUser } from "./nav-user";

interface SidebarClientWrapperProps {
  children: React.ReactNode;
}

/**
 * Client-side wrapper for the sidebar
 * Handles client-side functionality like navigation, state management, etc.
 */
export function SidebarClientWrapper({
  children,
  ...props
}: SidebarClientWrapperProps & React.ComponentProps<typeof Sidebar>) {
  const { open, isMobile } = useSidebar();
  const pathname = usePathname();
  const router = useRouter();

  return (
    <Sidebar collapsible="icon" variant="inset" {...props}>
      <SidebarHeader className="py-2 px-0 flex justify-center">
        <div
          className={cn(
            "flex items-center gap-4 pl-1.5",
            open ? "justify-between" : "justify-center"
          )}
        >
          {(open || isMobile) && <Logo showText={open || isMobile} />}

          <SidebarTrigger />
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Generate Button */}
        <SidebarGroup>
          <SidebarGroupContent className="py-1">
            {open || isMobile ? (
              <div className="space-y-2">
                <Button
                  className="capitalize flex gap-2 w-full justify-center"
                  size={"sm"}
                  onClick={async () => {
                    const result = await createChat();
                    if (result.success && result.data) {
                      router.push(`/chat/${result.data}`);
                    }
                  }}
                >
                  <Sparkles className="h-4 w-4" />
                  New Chat
                </Button>
                <Button
                  className="capitalize flex gap-2 w-full justify-center"
                  asChild
                  size={"sm"}
                >
                  <Link href="/theme/create">
                    <Sparkles className="h-4 w-4" />
                    New Color Theme
                  </Link>
                </Button>
              </div>
            ) : (
              <TooltipProvider>
                <Tooltip delayDuration={100}>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="outline"
                      className="mx-auto"
                      asChild
                    >
                      <Link href="/theme/create" className="inline-flex">
                        <Sparkles className="h-4 w-4" />
                      </Link>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>New Color Theme</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={pathname.startsWith("/dashboard")}
                  tooltip={"Dashboard"}
                >
                  <Link href="/dashboard">
                    <LayoutDashboardIcon className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className={cn(!open && "sr-only")}>
            Color Themes
          </SidebarGroupLabel>

          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"History"}
                  asChild
                  isActive={pathname.startsWith("/theme/history")}
                >
                  <Link href="/theme/history">
                    <History className="h-4 w-4" />
                    <span>History</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"Stared Themes"}
                  asChild
                  isActive={pathname.startsWith("/theme/stars")}
                >
                  <Link href="/theme/stars">
                    <StarIcon className="h-4 w-4" />
                    <span>Favorites</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"Templates"}
                  asChild
                  isActive={pathname.startsWith("/theme/templates")}
                >
                  <Link href="/theme/templates">
                    <Palette className="h-4 w-4" />
                    <span>Templates</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Chat History Section */}
        <SidebarGroup>
          <SidebarGroupLabel>Recent Chats</SidebarGroupLabel>
          <SidebarGroupContent>{children}</SidebarGroupContent>
        </SidebarGroup>

        {/* Component Previews Section - Collapsible Groups */}
        <Collapsible defaultOpen={false} className="group/collapsible">
          <SidebarGroup>
            <CollapsibleTrigger className="w-full" asChild>
              <SidebarGroupLabel className="flex items-center cursor-pointer">
                {open || isMobile ? (
                  <>
                    Component Previews
                    <ChevronDown className="ml-auto h-4 w-4 transition-transform group-data-[state=open]/previews:rotate-180" />
                  </>
                ) : (
                  <div className="flex justify-center w-full">
                    <LayoutGridIcon className="h-4 w-4" />
                  </div>
                )}
              </SidebarGroupLabel>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <ComponentPreviewGroups open={open || isMobile} />
              </SidebarGroupContent>
            </CollapsibleContent>
          </SidebarGroup>
        </Collapsible>

        {/* Resources Section */}
        <SidebarGroup>
          <SidebarGroupLabel className={cn(!open && "sr-only")}>
            Resources
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={"Market"}
                  asChild
                  isActive={pathname.startsWith("/market")}
                >
                  <Link href="/market">
                    <Store className="h-4 w-4" />
                    <span>Market</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip={"Tips"}>
                  <Link href="/theme/tips">
                    <BookOpen className="h-4 w-4" />
                    <span>Tips</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip={"About"}>
                  <Link href="/about">
                    <Info className="h-4 w-4" />
                    <span>About</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="py-2">
        <div
          className={cn(
            "flex items-center",
            open ? "justify-between" : "justify-center flex-col gap-2"
          )}
        >
          {!open || (isMobile && <SidebarTrigger />)}

          {open || isMobile ? (
            <NavUser />
          ) : (
            <TooltipProvider>
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <User className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Account</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
