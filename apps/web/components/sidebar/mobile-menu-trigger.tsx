"use client";

import { Menu } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { useSidebar } from "@chromify/ui/components/sidebar";

export function MobileMenuTrigger() {
  const { setOpenMobile } = useSidebar();

  return (
    <Button
      variant="ghost"
      size="icon"
      className="md:hidden" // Only show on mobile
      onClick={() => setOpenMobile(true)}
    >
      <Menu className="h-5 w-5" />
      <span className="sr-only">Open menu</span>
    </Button>
  );
}
