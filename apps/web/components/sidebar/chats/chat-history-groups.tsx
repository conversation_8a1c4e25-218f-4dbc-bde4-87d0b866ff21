"use client";

import Link from "next/link";
import { ChevronDown, History } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@chromify/ui/components/collapsible";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@chromify/ui/components/sidebar";
import SidebarChatEdit from "./chat-edit";

interface ChatHistoryGroupsProps {
  todayChats: any[];
  yesterdayChats: any[];
  pastWeekChats: any[];
  maxChatsPerSection?: number;
}

/**
 * Component to display chat history groups
 * Separated to allow for client-side interactivity with Collapsible
 */
export function ChatHistoryGroups({
  todayChats = [],
  yesterdayChats = [],
  pastWeekChats = [],
  maxChatsPerSection = 5,
}: ChatHistoryGroupsProps) {
  return (
    <div className="flex flex-col">
      {/* Today's chats */}
      {todayChats.length > 0 && (
        <SidebarGroup>
          <Collapsible defaultOpen className="w-full">
            <CollapsibleTrigger className="w-full">
              <SidebarGroupLabel className="flex items-center justify-between w-full cursor-pointer">
                <span>Today</span>
                <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </SidebarGroupLabel>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {todayChats.slice(0, maxChatsPerSection).map((chat) => (
                    <SidebarChatEdit chat={chat} key={chat.id} />
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>
      )}

      {/* Yesterday's chats */}
      {yesterdayChats.length > 0 && (
        <SidebarGroup>
          <Collapsible defaultOpen className="w-full">
            <CollapsibleTrigger className="w-full">
              <SidebarGroupLabel className="flex items-center justify-between w-full cursor-pointer">
                <span>Yesterday</span>
                <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </SidebarGroupLabel>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {yesterdayChats.slice(0, maxChatsPerSection).map((chat) => (
                    <SidebarChatEdit chat={chat} key={chat.id} />
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>
      )}

      {/* Past week chats */}
      {pastWeekChats.length > 0 && (
        <SidebarGroup>
          <Collapsible defaultOpen className="w-full">
            <CollapsibleTrigger className="w-full">
              <SidebarGroupLabel className="flex items-center justify-between w-full cursor-pointer">
                <span>Past 7 Days</span>
                <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </SidebarGroupLabel>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarGroupContent>
                <SidebarMenu>
                  {pastWeekChats.slice(0, maxChatsPerSection).map((chat) => (
                    <SidebarChatEdit chat={chat} key={chat.id} />
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </CollapsibleContent>
          </Collapsible>
        </SidebarGroup>
      )}

      {/* View all chats link */}
      {(todayChats.length > 0 ||
        yesterdayChats.length > 0 ||
        pastWeekChats.length > 0) && (
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip="View all chats">
                  <Link
                    href="/chats"
                    className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <History className="h-4 w-4" />
                    <span>View all chats</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      )}

      {/* Show message if no chats */}
      {todayChats.length === 0 &&
        yesterdayChats.length === 0 &&
        pastWeekChats.length === 0 && (
          <SidebarGroup>
            <SidebarGroupContent>
              <div className="px-3 py-2 text-sm text-muted-foreground">
                No recent chats found.
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
    </div>
  );
}
