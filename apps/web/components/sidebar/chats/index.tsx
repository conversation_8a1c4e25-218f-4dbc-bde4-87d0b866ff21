import { getRecentChatSessions } from "@/services/chat";
import { ChatHistoryGroups } from "./chat-history-groups";

/**
 * Server component that fetches and displays chat history
 * Grouped by time periods: Today, Yesterday, Past 7 Days
 */
async function SidebarChatsHistory() {
  // Fetch recent chats grouped by time periods
  const { todayChats, yesterdayChats, pastWeekChats } =
    await getRecentChatSessions(5);

  // Format chat data for the client component
  const formattedTodayChats = todayChats.map((chat) => ({
    id: chat.id,
    metadata: {
      title: chat.title,
      updatedAt: chat.last_message_at,
      isPinned: chat.is_pinned,
    },
  }));

  const formattedYesterdayChats = yesterdayChats.map((chat) => ({
    id: chat.id,
    metadata: {
      title: chat.title,
      updatedAt: chat.last_message_at,
      isPinned: chat.is_pinned,
    },
  }));

  const formattedPastWeekChats = pastWeekChats.map((chat) => ({
    id: chat.id,
    metadata: {
      title: chat.title,
      updatedAt: chat.last_message_at,
      isPinned: chat.is_pinned,
    },
  }));

  // Use the client component to render the chat groups
  return (
    <ChatHistoryGroups
      todayChats={formattedTodayChats}
      yesterdayChats={formattedYesterdayChats}
      pastWeekChats={formattedPastWeekChats}
      maxChatsPerSection={5}
    />
  );
}

export default SidebarChatsHistory;
