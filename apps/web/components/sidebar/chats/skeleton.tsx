"use client";

import {
  SidebarMenu,
  SidebarMenuSkeleton,
  SidebarMenuItem,
} from "@chromify/ui/components/sidebar";

/**
 * Skeleton loader for chat history
 * Used as a fallback when loading chat history data
 */
export function ChatHistorySkeleton() {
  return (
    <SidebarMenu>
      {Array.from({ length: 3 }).map((_, index) => (
        <SidebarMenuItem key={index}>
          <SidebarMenuSkeleton />
        </SidebarMenuItem>
      ))}
    </SidebarMenu>
  );
}
