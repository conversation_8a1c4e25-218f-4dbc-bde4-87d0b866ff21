"use client";

import { useState, useTransition } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { MoreH<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pin, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@chromify/ui/components/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import { Input } from "@chromify/ui/components/input";
import {
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@chromify/ui/components/sidebar";
import {
  deleteChatSessionAction,
  toggleChatSessionPinned,
  updateChatSessionTitle,
} from "@/lib/actions/chat";
import { useChatStore } from "@/store/chat-store";

interface Chat {
  id: string;
  metadata: {
    title: string;
    updatedAt: string;
    isPinned?: boolean;
  };
}

function SidebarChatEdit({ chat }: { chat: Chat }) {
  const pathname = usePathname();
  const router = useRouter();

  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editTitle, setEditTitle] = useState("");
  const [isPending, startTransition] = useTransition();
  // Get the current chat ID from the pathname
  const currentChatId = pathname.endsWith(`/chat/${chat.id}`);

  // Get chat title from store or fall back to the one from props
  const {
    chatTitles,
    updateChatTitle: updateStoreChatTitle,
    removeChatTitle,
  } = useChatStore();
  const chatTitle = chatTitles[chat.id] || chat.metadata.title;

  // Start editing a chat title
  const startEditing = (chat: Chat) => {
    // Use title from store if available, otherwise use the one from props
    setEditTitle(chatTitles[chat.id] || chat.metadata.title);
    setIsRenameDialogOpen(true);
  };

  // Save the edited chat title
  const saveTitle = async (id: string) => {
    startTransition(async () => {
      try {
        // Update in database
        await updateChatSessionTitle(id, editTitle);

        // Also update in store for immediate UI update
        updateStoreChatTitle(id, editTitle);

        setIsRenameDialogOpen(false);
        toast.success("Chat renamed successfully");
      } catch (error) {
        toast.error("Failed to rename chat");
        console.error("Error renaming chat:", error);
      }
    });
  };

  // Open delete confirmation dialog
  const confirmDelete = () => {
    setIsDeleteDialogOpen(true);
  };

  // Toggle pin status
  const togglePin = async (id: string) => {
    startTransition(async () => {
      try {
        const result = await toggleChatSessionPinned(id);
        if (result.success) {
          toast.success(
            result.data
              ? "Chat pinned successfully"
              : "Chat unpinned successfully"
          );
        } else {
          toast.error("Failed to update pin status");
        }
      } catch (error) {
        toast.error("Failed to update pin status");
        console.error("Error toggling pin status:", error);
      }
    });
  };

  // Delete a chat
  const deleteChat = async (id: string) => {
    startTransition(async () => {
      try {
        await deleteChatSessionAction(id);

        // Also remove from store
        removeChatTitle(id);

        setIsDeleteDialogOpen(false);
        toast.success("Chat deleted successfully");

        // If the user is currently viewing this chat, redirect to home page
        if (currentChatId) {
          router.push("/dashboard");
        }
      } catch (error) {
        toast.error("Failed to delete chat");
        console.error("Error deleting chat:", error);
      }
    });
  };

  return (
    <SidebarMenuItem className="group">
      <SidebarMenuButton asChild isActive={currentChatId}>
        <Link
          href={`/chat/${chat.id}`}
          prefetch={true}
          className="flex items-center gap-2"
        >
          {chat.metadata.isPinned && (
            <Pin className="h-3 w-3 text-muted-foreground flex-shrink-0" />
          )}
          <span className="truncate">{chatTitle}</span>
        </Link>
      </SidebarMenuButton>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction showOnHover={true}>
            <MoreHorizontal className="h-4 w-4" />
          </SidebarMenuAction>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="right" align="start" className="w-48">
          <DropdownMenuItem
            onClick={() => {
              startEditing(chat);
            }}
          >
            <Pencil className="mr-2 h-4 w-4" />
            <span>Rename</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => togglePin(chat.id)}>
            <Pin className="mr-2 h-4 w-4" />
            <span>{chat.metadata.isPinned ? "Unpin chat" : "Pin chat"}</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onClick={confirmDelete}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Rename Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Rename Chat</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="w-full"
              placeholder="Enter chat title"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  saveTitle(chat.id);
                }
              }}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              disabled={isPending}
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button disabled={isPending} onClick={() => saveTitle(chat.id)}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Chat</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this chat? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              disabled={isPending}
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              disabled={isPending}
              variant="destructive"
              onClick={() => deleteChat(chat.id)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SidebarMenuItem>
  );
}

export default SidebarChatEdit;
