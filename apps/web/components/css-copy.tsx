"use client";

import { Check, Copy } from "lucide-react";
import { toast } from "sonner";
import { highlight } from "sugar-high";

import { But<PERSON> } from "@chromify/ui/components/button";
import { useCopyToClipboard } from "@/hooks/use-copy-to-clipboard";

function GeneratedCSS({ css }: { css: string }) {
  const { copyToClipboard, isCopied } = useCopyToClipboard();
  return (
    <div className="mb-6">
      <div className="relative group">
        <pre className="bg-card shadow p-4 rounded-md text-sm overflow-auto h-[600px]">
          <code
            className="space-y-1"
            dangerouslySetInnerHTML={{ __html: highlight(css) }}
          />
        </pre>

        <Button
          variant={"ghost"}
          size={"icon"}
          onClick={() => {
            copyToClipboard(css);
            toast.success("Copied to clipboard");
          }}
          className="absolute opacity-0 group-hover:opacity-100 transition-opacity right-2 top-2"
        >
          {isCopied ? (
            <Check className="h-4 w-4" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  );
}

export default GeneratedCSS;
