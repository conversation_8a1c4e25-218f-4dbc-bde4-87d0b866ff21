import React from "react";
import Image from "next/image";
import Link from "next/link";

const ChromifyLogo = ({ showText = true, size = "default" }) => {
  const sizeClasses = {
    small: { container: "gap-1", image: 20, text: "text-lg" },
    default: { container: "gap-2", image: 24, text: "text-xl" },
    large: { container: "gap-3", image: 32, text: "text-2xl" },
  };

  const selectedSize =
    sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.default;

  return (
    <div
      className={`flex items-center ${selectedSize.container} transition-all duration-300`}
    >
      <Link href={"/"} className="inline-flex items-center gap-2">
        <div className="relative">
          <Image
            src="/logo.svg"
            alt="Chromify Logo"
            width={selectedSize.image}
            height={selectedSize.image}
            className="z-10 relative drop-shadow-sm"
          />
        </div>

        {showText && (
          <div className="font-bold relative">
            <span className="bg-gradient-to-r from-cyan-500 via-teal-400 to-emerald-500 text-transparent bg-clip-text bg-size-200 animate-gradient-x drop-shadow-sm transition-all duration-300 tracking-tight">
              Chromify
            </span>
            <div className="absolute -inset-1 blur-sm bg-gradient-to-r from-cyan-500/20 via-teal-400/20 to-emerald-500/20 rounded-lg opacity-70 animate-pulse duration-4000" />
          </div>
        )}
      </Link>
    </div>
  );
};

export default ChromifyLogo;
