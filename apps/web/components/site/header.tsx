"use client";

import { useState } from "react";
import Link from "next/link";
import { SignOutButton, useUser } from "@clerk/nextjs";
import {
  LogOutIcon,
  Palette,
  SettingsIcon,
  SparklesIcon,
  Star,
  User,
} from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import { GradientButton } from "@chromify/ui/components/gradient-button";
import { Skeleton } from "@chromify/ui/components/skeleton";
import { HEADER_NAVIGATION_ITEMS } from "@/constants/site";
import ChromifyLogo from "./logo";

export function ChromifyHeader() {
  const { isLoaded, isSignedIn, user } = useUser();

  const [isOpen, setOpen] = useState(false);

  return (
    <header className="w-full z-40 fixed top-0 left-0 bg-background/80 backdrop-blur-sm border-b border-border">
      <div className="container relative mx-auto h-16 flex items-center justify-between px-4">
        {/* Logo Area */}
        <ChromifyLogo />

        {/* Auth buttons - conditionally rendered */}
        {isLoaded ? (
          <>
            <div className="flex items-center">
              {isSignedIn ? (
                <div className="flex items-center gap-6">
                  <GradientButton variant="glow" asChild>
                    <Link href="/theme/create" className="hidden sm:block">
                      Create Theme
                    </Link>
                  </GradientButton>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="flex items-center gap-2 size-10 rounded-full p-1"
                      >
                        <div className="flex items-center gap-2 size-10">
                          {user?.imageUrl ? (
                            <img
                              src={user.imageUrl}
                              alt={user.fullName || "User"}
                              className="size-8 rounded-full"
                            />
                          ) : (
                            <User className="h-5 w-5" />
                          )}
                        </div>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <Link href="/dashboard">
                        <DropdownMenuItem>
                          <SparklesIcon className="mr-2 h-4 w-4" />
                          Dashboard
                        </DropdownMenuItem>
                      </Link>
                      <DropdownMenuSeparator />
                      <Link href="/theme/history">
                        <DropdownMenuItem>
                          <User className="mr-2 h-4 w-4" />
                          History
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/theme/stars">
                        <DropdownMenuItem>
                          <Star className="mr-2 h-4 w-4" />
                          Stars
                        </DropdownMenuItem>
                      </Link>
                      <Link href="/theme/templates">
                        <DropdownMenuItem>
                          <Palette className="mr-2 h-4 w-4" />
                          Templates
                        </DropdownMenuItem>
                      </Link>
                      <DropdownMenuSeparator />
                      <Link href="/account/settings">
                        <DropdownMenuItem>
                          <SettingsIcon className="mr-2 h-4 w-4" />
                          Settings
                        </DropdownMenuItem>
                      </Link>
                      <DropdownMenuSeparator />

                      <DropdownMenuItem>
                        <LogOutIcon className="mr-2 h-4 w-4" />
                        <SignOutButton />
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ) : (
                <div className="flex items-center gap-6">
                  <Link href="/sign-in">
                    <Button variant="outline" size="sm">
                      Sign in
                    </Button>
                  </Link>
                  <Link href="/sign-up">
                    <Button size="sm">Sign up</Button>
                  </Link>
                </div>
              )}
            </div>
            {/* Mobile menu */}
            {isOpen && (
              <div className="absolute top-16 left-0 right-0 border-t flex flex-col w-full bg-background shadow-lg py-4 px-4 gap-2 z-50">
                {HEADER_NAVIGATION_ITEMS.map(
                  (item) =>
                    (!item.authRequired ||
                      (item.authRequired && isSignedIn)) && (
                      <Link
                        key={item.title}
                        href={item.href}
                        className="flex items-center py-3 px-3 hover:bg-muted rounded-md text-foreground"
                        onClick={() => setOpen(false)}
                      >
                        {item.icon}
                        {item.title}
                      </Link>
                    )
                )}

                {/* Auth-specific items */}
                {isSignedIn ? (
                  <Link
                    href="/account"
                    className="flex items-center py-3 px-3 hover:bg-muted rounded-md text-foreground"
                    onClick={() => setOpen(false)}
                  >
                    <User className="w-4 h-4 mr-2" />
                    Account Settings
                  </Link>
                ) : (
                  <>
                    <Link
                      href="/sign-in"
                      className="flex items-center py-3 px-3 hover:bg-muted rounded-md text-foreground"
                      onClick={() => setOpen(false)}
                    >
                      Sign in
                    </Link>
                    <Link
                      href="/sign-up"
                      className="flex items-center py-3 px-3 hover:bg-primary/10 rounded-md text-primary font-medium"
                      onClick={() => setOpen(false)}
                    >
                      Create account
                    </Link>
                  </>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center gap-6">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-16" />
          </div>
        )}
      </div>
    </header>
  );
}
