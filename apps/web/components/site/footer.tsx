"use client";

import Link from "next/link";
import {
  Accessibility,
  Code,
  Github,
  Instagram,
  Mail,
  Moon,
  Sparkles,
  Twitter,
} from "lucide-react";
import { motion } from "motion/react";

import { cn } from "@chromify/ui/lib/utils";
import { GridPattern } from "../landing/grid-pattern";
import Chromify<PERSON>ogo from "./logo";

// Footer link component with hover animation
const FooterLink = ({
  href,
  children,
  className,
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <Link
      href={href}
      className={cn(
        "text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center gap-1.5 group",
        className
      )}
    >
      {children}
    </Link>
  );
};

// Footer column component
const FooterColumn = ({
  title,
  children,
  delay = 0,
}: {
  title: string;
  children: React.ReactNode;
  delay?: number;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="flex flex-col gap-4"
    >
      <h3 className="font-semibold text-foreground">{title}</h3>
      <div className="flex flex-col gap-3">{children}</div>
    </motion.div>
  );
};

// Social media icon component
const SocialIcon = ({
  href,
  icon: Icon,
  label,
}: {
  href: string;
  icon: React.ElementType;
  label: string;
}) => {
  return (
    <Link
      href={href}
      className="w-9 h-9 flex items-center justify-center rounded-full bg-muted hover:bg-primary/10 hover:text-primary transition-colors duration-200"
      aria-label={label}
    >
      <Icon className="w-4 h-4" />
    </Link>
  );
};

export function Footer({
  hasChromifyText = true,
}: {
  hasChromifyText?: boolean;
}) {
  return (
    <footer className="border-t border-border bg-gradient-to-b from-muted/30 to-background relative overflow-hidden md:max-h-[580px]">
      {/* Decorative elements */}
      <GridPattern
        className="opacity-[0.02]"
        width={40}
        height={40}
        strokeWidth={0.5}
      />

      {/* Main footer content */}
      <div className="container mx-auto px-4 md:px-6 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-10">
          {/* Brand column */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="lg:col-span-2"
          >
            <div className="flex flex-col gap-6">
              <ChromifyLogo size="large" />
              <p className="text-muted-foreground max-w-md">
                Chromify combines AI with expert color theory to deliver
                beautiful, accessible color schemes for any project. Perfect for
                developers and designers.
              </p>
              <div className="flex gap-3">
                <SocialIcon
                  href="https://github.com"
                  icon={Github}
                  label="GitHub"
                />
                <SocialIcon
                  href="https://twitter.com"
                  icon={Twitter}
                  label="Twitter"
                />
                <SocialIcon
                  href="https://instagram.com"
                  icon={Instagram}
                  label="Instagram"
                />
                <SocialIcon
                  href="mailto:<EMAIL>"
                  icon={Mail}
                  label="Email"
                />
              </div>
            </div>
          </motion.div>

          {/* Features column */}
          <FooterColumn title="Features" delay={0.1}>
            <FooterLink href="/features/ai-generation">
              <Sparkles className="w-3.5 h-3.5" />
              <span>AI-Powered Generation</span>
            </FooterLink>
            <FooterLink href="/features/dark-mode">
              <Moon className="w-3.5 h-3.5" />
              <span>Light & Dark Modes</span>
            </FooterLink>
            <FooterLink href="/features/accessibility">
              <Accessibility className="w-3.5 h-3.5" />
              <span>Accessibility Tools</span>
            </FooterLink>
            <FooterLink href="/features/tailwind">
              <Code className="w-3.5 h-3.5" />
              <span>Tailwind CSS Export</span>
            </FooterLink>
          </FooterColumn>

          {/* Resources column */}
          <FooterColumn title="Resources" delay={0.2}>
            <FooterLink href="/docs">
              <span>Documentation</span>
            </FooterLink>
            <FooterLink href="/blog">
              <span>Blog</span>
            </FooterLink>
            <FooterLink href="/tutorials">
              <span>Tutorials</span>
            </FooterLink>
            <FooterLink href="/market">
              <span>Color Market</span>
            </FooterLink>
            <FooterLink href="/terms" className="text-sm">
              Terms of Service
            </FooterLink>
            <FooterLink href="/privacy" className="text-sm">
              Privacy Policy
            </FooterLink>
          </FooterColumn>

          {/* Company column */}
          <FooterColumn title="Company" delay={0.3}>
            <FooterLink href="/about">
              <span>About Us</span>
            </FooterLink>
            <FooterLink href="/pricing">
              <span>Pricing</span>
            </FooterLink>
            <FooterLink href="/contact">
              <span>Contact</span>
            </FooterLink>
            <FooterLink href="/careers">
              <span>Careers</span>
            </FooterLink>
          </FooterColumn>
        </div>

        {/* Newsletter section */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-16 pt-8 border-t border-border"
        >
          <div className="flex flex-col md:flex-row gap-6 justify-between items-start md:items-center">
            <div>
              <h3 className="font-semibold text-foreground mb-2">
                Stay updated with Chromify
              </h3>
              <p className="text-muted-foreground max-w-md">
                Subscribe to our newsletter for the latest features, tutorials,
                and color inspiration.
              </p>
            </div>
            <div className="flex gap-3">
              <Link href="/sign-up">
                <Button size="sm" className="group">
                  <Zap className="mr-2 h-4 w-4 transition-transform group-hover:scale-125" />
                  Get Started
                </Button>
              </Link>
              <Link href="/market">
                <Button variant="outline" size="sm">
                  <Palette className="mr-2 h-4 w-4" />
                  Browse Colors
                </Button>
              </Link>
            </div>
          </div>
        </motion.div> */}
      </div>

      {/* Bottom footer */}
      {/* <div className="border-t border-border">
        <div className="container mx-auto px-4 md:px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Chromify. All rights reserved.
            </p>
            <div className="flex gap-6 text-sm">
              <FooterLink href="/terms" className="text-sm">
                Terms of Service
              </FooterLink>
              <FooterLink href="/privacy" className="text-sm">
                Privacy Policy
              </FooterLink>
            </div>
          </div>
        </div>
      </div> */}
      {hasChromifyText && (
        <h5 className="bg-clip-text block md:hidden text-transparent bg-gradient-to-r from-chart-2/10 via-primary/10 to-chart-3/10 text-[120px] leading-none text-center pointer-events-none">
          chromify
        </h5>
      )}
    </footer>
  );
}
