import React from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Plus, XCircle } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@chromify/ui/components/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { useImageUpload } from "@/hooks/useImageUpload";

export interface ImageUploaderProps {
  /**
   * Called when an image is selected or removed
   */
  onImageChange?: (imageData: string | undefined) => void;

  /**
   * The initial image data (if any)
   */
  initialImage?: string;

  /**
   * Custom class names to apply to the image preview container
   */
  previewClassName?: string;

  /**
   * Size of the preview thumbnail (width and height)
   */
  previewSize?: "sm" | "md" | "lg";

  /**
   * Whether to show the image preview
   */
  showPreview?: boolean;

  /**
   * ID for the component
   */
  id?: string;

  /**
   * Hide the upload button (useful when showing only the preview)
   */
  hideUploadButton?: boolean;
}

/**
 * A reusable component for uploading and previewing images
 */
export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageChange,
  initialImage,
  previewClassName = "",
  previewSize = "md",
  showPreview = true,
  id,
  hideUploadButton = false,
}) => {
  const {
    imagePreview,
    fileInputRef,
    handleUploadClick,
    handleFileUpload,
    handleDeleteImage,
  } = useImageUpload({
    onImageChange,
  });

  // Map size options to actual dimensions
  const sizeMap = {
    sm: "w-12 h-12",
    md: "w-16 h-16",
    lg: "w-24 h-24",
  };

  // Use initialImage as fallback if available and no image has been uploaded
  const displayImage = imagePreview || initialImage;

  return (
    <motion.div className="flex flex-col w-12" layout>
      {/* Always include the file input but keep it hidden */}
      <input
        type="file"
        id={id}
        ref={fileInputRef}
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
        aria-label="Upload image"
      />

      {/* Upload Button - can be hidden */}
      {!hideUploadButton && (
        <motion.div layout>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger disabled asChild>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleUploadClick}
                  aria-label="Upload image"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>coming soon</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </motion.div>
      )}

      {/* Image preview section with AnimatePresence for smooth transitions */}
      <AnimatePresence>
        {showPreview && displayImage && (
          <motion.div
            className={hideUploadButton ? "" : "mt-6 w-full"}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2, easings: [0.4, 0, 0.6, 1] }}
            layout
          >
            <div className="relative inline-block">
              <Dialog>
                <DialogTrigger asChild>
                  <div className="group relative cursor-pointer">
                    <div
                      className={`${sizeMap[previewSize]} border p-1 rounded-lg overflow-hidden ${previewClassName}`}
                    >
                      <img
                        src={displayImage}
                        alt="Uploaded preview"
                        className="size-48 object-cover"
                      />
                    </div>
                  </div>
                </DialogTrigger>
                <DialogTitle className="hidden">Image Preview</DialogTitle>
                <DialogContent className="sm:max-w-md">
                  <div className="relative">
                    <div className="max-h-80vh overflow-auto">
                      <img
                        src={displayImage}
                        alt="Uploaded image"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              {/* Delete button */}
              <motion.div>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 absolute -top-2 -right-2 p-0"
                  onClick={handleDeleteImage}
                  aria-label="Remove image"
                >
                  <XCircle className="h-4 w-4" />
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
