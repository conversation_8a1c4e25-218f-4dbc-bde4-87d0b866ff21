import { ChartLineIcon, MoonIcon, PanelLeftIcon, SunIcon } from "lucide-react";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";

function ThemeVariantIcons({
  includeDarkMode,
  includeCharts,
  includeSidebar,
}: {
  includeDarkMode?: boolean;
  includeCharts?: boolean;
  includeSidebar?: boolean;
}) {
  return (
    <div className="flex gap-4">
      <SunIcon className="size-5 text-primary" />
      <TooltipProvider delayDuration={10}>
        <Tooltip>
          <TooltipTrigger>
            <MoonIcon
              className={cn(
                "size-5",
                includeDarkMode ? "text-primary" : "text-muted-foreground/50"
              )}
            />
          </TooltipTrigger>
          <TooltipContent>
            <p>
              {includeDarkMode
                ? "Dark Mode included"
                : "Dark Mode is not included"}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider delayDuration={10}>
        <Tooltip>
          <TooltipTrigger>
            <ChartLineIcon
              className={cn(
                "size-5",
                includeCharts ? "text-primary" : "text-muted-foreground/50"
              )}
            />
          </TooltipTrigger>
          <TooltipContent>
            <p>
              {includeCharts ? "Charts included" : "Charts are not included"}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider delayDuration={10}>
        <Tooltip>
          <TooltipTrigger>
            <PanelLeftIcon
              className={cn(
                "size-5",
                includeSidebar ? "text-primary" : "text-muted-foreground/50"
              )}
            />
          </TooltipTrigger>
          <TooltipContent>
            <p>
              {includeSidebar ? "Sidebar included" : "Sidebar is not included"}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

export default ThemeVariantIcons;
