"use client";

import { useThemeStore } from "@/store/theme-store";
import type { DBTheme } from "@/types/theme";

function SwatchPreview({
  themeColors,
}: {
  themeColors: Pick<DBTheme, "colors" | "dark_colors">;
}) {
  const { mode } = useThemeStore();
  const colors =
    mode === "light"
      ? themeColors.colors
      : themeColors.dark_colors || themeColors.colors;
  const { primary, secondary, accent, card } = colors;
  return (
    <div className="flex items-center gap-2">
      <div className="h-6 w-6 border overflow-hidden rounded-full group">
        <div className="grid h-12 w-12 -translate-x-1/4 -translate-y-1/4 grid-cols-2 overflow-hidden rounded-full transition-all ease-in-out group-hover:rotate-45 rotate-0">
          <div
            className="flex h-6 w-6"
            style={{ backgroundColor: primary ?? "var(--primary)" }}
            title="Primary"
          />
          <div
            className="flex h-6 w-6"
            style={{ backgroundColor: secondary ?? "var(--secondary)" }}
            title="Secondary"
          />
          <div
            className="flex h-6 w-6"
            style={{ backgroundColor: accent ?? "var(--accent)" }}
            title="Accent"
          />
          <div
            className="flex h-6 w-6"
            style={{ backgroundColor: card ?? "var(--card)" }}
            title="Card"
          />
        </div>
      </div>
    </div>
  );
}

export default SwatchPreview;
