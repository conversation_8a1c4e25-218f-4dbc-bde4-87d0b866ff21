"use client";

import { useEffect, useState } from "react";
import { Co<PERSON>, Share } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@chromify/ui/components/dialog";
import { Input } from "@chromify/ui/components/input";
import { Label } from "@chromify/ui/components/label";
import { Switch } from "@chromify/ui/components/switch";
import { toggleThemePublic } from "@/lib/actions/theme";

interface MakePublicButtonProps {
  themeId: string;
  isPublic: boolean;
  shareId?: string | null;
}

export default function MakePublicButton({
  themeId,
  isPublic: initialIsPublic,
  shareId: initialShareId,
}: MakePublicButtonProps) {
  // const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [isPublic, setIsPublic] = useState(initialIsPublic);
  const [shareId, setShareId] = useState(initialShareId);
  const [isLoading, setIsLoading] = useState(false);
  // const [isRegenerating, setIsRegenerating] = useState(false);
  const [origin, setOrigin] = useState("");

  useEffect(() => {
    // This will only run on the client, after the component is mounted
    setOrigin(window.location.origin);
  }, []);

  const shareUrl = shareId && origin ? `${origin}/shared/${shareId}` : null;

  const handleTogglePublic = async () => {
    setIsLoading(true);

    try {
      const result = await toggleThemePublic(themeId);

      if (result.success) {
        setIsPublic(result.isPublic || false);
        setShareId(result.sharedId || null);

        toast.info(
          result.isPublic
            ? "Color scheme is now public, others can now view your color scheme with the share link"
            : "Color scheme is now private, your color scheme is no longer publicly accessible"
        );
      } else {
        toast.error(
          result.error || "Something went wrong! Error updating color scheme"
        );
      }
    } catch (error) {
      toast.error("Something went wrong! Error updating color scheme");
    } finally {
      setIsLoading(false);
    }
  };

  // const handleRegenerateShareId = async () => {
  //   if (
  //     !confirm("Are you sure? This will invalidate any existing share links.")
  //   ) {
  //     return;
  //   }

  //   setIsRegenerating(true);

  //   try {
  //     const result = await regenerateSharedId(themeId, pathname);

  //     if (result.success && result.sharedId) {
  //       setShareId(result.sharedId);
  //       toast.success("Share link regenerated successfully");
  //     } else {
  //       toast.error(result.error || "Failed to regenerate share link");
  //     }
  //   } catch (error) {
  //     toast.error("Something went wrong! Error regenerating share link");
  //   } finally {
  //     setIsRegenerating(false);
  //   }
  // };

  const copyShareLink = () => {
    if (shareUrl) {
      navigator.clipboard.writeText(shareUrl);
      toast.success(
        "Share link copied! The share link has been copied to your clipboard"
      );
    }
  };

  return (
    <>
      <Button
        variant={"ghost"}
        size="icon"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 bg-transparent hover:bg-primary/5 hover:border"
      >
        <Share size={16} />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {isPublic ? "Share Color Scheme" : "Make Color Scheme Public"}
            </DialogTitle>
            <DialogDescription>
              {isPublic
                ? "Toggle off to make your color scheme private again"
                : "Toggle on to make your color scheme public and shareable"}
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center justify-between py-4">
            <Switch
              id="public-toggle"
              checked={isPublic}
              onCheckedChange={handleTogglePublic}
              disabled={isLoading}
            />
          </div>

          {isPublic && shareId && (
            <div className="flex flex-col space-y-4">
              <div className="space-y-2">
                <Label htmlFor="share-link">Share link</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="share-link"
                    value={shareUrl || ""}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    onClick={copyShareLink}
                    type="button"
                    size="icon"
                    variant="outline"
                    title="Copy to clipboard"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Regenerate share link</Label>
                  <Button
                    onClick={handleRegenerateShareId}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    disabled={isRegenerating}
                  >
                    <RefreshCw className="h-3.5 w-3.5" />
                    Regenerate
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  This will create a new share link and invalidate the previous
                  one.
                </p>
              </div> */}
            </div>
          )}

          <DialogFooter className="sm:justify-end">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
