import { ChartLineIcon, Moon, PanelLeftIcon, Sun } from "lucide-react";

import { cn } from "@chromify/ui/lib/utils";
import type { DBTheme } from "@/types/theme";

function ThemeCardConfig({
  theme,
}: {
  theme: Pick<DBTheme, "colors" | "dark_colors">;
}) {
  const colors = theme.colors;
  const hasSidebar = !!colors.sidebar;
  const hasCharts = !!colors["chart-1"];
  const hasDarkMode = theme.dark_colors ?? false;
  return (
    <div className="flex gap-4 items-center">
      <Sun className="size-5 text-primary" />
      <Moon
        className={cn(
          "size-5",
          hasDarkMode ? "text-primary" : "text-muted-foreground/50"
        )}
      />
      <ChartLineIcon
        className={cn(
          "size-5",
          hasCharts ? "text-primary" : "text-muted-foreground/50"
        )}
      />
      <PanelLeftIcon
        className={cn(
          "size-5",
          hasSidebar ? "text-primary" : "text-muted-foreground/50"
        )}
      />
    </div>
  );
}

export default ThemeCardConfig;
