import { Square } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import TailwindIcon from "@/components/icons/tailwind";

function ThemeCardOptions({
  format,
  borderRaduis,
}: {
  format: "hsl" | "oklch";
  borderRaduis: string;
}) {
  return (
    <div className="flex items-center gap-1">
      <Badge variant={"outline"}>
        <TailwindIcon />
        {format === "hsl" ? "v3" : "v4"}
      </Badge>
      <Badge variant={"outline"}>
        <Square />
        {borderRaduis}rem
      </Badge>
    </div>
  );
}

export default ThemeCardOptions;
