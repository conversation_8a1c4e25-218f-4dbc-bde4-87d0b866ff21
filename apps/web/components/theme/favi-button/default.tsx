"use client";

import { useOptimistic, useTransition } from "react";
import { Star } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";
import { toggleThemeFavorite } from "@/lib/actions/theme";

interface FavoriteButtonProps {
  themeId: string;
  isFavorite: boolean;
  label?: string;
  className?: string;
}

export default function FavoriteButton({
  themeId,
  isFavorite: initialIsFavorite,
  label,
  className,
}: FavoriteButtonProps) {
  const [isPending, startTransition] = useTransition();

  // Using useOptimistic hook with proper updateFn pattern
  const [optimisticFavorite, addOptimisticFavorite] = useOptimistic(
    initialIsFavorite,
    (currentState, newState: boolean) => newState
  );

  const handleToggleFavorite = () => {
    const newFavoriteState = !optimisticFavorite;

    // Wrap the operation in startTransition
    startTransition(() => {
      // Optimistically update UI immediately
      addOptimisticFavorite(newFavoriteState);

      // Perform the server action
      toggleThemeFavorite(themeId)
        .then((result) => {
          if (result.success) {
            toast.success(
              result.isFavorite
                ? "Added to favorites"
                : "Removed from favorites"
            );
          } else {
            // On error, the state will automatically revert to the actual state
            // on the next render cycle
            toast.error("Error updating favorites");
          }
        })
        .catch(() => {
          toast.error("Error updating favorites");
        });
    });
  };

  return (
    <Button
      variant={"ghost"}
      size="icon"
      onClick={handleToggleFavorite}
      disabled={isPending}
      className={cn(
        "flex items-center gap-2 z-20 bg-transparent hover:bg-accent",
        label && "w-full",
        className
      )}
      aria-label={
        optimisticFavorite ? "Remove from favorites" : "Add to favorites"
      }
    >
      <Star
        className={cn(
          "size-4 text-yellow-500",
          optimisticFavorite && "fill-yellow-500",
          label && "mr-2"
        )}
      />
      {label && <span>{label}</span>}
    </Button>
  );
}
