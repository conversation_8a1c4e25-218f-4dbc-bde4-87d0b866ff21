"use client";

import { useOptimistic, useTransition } from "react";
import { Star } from "lucide-react";
import { toast } from "sonner";

import { DropdownMenuItem } from "@chromify/ui/components/dropdown-menu";
import { cn } from "@chromify/ui/lib/utils";
import { toggleThemeFavorite } from "@/lib/actions/theme";

interface FavoriteButtonProps {
  themeId: string;
  isFavorite: boolean;
}

export default function FavoriteDropdown({
  themeId,
  isFavorite: initialIsFavorite,
}: FavoriteButtonProps) {
  const [isPending, startTransition] = useTransition();

  // Using useOptimistic hook with proper updateFn pattern
  const [optimisticFavorite, addOptimisticFavorite] = useOptimistic(
    initialIsFavorite,
    (currentState, newState: boolean) => newState
  );

  const handleToggleFavorite = () => {
    const newFavoriteState = !optimisticFavorite;

    // Wrap the operation in startTransition
    startTransition(() => {
      // Optimistically update UI immediately
      addOptimisticFavorite(newFavoriteState);

      // Perform the server action
      toggleThemeFavorite(themeId)
        .then((result) => {
          if (result.success) {
            toast.success(
              result.isFavorite
                ? "Added to favorites"
                : "Removed from favorites"
            );
          } else {
            // On error, the state will automatically revert to the actual state
            // on the next render cycle
            toast.error("Error updating favorites");
          }
        })
        .catch(() => {
          toast.error("Error updating favorites");
        });
    });
  };

  return (
    <DropdownMenuItem
      disabled={isPending}
      onSelect={(e) => {
        e.preventDefault();
        handleToggleFavorite();
      }}
      aria-label={
        optimisticFavorite ? "Remove from favorites" : "Add to favorites"
      }
      className="space-x-2"
    >
      <Star
        className={cn(
          "size-4 text-yellow-500",
          optimisticFavorite && "fill-yellow-500"
        )}
      />
      <span className="text-xs">
        {optimisticFavorite ? "Unstar" : "Star theme"}
      </span>
    </DropdownMenuItem>
  );
}
