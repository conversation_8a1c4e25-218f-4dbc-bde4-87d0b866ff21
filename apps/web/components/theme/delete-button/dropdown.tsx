"use client";

import { useState } from "react";
import { Trash } from "lucide-react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@chromify/ui/components/alert-dialog";
import { DropdownMenuItem } from "@chromify/ui/components/dropdown-menu";
import { cn } from "@chromify/ui/lib/utils";
import { deleteTheme } from "@/lib/actions/theme";
import { useThemeStore } from "@/store/theme-store";

interface DeleteButtonProps {
  themeId: string;
  onDeleted?: () => void; // Optional callback for after successful deletion
  themeName: string;
  isFavorite: boolean;
}

export default function DeleteDropdown({
  themeId,
  onDeleted,
  themeName,
  isFavorite = false,
}: DeleteButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { removeCustomTheme } = useThemeStore();
  const handleDelete = async () => {
    setIsLoading(true);

    try {
      const result = await deleteTheme(themeId);

      if (result.success) {
        toast.success("The color scheme has been successfully deleted");

        // Close the dialog
        setIsOpen(false);
        removeCustomTheme(themeId);
        // Call the onDeleted callback if provided
        if (onDeleted) {
          onDeleted();
        }
      } else {
        toast.error("Error deleting color scheme");
      }
    } catch {
      toast.error("Error deleting color scheme");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DropdownMenuItem
        onSelect={(e) => {
          e.preventDefault();
          setIsOpen(true);
        }}
        className="space-x-2"
      >
        <Trash className={cn("text-destructive size-4")} />
        <span className="text-xs">Delete</span>
      </DropdownMenuItem>

      <AlertDialog
        open={isOpen}
        onOpenChange={(open) => {
          // Prevent closing the dialog while deletion is in progress
          if (isLoading && !open) return;
          setIsOpen(open);
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete{" "}
              <span className="font-medium text-primary">{themeName}</span> and
              remove all associated data.
              {isFavorite && (
                <span className="text-destructive font-medium block mt-2">
                  This is one of your favorite color schemes.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
