"use client";

import { useState } from "react";
import { Trash } from "lucide-react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@chromify/ui/components/alert-dialog";
import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";
import { deleteTheme } from "@/lib/actions/theme";
import { useThemeStore } from "@/store/theme-store";

interface DeleteButtonProps {
  themeId: string;
  onDeleted?: () => void; // Optional callback for after successful deletion
  themeName: string;
  isFavorite: boolean;
  label?: string;
}

export default function DeleteThemeButton({
  themeId,
  onDeleted,
  themeName,
  isFavorite = false,
  label,
}: DeleteButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { removeCustomTheme } = useThemeStore();
  const handleDelete = async () => {
    setIsLoading(true);

    try {
      const result = await deleteTheme(themeId);

      if (result.success) {
        toast.success("The color scheme has been successfully deleted");

        // Close the dialog
        setIsOpen(false);
        removeCustomTheme(themeId);
        // Call the onDeleted callback if provided
        if (onDeleted) {
          onDeleted();
        }
      } else {
        toast.error("Error deleting color scheme");
      }
    } catch {
      toast.error("Error deleting color scheme");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        className={cn(
          "flex items-center gap-2 z-20 bg-transparent hover:bg-accent",
          label && "w-full"
        )}
        size="icon"
        onClick={() => setIsOpen(true)}
      >
        <Trash className={cn("text-destructive", label && "mr-2")} />
        {label && <span>{label}</span>}
      </Button>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete{" "}
              <span className="font-medium text-primary">{themeName}</span> and
              remove all associated data.
              {isFavorite && (
                <span className="text-destructive font-medium block mt-2">
                  This is one of your favorite color schemes.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault(); // Prevent the dialog from closing automatically
                handleDelete();
              }}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
