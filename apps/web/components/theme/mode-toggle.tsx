"use client";

import { <PERSON>, <PERSON> } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Switch } from "@chromify/ui/components/switch";
import { AIThemeMode } from "@/schema/theme";
import { ThemeState } from "../../app/(private)/theme/[id]/_components/streaming/types";

interface ThemeModeToggleProps {
  state: ThemeState;
  activeMode: AIThemeMode;
  setActiveMode: (mode: AIThemeMode) => void;
}

export function ThemeModeToggle({
  state,
  activeMode,
  setActiveMode,
}: ThemeModeToggleProps) {
  // Determine if dark mode is available based on completion status and data
  const hasDarkMode =
    state.completion.dark_colors &&
    Object.keys(state.displayTheme.dark_colors || {}).length > 0;

  return (
    <div className="flex items-center justify-end space-x-2">
      <Button
        size="icon"
        onClick={() => setActiveMode("light")}
        disabled={activeMode === "light" || !state.completion.colors}
      >
        <Sun className="h-4 w-4" />
      </Button>
      {(hasDarkMode || state.status.isStreaming) && (
        <Button
          size="icon"
          onClick={() => setActiveMode("dark")}
          disabled={activeMode === "dark" || !hasDarkMode}
        >
          <Moon className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}

export function ThemeModeToggleSwitch({
  state,
  activeMode,
  setActiveMode,
}: ThemeModeToggleProps) {
  const hasDarkMode =
    state.completion.dark_colors &&
    Object.keys(state.displayTheme.dark_colors || {}).length > 0;

  return (
    <div className="flex items-center justify-end space-x-2">
      <Sun className="h-4 w-4" />
      <Switch
        checked={activeMode === "dark"}
        onCheckedChange={(checked) => setActiveMode(checked ? "dark" : "light")}
        disabled={!hasDarkMode}
        aria-label="Toggle dark mode"
      />
      <Moon className="h-4 w-4" />
    </div>
  );
}
