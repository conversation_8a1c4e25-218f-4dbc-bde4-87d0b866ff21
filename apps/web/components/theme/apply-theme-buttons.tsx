"use client";

import { <PERSON><PERSON><PERSON>, SunIcon } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { useTheme } from "@/components/theme-provider";
import { useThemeStore } from "@/store/theme-store";
import type { DBTheme } from "@/types/theme";

type ApplyThemeButtonsProps = {
  theme: DBTheme;
};

// User use generated theme to change app's theme
function ApplyThemeButtons({ theme }: ApplyThemeButtonsProps) {
  const { setTheme, setMode } = useTheme();
  const { addCustomTheme } = useThemeStore();

  // Handle save of theme
  const handleApplyLightTheme = () => {
    // Create a minimal store theme with only essential data
    const storeTheme = {
      id: theme.id || `${theme.name}-${Date.now()}`, // Use ID from database or generate a unique one
      name: theme.name,
      format: theme.format,
      colors: theme.colors,
      dark_colors: theme.dark_colors,
    };

    // Add to custom themes
    addCustomTheme(storeTheme);

    // Apply the theme
    setTheme(storeTheme);
    setMode("light");
  };

  const handleApplyDarkTheme = () => {
    // Create a minimal store theme with only essential data
    const storeTheme = {
      id: theme.id || `${theme.name}-${Date.now()}`, // Use ID from database or generate a unique one
      name: theme.name,
      format: theme.format,
      colors: theme.colors,
      dark_colors: theme.dark_colors,
    };

    // Add to custom themes
    addCustomTheme(storeTheme);

    // Apply the theme
    setTheme(storeTheme);
    setMode("dark");
  };
  return (
    <div className="flex gap-2">
      <Button
        aria-label="Apply light theme"
        size={"icon"}
        onClick={handleApplyLightTheme}
      >
        <SunIcon />
      </Button>
      {theme.dark_colors && (
        <Button
          aria-label="Apply dark theme"
          size={"icon"}
          onClick={handleApplyDarkTheme}
        >
          <MoonIcon />
        </Button>
      )}
    </div>
  );
}

export default ApplyThemeButtons;
