import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>bL<PERSON>,
  B<PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator,
} from "@chromify/ui/components/breadcrumb";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@chromify/ui/components/card";
import { Skeleton } from "@chromify/ui/components/skeleton";

function ThemeCardsLoading() {
  const skeletonCards = Array(9).fill(null);

  return (
    <>
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem className="hidden md:block">
            Color Schemes
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>History</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="container mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-8 w-32" />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {skeletonCards.map((_, index) => (
            <Card
              key={index}
              className="overflow-hidden transition-all relative group p-2"
            >
              <CardHeader className="relative p-0">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-10 w-10 rounded-md" />
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-5 w-16 rounded-full" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </CardHeader>

              <CardContent className="space-y-6 p-2">
                <div className="flex gap-4 items-center">
                  <Skeleton className="h-4 w-12 ml-2" />
                </div>
                <div className="flex justify-between items-center">
                  <Skeleton className="h-9 w-24" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </CardContent>

              <CardFooter className="p-2">
                <Skeleton className="h-4 w-40" />
                <div className="absolute bottom-3 right-3">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-8 rounded-md" />
                    <Skeleton className="h-8 w-8 rounded-md" />
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
}

export default ThemeCardsLoading;
