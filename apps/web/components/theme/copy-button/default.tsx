"use client";

import { ClipboardCopy } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@chromify/ui/components/button";
import { useCopyToClipboard } from "@/hooks/use-copy-to-clipboard";

function CopyButton({ css }: { css: string }) {
  const { copyToClipboard } = useCopyToClipboard();
  return (
    <Button
      variant="secondary"
      size="icon"
      onClick={() => {
        copyToClipboard(css);
        toast.success("CSS copied!");
      }}
    >
      <ClipboardCopy className="size-4" />
    </Button>
  );
}

export default CopyButton;
