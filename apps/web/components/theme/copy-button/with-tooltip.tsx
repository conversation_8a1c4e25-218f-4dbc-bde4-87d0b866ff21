"use client";

import { ClipboardCopy } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { useCopyToClipboard } from "@/hooks/use-copy-to-clipboard";

function CopyButtonWithTooltip({ css }: { css: string }) {
  const { copyToClipboard } = useCopyToClipboard();
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            onClick={() => {
              copyToClipboard(css);
              toast.success("CSS copied!");
            }}
          >
            <ClipboardCopy className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p>Copy CSS</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default CopyButtonWithTooltip;
