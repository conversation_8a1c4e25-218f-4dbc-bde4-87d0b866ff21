"use client";

import { Co<PERSON> } from "lucide-react";
import { toast } from "sonner";

import { DropdownMenuItem } from "@chromify/ui/components/dropdown-menu";
import { useCopyToClipboard } from "@/hooks/use-copy-to-clipboard";

function CopyButtonWithDropdown({ css }: { css: string }) {
  const { copyToClipboard } = useCopyToClipboard();
  return (
    <DropdownMenuItem
      className="space-x-2"
      onSelect={() => {
        copyToClipboard(css);
        toast.success("CSS copied!");
      }}
    >
      <Copy className="size-4 text-primary" />
      <span className="text-xs">Copy CSS</span>
    </DropdownMenuItem>
  );
}

export default CopyButtonWithDropdown;
