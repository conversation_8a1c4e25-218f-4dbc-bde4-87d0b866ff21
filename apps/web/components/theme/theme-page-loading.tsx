import { Skeleton } from "@chromify/ui/components/skeleton";

function ThemePageLoading() {
  return (
    <div className="w-full py-4">
      <div>
        <div className="h-12 flex justify-between mb-6">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-6 w-32" />
        </div>
        <div className="space-y-6">
          {/* Header skeleton */}
          <div className="flex justify-between">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-40" />
          </div>

          {/* Badges skeleton */}
          <div className="flex flex-col items-start gap-2">
            <div className="flex flex-wrap gap-2 items-center">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-4 w-28" />
            </div>
          </div>
          {/* Description skeleton */}
          <div className="max-w-3xl space-y-4">
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-5 w-5/6" />
            <Skeleton className="h-5 w-5/6" />
          </div>
        </div>
      </div>

      <div className="mx-auto py-8 flex justify-between">
        <div className="w-full">
          <div className="flex space-x-2 w-full max-w-md mb-8">
            <Skeleton className="h-8 w-28" />
            <Skeleton className="h-8 w-28" />
            <Skeleton className="h-8 w-28" />
          </div>

          <div className="space-y-12">
            <div>
              <div className="mb-6">
                <Skeleton className="h-8 w-40 mb-2" />
                <Skeleton className="h-5 w-full max-w-lg" />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {Array(6)
                  .fill(0)
                  .map((_, i) => (
                    <div
                      key={i}
                      className="group relative overflow-hidden rounded-xl"
                    >
                      <Skeleton className="h-24 w-full" />
                    </div>
                  ))}
              </div>
            </div>

            <div>
              <div className="mb-6">
                <Skeleton className="h-8 w-40 mb-2" />
                <Skeleton className="h-5 w-full max-w-lg" />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {Array(6)
                  .fill(0)
                  .map((_, i) => (
                    <div
                      key={i}
                      className="group relative overflow-hidden rounded-xl"
                    >
                      <Skeleton className="h-24 w-full" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ThemePageLoading;
