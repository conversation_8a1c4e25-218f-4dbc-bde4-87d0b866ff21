import React, { memo } from "react";

/**
 * ShimmerText component that applies a shimmer effect to its children
 * Memoized to prevent unnecessary re-renders that could restart the animation
 */
const ShimmerText = memo(function ShimmerText({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div
      className="animate-shimmer inline-block text-transparent text-sm"
      style={{
        backgroundClip: "text",
        backgroundImage:
          "linear-gradient(-35deg, var(--foreground) 0%, var(--accent) 50%, var(--foreground) 100%)",
        backgroundSize: "200% 150%",
        backgroundPosition: "-100% 0",
        // Ensure animation continues from its current state rather than restarting
        animationPlayState: "running",
      }}
    >
      {children}
    </div>
  );
});

export default ShimmerText;
