function ThemeSvgDark() {
  return (
    <svg viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="800" height="450" rx="6" fill="#121212" />

      <rect x="0" y="0" width="220" height="450" fill="#1A1A1A" />

      <rect x="20" y="15" width="160" height="24" rx="4" fill="#1A1A1A" />
      <circle cx="30" cy="27" r="10" fill="#3FCF8E" />
      <rect x="45" y="21" width="80" height="12" rx="2" fill="#3FCF8E" />

      <rect
        x="20"
        y="60"
        width="150"
        height="32"
        rx="16"
        stroke="#3FCF8E"
        fill="#1A1A1A"
      />
      <rect x="40" y="72" width="80" height="8" rx="2" fill="#3FCF8E" />
      <rect x="135" y="72" width="8" height="8" rx="2" fill="#3FCF8E" />

      <rect x="12" y="115" width="180" height="20" rx="4" fill="#1A1A1A" />
      <rect x="12" y="143" width="180" height="32" rx="4" fill="#2A2A2A" />
      <rect x="40" y="153" width="60" height="12" rx="2" fill="#FFFFFF" />

      <rect x="12" y="175" width="180" height="32" rx="4" fill="#1A1A1A" />
      <rect x="40" y="185" width="60" height="12" rx="2" fill="#777777" />

      <rect x="12" y="224" width="180" height="20" rx="4" fill="#1A1A1A" />

      <rect x="12" y="254" width="180" height="32" rx="4" fill="#1A1A1A" />
      <rect x="40" y="264" width="80" height="12" rx="2" fill="#777777" />

      <rect x="12" y="286" width="180" height="32" rx="4" fill="#1A1A1A" />
      <rect x="40" y="296" width="80" height="12" rx="2" fill="#777777" />

      <rect x="12" y="318" width="180" height="32" rx="4" fill="#1A1A1A" />
      <rect x="40" y="328" width="80" height="12" rx="2" fill="#777777" />

      <rect x="220" y="0" width="580" height="450" fill="#121212" />

      <rect x="245" y="30" width="500" height="20" rx="4" fill="#121212" />
      <rect x="245" y="35" width="80" height="10" rx="2" fill="#777777" />
      <rect x="345" y="35" width="60" height="10" rx="2" fill="#FFFFFF" />

      <rect x="290" y="75" width="400" height="30" rx="4" fill="#121212" />
      <rect x="290" y="80" width="260" height="20" rx="2" fill="#FFFFFF" />

      <rect x="680" y="75" width="90" height="24" rx="12" fill="#222222" />
      <rect x="685" y="82" width="80" height="10" rx="2" fill="#777777" />

      <rect
        x="290"
        y="120"
        width="230"
        height="195"
        rx="8"
        fill="#1E1E1E"
        stroke="#333333"
        strokeWidth="1"
      />
      <circle cx="310" cy="150" r="15" fill="#FF7A50" />
      <rect x="335" y="145" width="100" height="15" rx="2" fill="#FFFFFF" />
      <rect x="335" y="170" width="150" height="10" rx="2" fill="#121212" />
      <rect x="335" y="190" width="150" height="10" rx="2" fill="#121212" />
      <rect x="335" y="210" width="150" height="10" rx="2" fill="#121212" />

      <rect x="310" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
      <rect x="335" y="254" width="40" height="8" rx="2" fill="#121212" />

      <rect x="305" y="290" width="200" height="12" rx="2" fill="#555555" />

      <rect
        x="540"
        y="120"
        width="230"
        height="195"
        rx="8"
        fill="#1E1E1E"
        stroke="#333333"
        strokeWidth="1"
      />
      <circle cx="560" cy="150" r="15" fill="#3FCF8E" />
      <rect x="585" y="145" width="100" height="15" rx="2" fill="#FFFFFF" />

      <rect x="815" y="150" width="18" height="18" rx="9" fill="#3FCF8E" />
      <rect x="820" y="155" width="8" height="8" rx="2" fill="#121212" />

      <rect x="585" y="170" width="150" height="10" rx="2" fill="#121212" />
      <rect x="585" y="190" width="150" height="10" rx="2" fill="#121212" />
      <rect x="585" y="210" width="150" height="10" rx="2" fill="#121212" />

      <rect x="560" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
      <rect x="585" y="254" width="40" height="8" rx="2" fill="#121212" />

      <rect x="660" y="245" width="90" height="26" rx="13" fill="#333333" />
      <rect x="685" y="254" width="40" height="8" rx="2" fill="#777777" />

      <rect x="555" y="290" width="200" height="12" rx="2" fill="#555555" />
    </svg>
  );
}

function ThemeSvglight() {
  return (
    <svg viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="800" height="450" rx="6" fill="#F8F9FA" />

      <rect x="0" y="0" width="220" height="450" fill="#FFFFFF" />

      <rect x="20" y="15" width="160" height="24" rx="4" fill="#FFFFFF" />
      <circle cx="30" cy="27" r="10" fill="#3FCF8E" />
      <rect x="45" y="21" width="80" height="12" rx="2" fill="#3FCF8E" />

      <rect
        x="20"
        y="60"
        width="150"
        height="32"
        rx="16"
        stroke="#3FCF8E"
        fill="#FFFFFF"
      />
      <rect x="40" y="72" width="80" height="8" rx="2" fill="#3FCF8E" />
      <rect x="135" y="72" width="8" height="8" rx="2" fill="#3FCF8E" />

      <rect x="12" y="115" width="180" height="20" rx="4" fill="#FFFFFF" />
      <rect x="12" y="143" width="180" height="32" rx="4" fill="#F0F0F0" />
      <rect x="40" y="153" width="60" height="12" rx="2" fill="#333333" />

      <rect x="12" y="175" width="180" height="32" rx="4" fill="#FFFFFF" />
      <rect x="40" y="185" width="60" height="12" rx="2" fill="#777777" />

      <rect x="12" y="224" width="180" height="20" rx="4" fill="#FFFFFF" />

      <rect x="12" y="254" width="180" height="32" rx="4" fill="#FFFFFF" />
      <rect x="40" y="264" width="80" height="12" rx="2" fill="#777777" />

      <rect x="12" y="286" width="180" height="32" rx="4" fill="#FFFFFF" />
      <rect x="40" y="296" width="80" height="12" rx="2" fill="#777777" />

      <rect x="12" y="318" width="180" height="32" rx="4" fill="#FFFFFF" />
      <rect x="40" y="328" width="80" height="12" rx="2" fill="#777777" />

      <rect x="220" y="0" width="580" height="450" fill="#F8F9FA" />

      <rect x="245" y="30" width="500" height="20" rx="4" fill="#F8F9FA" />
      <rect x="245" y="35" width="80" height="10" rx="2" fill="#777777" />
      <rect x="345" y="35" width="60" height="10" rx="2" fill="#333333" />

      <rect x="290" y="75" width="400" height="30" rx="4" fill="#F8F9FA" />
      <rect x="290" y="80" width="260" height="20" rx="2" fill="#333333" />

      <rect x="680" y="75" width="90" height="24" rx="12" fill="#EEEEEE" />
      <rect x="685" y="82" width="80" height="10" rx="2" fill="#777777" />

      <rect
        x="290"
        y="120"
        width="230"
        height="195"
        rx="8"
        fill="#FFFFFF"
        stroke="#E5E5E5"
        strokeWidth="1"
      />
      <circle cx="310" cy="150" r="15" fill="#FF7A50" />
      <rect x="335" y="145" width="100" height="15" rx="2" fill="#333333" />
      <rect x="335" y="170" width="150" height="10" rx="2" fill="#DDDDDD" />
      <rect x="335" y="190" width="150" height="10" rx="2" fill="#DDDDDD" />
      <rect x="335" y="210" width="150" height="10" rx="2" fill="#DDDDDD" />

      <rect x="310" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
      <rect x="335" y="254" width="40" height="8" rx="2" fill="#FFFFFF" />

      <rect x="305" y="290" width="200" height="12" rx="2" fill="#999999" />

      <rect
        x="540"
        y="120"
        width="230"
        height="195"
        rx="8"
        fill="#FFFFFF"
        stroke="#E5E5E5"
        strokeWidth="1"
      />
      <circle cx="560" cy="150" r="15" fill="#3FCF8E" />
      <rect x="585" y="145" width="100" height="15" rx="2" fill="#333333" />

      <rect x="815" y="150" width="18" height="18" rx="9" fill="#3FCF8E" />
      <rect x="820" y="155" width="8" height="8" rx="2" fill="#FFFFFF" />

      <rect x="585" y="170" width="150" height="10" rx="2" fill="#DDDDDD" />
      <rect x="585" y="190" width="150" height="10" rx="2" fill="#DDDDDD" />
      <rect x="585" y="210" width="150" height="10" rx="2" fill="#DDDDDD" />

      <rect x="560" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
      <rect x="585" y="254" width="40" height="8" rx="2" fill="#FFFFFF" />

      <rect x="660" y="245" width="90" height="26" rx="13" fill="#DDDDDD" />
      <rect x="685" y="254" width="40" height="8" rx="2" fill="#777777" />

      <rect x="555" y="290" width="200" height="12" rx="2" fill="#999999" />
    </svg>
  );
}

function ThemeSvgThemed() {
  return (
    <svg viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
      {/* Background */}
      <rect
        width="800"
        height="450"
        rx="6"
        style={{ fill: "var(--background)" }}
      />

      {/* Sidebar */}
      <rect
        x="0"
        y="0"
        width="220"
        height="450"
        style={{ fill: "var(--secondary)" }}
      />

      {/* Logo Area */}
      <rect
        x="20"
        y="15"
        width="160"
        height="24"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <circle cx="30" cy="27" r="10" style={{ fill: "var(--primary)" }} />
      <rect
        x="45"
        y="21"
        width="80"
        height="12"
        rx="2"
        style={{ fill: "var(--primary)" }}
      />

      {/* New Color Scheme Button */}
      <rect
        x="20"
        y="60"
        width="150"
        height="32"
        rx="16"
        style={{
          fill: "var(--secondary)",
          stroke: "var(--primary)",
          strokeWidth: "1.5",
        }}
      />
      <rect
        x="40"
        y="72"
        width="80"
        height="8"
        rx="2"
        style={{ fill: "var(--primary)" }}
      />
      <rect
        x="135"
        y="72"
        width="8"
        height="8"
        rx="2"
        style={{ fill: "var(--primary)" }}
      />

      {/* Sidebar Menu Items */}
      <rect
        x="12"
        y="115"
        width="180"
        height="20"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <rect
        x="12"
        y="143"
        width="180"
        height="32"
        rx="4"
        style={{ fill: "var(--accent)" }}
      />
      <rect
        x="40"
        y="153"
        width="60"
        height="12"
        rx="2"
        style={{ fill: "var(--foreground)" }}
      />

      <rect
        x="12"
        y="175"
        width="180"
        height="32"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <rect
        x="40"
        y="185"
        width="60"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      <rect
        x="12"
        y="224"
        width="180"
        height="20"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />

      <rect
        x="12"
        y="254"
        width="180"
        height="32"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <rect
        x="40"
        y="264"
        width="80"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      <rect
        x="12"
        y="286"
        width="180"
        height="32"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <rect
        x="40"
        y="296"
        width="80"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      <rect
        x="12"
        y="318"
        width="180"
        height="32"
        rx="4"
        style={{ fill: "var(--secondary)" }}
      />
      <rect
        x="40"
        y="328"
        width="80"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      {/* Main Content Area - Background already set */}

      {/* Navigation Breadcrumb */}
      <rect
        x="245"
        y="30"
        width="500"
        height="20"
        rx="4"
        style={{ fill: "var(--background)" }}
      />
      <rect
        x="245"
        y="35"
        width="80"
        height="10"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />
      <rect
        x="345"
        y="35"
        width="60"
        height="10"
        rx="2"
        style={{ fill: "var(--foreground)" }}
      />

      {/* Content Header */}
      <rect
        x="290"
        y="75"
        width="400"
        height="30"
        rx="4"
        style={{ fill: "var(--background)" }}
      />
      <rect
        x="290"
        y="80"
        width="260"
        height="20"
        rx="2"
        style={{ fill: "var(--foreground)" }}
      />

      {/* Generate Stats */}
      <rect
        x="680"
        y="75"
        width="90"
        height="24"
        rx="12"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="685"
        y="82"
        width="80"
        height="10"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      {/* Theme Cards */}
      {/* Card 1 */}
      <rect
        x="290"
        y="120"
        width="230"
        height="195"
        rx="8"
        style={{
          fill: "var(--card)",
          stroke: "var(--border)",
          strokeWidth: "1",
        }}
      />
      <circle cx="310" cy="150" r="15" style={{ fill: "var(--destructive)" }} />
      <rect
        x="335"
        y="145"
        width="100"
        height="15"
        rx="2"
        style={{ fill: "var(--card-foreground)" }}
      />
      <rect
        x="335"
        y="170"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="335"
        y="190"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="335"
        y="210"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />

      <rect
        x="310"
        y="245"
        width="90"
        height="26"
        rx="13"
        style={{ fill: "var(--primary)" }}
      />
      <rect
        x="335"
        y="254"
        width="40"
        height="8"
        rx="2"
        style={{ fill: "var(--primary-foreground)" }}
      />

      <rect
        x="305"
        y="290"
        width="200"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      {/* Card 2 */}
      <rect
        x="540"
        y="120"
        width="230"
        height="195"
        rx="8"
        style={{
          fill: "var(--card)",
          stroke: "var(--border)",
          strokeWidth: "1",
        }}
      />
      <circle cx="560" cy="150" r="15" style={{ fill: "var(--primary)" }} />
      <rect
        x="585"
        y="145"
        width="100"
        height="15"
        rx="2"
        style={{ fill: "var(--card-foreground)" }}
      />

      <rect
        x="585"
        y="170"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="585"
        y="190"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="585"
        y="210"
        width="150"
        height="10"
        rx="2"
        style={{ fill: "var(--muted)" }}
      />

      <rect
        x="560"
        y="245"
        width="90"
        height="26"
        rx="13"
        style={{ fill: "var(--primary)" }}
      />
      <rect
        x="585"
        y="254"
        width="40"
        height="8"
        rx="2"
        style={{ fill: "var(--primary-foreground)" }}
      />

      <rect
        x="660"
        y="245"
        width="90"
        height="26"
        rx="13"
        style={{ fill: "var(--muted)" }}
      />
      <rect
        x="685"
        y="254"
        width="40"
        height="8"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />

      <rect
        x="555"
        y="290"
        width="200"
        height="12"
        rx="2"
        style={{ fill: "var(--muted-foreground)" }}
      />
    </svg>
  );
}

function ThemeSvgSystem() {
  return (
    <svg viewBox="0 0 800 450" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="800" height="450" rx="6" fill="#121212" />

      <clipPath id="lightSideClip">
        <path d="M460 0 L800 0 L800 450 L250 450 Z" />
      </clipPath>

      <g clipPath="url(#lightSideClip)">
        <rect width="800" height="450" fill="#F8F9FA" />

        <rect x="0" y="0" width="220" height="450" fill="#FFFFFF" />

        <rect x="245" y="30" width="500" height="20" rx="4" fill="#F8F9FA" />
        <rect x="245" y="35" width="80" height="10" rx="2" fill="#777777" />
        <rect x="345" y="35" width="60" height="10" rx="2" fill="#333333" />

        <rect
          x="540"
          y="120"
          width="230"
          height="195"
          rx="8"
          fill="#FFFFFF"
          stroke="#E5E5E5"
          strokeWidth="1"
        />
        <circle cx="560" cy="150" r="15" fill="#3FCF8E" />
        <rect x="585" y="145" width="100" height="15" rx="2" fill="#333333" />

        <rect x="585" y="170" width="150" height="10" rx="2" fill="#DDDDDD" />
        <rect x="585" y="190" width="150" height="10" rx="2" fill="#DDDDDD" />
        <rect x="585" y="210" width="150" height="10" rx="2" fill="#DDDDDD" />

        <rect x="560" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
        <rect x="585" y="254" width="40" height="8" rx="2" fill="#FFFFFF" />

        <rect x="660" y="245" width="90" height="26" rx="13" fill="#DDDDDD" />
        <rect x="685" y="254" width="40" height="8" rx="2" fill="#777777" />

        <rect x="555" y="290" width="200" height="12" rx="2" fill="#999999" />

        <rect
          x="290"
          y="330"
          width="230"
          height="100"
          rx="8"
          fill="#FFFFFF"
          stroke="#E5E5E5"
          strokeWidth="1"
        />
        <rect x="310" y="350" width="190" height="10" rx="2" fill="#DDDDDD" />
        <rect x="310" y="370" width="190" height="10" rx="2" fill="#DDDDDD" />
        <rect x="310" y="390" width="150" height="10" rx="2" fill="#DDDDDD" />
      </g>

      <rect x="20" y="15" width="160" height="24" rx="4" fill="#1A1A1A" />
      <circle cx="30" cy="27" r="10" fill="#3FCF8E" />
      <rect x="45" y="21" width="80" height="12" rx="2" fill="#3FCF8E" />

      <rect
        x="20"
        y="60"
        width="150"
        height="32"
        rx="16"
        stroke="#3FCF8E"
        fill="#1A1A1A"
      />
      <rect x="40" y="72" width="80" height="8" rx="2" fill="#3FCF8E" />
      <rect x="135" y="72" width="8" height="8" rx="2" fill="#3FCF8E" />

      <rect x="12" y="115" width="180" height="20" rx="4" fill="#1A1A1A" />
      <rect x="12" y="143" width="180" height="32" rx="4" fill="#2A2A2A" />
      <rect x="40" y="153" width="60" height="12" rx="2" fill="#FFFFFF" />

      <rect x="12" y="175" width="180" height="32" rx="4" fill="#1A1A1A" />
      <rect x="40" y="185" width="60" height="12" rx="2" fill="#777777" />

      <rect x="290" y="75" width="160" height="30" rx="4" fill="#121212" />
      <rect x="290" y="80" width="150" height="20" rx="2" fill="#FFFFFF" />

      <rect
        x="290"
        y="120"
        width="230"
        height="195"
        rx="8"
        fill="#1E1E1E"
        stroke="#333333"
        strokeWidth="1"
      />
      <circle cx="310" cy="150" r="15" fill="#FF7A50" />
      <rect x="335" y="145" width="100" height="15" rx="2" fill="#FFFFFF" />
      <rect x="335" y="170" width="150" height="10" rx="2" fill="#333333" />
      <rect x="335" y="190" width="150" height="10" rx="2" fill="#333333" />
      <rect x="335" y="210" width="150" height="10" rx="2" fill="#333333" />

      <rect x="310" y="245" width="90" height="26" rx="13" fill="#3FCF8E" />
      <rect x="335" y="254" width="40" height="8" rx="2" fill="#121212" />

      <rect x="305" y="290" width="200" height="12" rx="2" fill="#555555" />

      <line
        x1="250"
        y1="450"
        x2="460"
        y2="0"
        stroke="#FFFFFF"
        strokeWidth="2"
      />

      <rect x="350" y="20" width="150" height="30" rx="4" fill="#333333" />
      <rect x="365" y="30" width="120" height="10" rx="2" fill="#FFFFFF" />
    </svg>
  );
}

export { ThemeSvgSystem, ThemeSvgDark, ThemeSvgThemed, ThemeSvglight };
