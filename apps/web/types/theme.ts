import { AIColor } from "@/schema/theme";

// Type for theme data returned from database
export interface DBTheme {
  id: string;
  request_id: string;
  name: string;
  theme_description: string;
  format: "hsl" | "oklch";
  colors: AIColor;
  dark_colors?: AIColor;
  created_at: string;
  is_favorite: boolean;
  is_public: boolean;
  shared_id?: string;
  analysis?: string[];
  primary_hue?: string;
  theme_requests?: {
    user_prompt: string;
    include_sidebar: boolean;
    include_chart: boolean;
    border_radius: string;
  };
}