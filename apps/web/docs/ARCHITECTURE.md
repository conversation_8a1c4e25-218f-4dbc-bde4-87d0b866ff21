# Chromify Architecture: Technical Design & Implementation Guide

This document outlines a comprehensive architecture for Chromify, an AI-powered color scheme generator. It addresses the current implementation and proposes optimizations for a scalable, maintainable architecture using Next.js App Router and server components.

## Table of Contents

- [Current Architecture Analysis](#current-architecture-analysis)
- [Proposed Architecture](#proposed-architecture)
- [Server Component Strategy](#server-component-strategy)
- [API Structure](#api-structure)
- [Frontend Structure](#frontend-structure)
- [Data Flow](#data-flow)
- [State Management Approach](#state-management-approach)
- [Implementation Guide](#implementation-guide)

## Current Architecture Analysis

### Strengths

- Strong component separation with clear responsibilities
- Type safety throughout with TypeScript + Zod
- Streaming API responses for better UX during theme generation
- Persistent state management for user preferences
- Good handling of both text and image-based theme generation

### Areas for Improvement

1. **Overuse of "use client"**

   - All components are marked with "use client" directives
   - Many components could benefit from being server components

2. **Redundant API Routes**

   - API routes used for operations that could be server components
   - Read operations creating unnecessary data fetching waterfalls

3. **State Management Complexity**

   - Current approach mixes UI state and domain state
   - Consider splitting into smaller, more focused stores

4. **Image Processing**

   - Large base64 strings in API requests are inefficient
   - Consider client-side resizing or direct uploads to storage

5. **Error Handling**

   - Current error recovery is minimal
   - Errors could be more informative and actionable

6. **Loading State**
   - Artificial loading steps may mislead users
   - Consider real progress indicators tied to actual processing stages

## Proposed Architecture

### Three-Phase Architecture with Server Components

The proposed architecture separates concerns while leveraging server components for better performance:

#### Phase 1: Request & Submission (Client + API)

- Client component handles form submission
- API route creates request record with unique ID
- Immediate redirect to dedicated page

#### Phase 2: Real-time Generation (Server Component + SSE)

- Initial data fetched in server component
- SSE (Server-Sent Events) for real-time updates
- Progressive UI updates during generation
- Robust error handling and recovery options

#### Phase 3: Post-Processing & Display (Server Component)

- Completed themes rendered directly by server component
- Calculate accessibility scores
- Generate theme variants
- Enhanced SEO with server-rendered content

## Server Component Strategy

### When to Use Server Components

Server components are ideal for:

1. **Data Fetching**

   - Initial page loads
   - Static or dynamic data that doesn't require client interactivity
   - SEO-critical content

2. **Heavy Computations**

   - Theme calculations
   - Accessibility scoring
   - Variant generation

3. **Reduced Client JS**
   - Theme preview components
   - Static UI elements
   - Layout components

### When to Use Client Components

Client components are necessary for:

1. **Interactive Elements**

   - Theme creation form
   - Color adjustments
   - Export options

2. **Real-time Updates**

   - Generation progress indicators
   - Interactive previews

3. **Browser APIs**
   - Image upload/processing
   - Clipboard interactions
   - Local storage access

### Server/Client Component Pattern

```
┌────────────────────────────────────────┐
│ Server Component (Page)                │
│ ┌────────────────────────────────────┐ │
│ │ Static UI Elements                 │ │
│ │                                    │ │
│ │ ┌────────────────────────────────┐ │ │
│ │ │ Client Component Islands       │ │ │
│ │ │                                │ │ │
│ │ └────────────────────────────────┘ │ │
│ │                                    │ │
│ └────────────────────────────────────┘ │
└────────────────────────────────────────┘
```

## API Structure

```
/app/
├── api/                    # API Routes for Client Components
│   ├── theme/
│   │   ├── create/         # Create new theme request
│   │   │   └── route.ts
│   │   └── [id]/
│   │       ├── generate/   # Start generation
│   │       │   └── route.ts
│   │       └── stream/     # SSE for real-time updates
│   │           └── route.ts
│   └── collection/
│       ├── save/           # Save theme to collection
│       │   └── route.ts
│       └── [id]/remove/    # Remove from collection
│           └── route.ts
├── theme/
│   ├── create/             # Server Component - Theme creation page
│   │   └── page.tsx
│   ├── [id]/               # Server Component - Theme view page
│   │   └── page.tsx
│   └── collection/         # Server Component - Collection page
│       └── page.tsx
```

## Frontend Structure

```
/app/
├── (public)/                # Public routes
│   ├── page.tsx             # Landing page (Server Component)
│   ├── pricing/             # Pricing information (Server Component)
│   └── features/            # Feature showcase (Server Component)
│   └── market/              # User shared themes (Server Component)
├── (private)/               # Auth-protected routes with middleware
│   ├── dashboard/           # User dashboard
│   │   └── page.tsx         # Server Component with client islands
│   ├── theme/
│   │   ├── create/          # Theme creation
│   │   │   ├── page.tsx     # Server Component shell
│   │   │   └── _components/ # Client Components
│   │   │       ├── theme-form.tsx
│   │   │       └── image-uploader.tsx
│   │   ├── [id]/            # Theme generation/view
│   │   │   ├── page.tsx     # Server Component
│   │   │   └── _components/ # Mixed Server/Client Components
│   │   │       ├── theme-preview.tsx     # Server Component
│   │   │       └── generation-client.tsx # Client Component
│   │   └── collection/      # User's saved themes
│   │       ├── page.tsx     # Server Component
│   │       └── _components/ # Mixed Server/Client Components
│   │           ├── theme-grid.tsx       # Server Component
│   │           └── collection-actions.tsx # Client Component
│   └── settings/            # User settings
│       └── page.tsx         # Server Component with client islands
├── api/                     # API routes (as outlined previously)
├── lib/                     # Shared utilities
│   ├── db/                  # Database access layer
│   │   ├── themes.ts        # Theme-related database operations
│   │   └── users.ts         # User-related database operations
│   ├── actions/             # Server Actions
│   │   ├── theme-actions.ts # Theme-related actions
│   │   └── user-actions.ts  # User-related actions
│   └── utils/               # Utility functions
│       ├── colors.ts        # Color manipulation utilities
│       └── theme-utils.ts   # Theme-related utilities
```

## Data Flow

### Complete Workflow with Server Components

```
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                       USER FLOW                                          │
└──────────────────────────────────────────────────────────────────────────────────────────┘
   │
   ▼
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│  Dashboard  │───────▶│Theme Creator│───────▶│Theme Viewer │───────▶│ Collection  │
│(Server Comp)│        │(Mixed S/C)  │        │(Mixed S/C)  │        │(Server Comp)│
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
                              │                      │                      │
                              ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                    COMPONENT LAYER                                        │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│DashboardView│        │  ThemeForm  │        │ThemeProgress│        │ThemeLibrary │
│(Server Comp)│        │(Client Comp)│        │(Client Comp)│        │(Server Comp)│
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
      │                      │                      │                      │
      │                      │                      │                      │
      │                      ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                     HOOKS LAYER                                           │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│ useAnalytics│        │useThemeForm │        │useGeneration│        │useCollection│
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
      │                      │                      │                      │
      │                      │                      │                      │
      │                      ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                     STATE LAYER                                           │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│UserPrefStore│        │FormDataStore│        │ GenerStore  │        │LibraryStore │
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
      │                      │                      │                      │
      │                      │                      │                      │
      │                      ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                  SERVICE LAYER                                            │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│ userService │        │themeService │        │ sseService  │        │exportService│
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
      │                      │                      │                      │
      │                      │                      │                      │
      ▼                      ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                               API + SERVER COMPONENT LAYER                                │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────┐        ┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│Server Comp  │        │/api/themes/ │        │/api/themes/ │        │Server Comp  │
│Data Access  │        │   create    │        │ [id]/stream │        │Data Access  │
└─────────────┘        └─────────────┘        └─────────────┘        └─────────────┘
      │                      │                      │                      │
      │                      │                      │                      │
      ▼                      ▼                      ▼                      ▼
┌──────────────────────────────────────────────────────────────────────────────────────────┐
│                                   DATABASE LAYER                                          │
└──────────────────────────────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│                                 Database Service                                         │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

### Theme Creation Flow (Updated)

1. User visits `/theme/create` page

   - Server component renders initial page shell
   - Client components hydrate for interactivity

2. User fills out ThemeForm (client component)

   - Form data validated client-side through Zod schema
   - On submit, themeService.createRequest() called

3. API request to `/api/theme/create` creates record in database
   - Returns theme ID to client
   - Router pushes to `/theme/[id]` page

### Theme Generation Flow (Updated)

1. `/theme/[id]` page loads:

   - **Server component** fetches initial theme data directly from database
   - Renders based on theme status:
     - For completed themes: Full server-rendered theme preview
     - For pending/generating: Server component shell with client component islands

2. For pending/generating themes:

   - Client component connects to SSE endpoint
   - Progress updates streamed via SSE
   - UI updates shown in real-time

3. Upon completion:
   - Final theme data received
   - Page refreshed or updated to show server-rendered theme
   - Option to save to collection presented

### Theme Collection Flow (Updated)

1. User navigates to `/theme/collection` page

   - **Server component** directly fetches user's theme collection
   - Renders theme grid with pagination
   - Client components hydrate for interactive elements (delete, export, etc.)

2. User saves theme through action button
   - Client component calls API endpoint
   - API request to `/api/theme/collection/save`
   - Theme added to user's collection in database
   - Page refreshed to show updated collection

## State Management Approach

### Server vs. Client State

With server components, state management becomes more nuanced:

- **Server-Fetched Data**: Use server components for initial data
- **Interactive State**: Use client-side state management for UI interactions
- **Shared State**: Use React Context for state that spans multiple components

### Recommended State Structure

```
├── Server State (Database)
│   ├── User data
│   ├── Themes data
│   └── Collection data
│
├── Client State (React State/Context)
│   ├── UI State
│   │   ├── Form inputs
│   │   ├── Modal visibility
│   │   └── Active tabs/sections
│   │
│   ├── Generation State
│   │   ├── Progress status
│   │   ├── Partial results
│   │   └── Error states
│   │
│   └── Ephemeral State
│       ├── Hover states
│       ├── Animations
│       └── Temporary selections
│
└── Persistent Client State (localStorage)
    ├── User preferences
    ├── Recent searches
    └── UI customizations
```

## Implementation Guide

### 1. Server Component Implementation

Create effective server components:

```typescript
// app/theme/[id]/page.tsx
import { Suspense } from 'react';
import { getThemeById } from '@/lib/db/themes';
import { ThemePreview } from './_components/theme-preview';
import ThemeGenerationClient from './_components/theme-generation-client';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';
import { notFound } from 'next/navigation';

export default async function ThemePage({ params }: { params: { id: string } }) {
  // Server-side data fetching
  const theme = await getThemeById(params.id);

  if (!theme) {
    notFound();
  }

  // For completed themes, use server rendering
  if (theme.status === 'completed') {
    return (
      <Suspense fallback={<LoadingSkeleton />}>
        <ThemePreview theme={theme.theme} />
      </Suspense>
    );
  }

  // For pending/generating themes, use client component
  return (
    <ThemeGenerationClient
      initialTheme={theme}
      themeId={params.id}
    />
  );
}
```

### 2. Client Components for Interactivity

Clearly mark client components for interactive elements:

```typescript
// app/theme/[id]/_components/theme-generation-client.tsx
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ThemeProgress } from './theme-progress';
import { PartialThemePreview } from './partial-theme-preview';
import { useEventSource } from '@/hooks/use-event-source';
import { Theme, ThemeStatus } from '@/types';

interface ThemeGenerationClientProps {
  initialTheme: Theme;
  themeId: string;
}

export default function ThemeGenerationClient({
  initialTheme,
  themeId,
}: ThemeGenerationClientProps) {
  const router = useRouter();
  const [theme, setTheme] = useState(initialTheme);

  // Connect to SSE for streaming updates
  const { data, error } = useEventSource(`/api/theme/${themeId}/stream`);

  useEffect(() => {
    if (data) {
      setTheme(prevTheme => ({
        ...prevTheme,
        ...data
      }));

      // If theme generation completed, refresh the page to get server-rendered version
      if (data.status === 'completed') {
        router.refresh();
      }
    }
  }, [data, router]);

  // Handle errors
  if (error) {
    return (
      <div className="error-container">
        <h2>Error generating theme</h2>
        <p>{error.message}</p>
        <button onClick={() => router.refresh()}>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div>
      <ThemeProgress
        status={theme.status as ThemeStatus}
        progress={theme.progress || 0}
      />

      {theme.partialTheme && (
        <PartialThemePreview theme={theme.partialTheme} />
      )}
    </div>
  );
}
```

### 3. Database Access Layer

Create a consistent database access layer:

```typescript
// lib/db/themes.ts
import { db } from "@/lib/db";
import {
  PaginationOptions,
  Theme,
  ThemeFormValues,
  ThemeStatus,
} from "@/types";

export async function getThemeById(id: string): Promise<Theme | null> {
  return db.theme.findUnique({
    where: { id },
    include: {
      user: true,
    },
  });
}

export async function getUserThemes(options: {
  userId: string;
  page: number;
  sort: string;
}): Promise<{ themes: Theme[]; totalPages: number }> {
  const { userId, page = 1, sort = "newest" } = options;
  const itemsPerPage = 12;
  const skip = (page - 1) * itemsPerPage;

  // Determine sort order
  const orderBy =
    sort === "newest"
      ? { createdAt: "desc" as const }
      : { createdAt: "asc" as const };

  // Get themes and count
  const [themes, count] = await Promise.all([
    db.theme.findMany({
      where: { userId },
      orderBy,
      skip,
      take: itemsPerPage,
    }),
    db.theme.count({ where: { userId } }),
  ]);

  return {
    themes,
    totalPages: Math.ceil(count / itemsPerPage),
  };
}

export async function createTheme(
  values: ThemeFormValues,
  userId: string
): Promise<Theme> {
  return db.theme.create({
    data: {
      status: "pending",
      userId,
      inputs: values,
      progress: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  });
}

export async function updateThemeStatus(
  id: string,
  status: ThemeStatus,
  data?: any
): Promise<Theme> {
  return db.theme.update({
    where: { id },
    data: {
      status,
      ...(data ? { theme: data } : {}),
      updatedAt: new Date(),
      ...(status === "completed" ? { completedAt: new Date() } : {}),
    },
  });
}
```

### 4. Custom Hooks

Create custom hooks for client components:

```typescript
// hooks/use-event-source.ts
"use client";

import { useEffect, useState } from "react";

interface UseEventSourceOptions {
  initialData?: any;
  onMessage?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function useEventSource(
  url: string,
  options: UseEventSourceOptions = {}
) {
  const [data, setData] = useState(options.initialData || null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let eventSource: EventSource | null = null;
    let mounted = true;

    const connectEventSource = () => {
      eventSource = new EventSource(url);

      eventSource.onmessage = (event) => {
        try {
          const parsedData = JSON.parse(event.data);

          if (mounted) {
            setData(parsedData);
            options.onMessage?.(parsedData);
          }

          // Close connection if we receive a completion or error status
          if (
            parsedData.status === "completed" ||
            parsedData.status === "failed"
          ) {
            eventSource?.close();
          }
        } catch (err) {
          if (mounted) {
            const error =
              err instanceof Error ? err : new Error("Unknown error");
            setError(error);
            options.onError?.(error);
          }
        }
      };

      eventSource.onerror = () => {
        if (mounted) {
          const error = new Error("EventSource connection failed");
          setError(error);
          options.onError?.(error);
          eventSource?.close();

          // Try to reconnect after a delay
          setTimeout(() => {
            if (mounted) {
              connectEventSource();
            }
          }, 3000);
        }
      };
    };

    connectEventSource();

    return () => {
      mounted = false;
      eventSource?.close();
    };
  }, [url, options]);

  return { data, error };
}
```

### 5. Integration with API Routes

Maintain necessary API routes:

```typescript
// app/api/theme/[id]/stream/route.ts
import { NextRequest } from "next/server";
import { streamObject } from "ai-sdk";

import { getThemeById } from "@/lib/db/themes";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const themeId = params.id;
  const theme = await getThemeById(themeId);

  if (!theme) {
    return new Response("Theme not found", { status: 404 });
  }

  // If already completed, return final state
  if (theme.status === "completed") {
    return new Response(
      JSON.stringify({
        status: "completed",
        theme: theme.theme,
        progress: 100,
      }),
      {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      }
    );
  }

  // Start streaming
  const stream = streamObject<{
    status: string;
    progress: number;
    partialTheme?: any;
    error?: string;
  }>();

  // Begin generation process in background
  generateThemeInBackground(themeId, stream);

  return new Response(stream.readable, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  });
}

async function generateThemeInBackground(
  themeId: string,
  stream: ReturnType<typeof streamObject>
) {
  try {
    // Simulate generation steps
    // In production, this would call your AI service

    // Start the generation process
    stream.write({
      status: "generating",
      progress: 10,
    });

    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Partial results
    stream.write({
      status: "generating",
      progress: 50,
      partialTheme: {
        // Partial theme data
      },
    });

    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Final results
    const finalTheme = {
      // Complete theme data
    };

    // Update database with final theme
    await updateThemeStatus(themeId, "completed", finalTheme);

    // Send final update
    stream.write({
      status: "completed",
      progress: 100,
      partialTheme: finalTheme,
    });

    stream.close();
  } catch (error) {
    console.error("Theme generation error:", error);

    // Update database with error status
    await updateThemeStatus(themeId, "failed");

    // Send error to client
    stream.write({
      status: "failed",
      progress: 0,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    stream.close();
  }
}
```

## Benefits of this Architecture

This updated architecture provides several key benefits:

1. **Performance Improvements**

   - Reduced client-side JavaScript
   - Faster initial page loads
   - Elimination of data fetching waterfalls

2. **Better SEO**

   - Server-rendered content for completed themes
   - Improved metadata handling
   - Faster indexing by search engines

3. **Improved Developer Experience**

   - Clear separation between server and client concerns
   - Reduced boilerplate code
   - Direct database access for server components

4. **Enhanced User Experience**

   - Faster time-to-first-meaningful-paint
   - Progressive enhancement for users with JS disabled
   - More responsive UI with focused client hydration

5. **Scalability**

   - Better separation of read and write operations
   - Reduced server load for read-heavy operations
   - More efficient caching opportunities

6. **Maintainability**
   - Clearer code organization
   - Better error handling
   - Simplified data flow

This architecture provides a solid foundation for Chromify while maintaining flexibility for future evolution, taking full advantage of Next.js App Router's server component model.
