# Chromify Data Flow & Database Design (Supabase with Clerk Integration)

## Overview

This document outlines the data flow architecture and database schema design for Chromify, an AI-powered color scheme generator, optimized for Supabase implementation with Clerk authentication. The application is built using Next.js App Router with server components, server actions, and integrates the Vercel AI SDK for streaming theme generation.

## Data Flow Architecture

### Core Flow Diagram

```mermaid
flowchart TD
    A[User] -->|1. Fill Form| B[Theme Create Page]
    B -->|2. Submit Form| C[Server Action: createTheme]
    C -->|3. Create Record| D[(Supabase DB)]
    C -->|4. Return Theme ID| B
    B -->|5. router.push| E[Theme/[id] Page]
    E -->|6. Fetch Theme| D
    E -->|7a. If pending| F[ThemeStreaming Component]
    E -->|7b. If completed| G[ThemePreview Component]
    E -->|7c. If failed| H[Error Component]
    F -->|8. Request streaming| I[/api/theme/id/streaming]
    I -->|9. Start AI processing| J[Vercel AI SDK]
    J -->|10. Stream partial results| I
    I -->|11. Stream updates| F
    J -->|12. Complete process| I
    I -->|13. Update complete record| D
    I -->|14. Final update| F
    F -->|15. Refresh page| E
    D -->|Real-time updates| F
```

### Authentication Flow with Clerk

```mermaid
flowchart TD
    A[User] -->|1. Sign in| B[Clerk Auth]
    B -->|2. Generate token| C[Clerk Session]
    D[App Client] -->|3. Request with token| E[Supabase Client]
    E -->|4. Include token in request| F[(Supabase DB)]
    F -->|5. Verify token with Clerk| G[Clerk JWT Verification]
    F -->|6. Apply RLS policies| H[Data with RLS Applied]
    H -->|7. Return filtered data| D
```

### Detailed Flow Steps

1. **Initial User Interaction**

   - User signs in with Clerk authentication
   - User fills out the theme generation form on the `theme/create` page
   - Form includes description, options (format, dark mode, sidebar, chart colors)
   - Optional image upload for image-based theme generation using Supabase Storage

2. **Form Submission**

   - Server action `createTheme` is triggered
   - Form data is validated against `ThemeFormSchema`
   - Creates a new record in the database with "pending" status
   - Uses Clerk user ID from the session token for user identification
   - Returns the generated theme ID to the client

3. **Redirection to Theme Page**

   - Client receives theme ID and navigates to `/theme/[id]` using Next.js router
   - Theme page fetches the theme data based on ID from Supabase using Clerk authentication

4. **Theme Status Handling**

   - Theme page checks the status of the theme from the database:
     - If "completed": Renders `ThemePreview` component with the generated theme
     - If "pending": Renders `ThemeStreaming` component to handle streaming generation
     - If "failed": Renders error component with retry option
   - Implements Supabase real-time subscriptions for status updates

5. **Streaming Generation Process**

   - `ThemeStreaming` component calls `/api/theme/[id]/streaming` endpoint
   - API route uses Vercel AI SDK's `streamObject` to generate the theme
   - AI model processes the request based on theme description/image
   - Partial theme objects are streamed back to the client in real-time
   - Client updates the UI to show generation progress

6. **Completion and Storage**
   - When theme generation completes, the final theme is saved to the database
   - Status is updated from "pending" to "completed"
   - User can now save, customize, or export the generated theme
   - Supabase real-time updates notify client of completion

## Database Schema Design (Supabase with Clerk Integration)

### Clerk Integration Setup

```sql
-- Function to extract Clerk user ID from JWT token
-- This replaces the standard auth.uid() function when using Clerk
CREATE OR REPLACE FUNCTION requesting_user_id()
RETURNS TEXT
LANGUAGE sql STABLE AS $$
  SELECT nullif(current_setting('request.jwt.claims', true)::json->>'sub', '')::text;
$$;
```

### Core Tables

#### User Profiles Table

```sql
-- Using Clerk for authentication instead of Supabase Auth
-- Create a user_profiles table linked to Clerk user IDs
CREATE TABLE user_profiles (
  id TEXT PRIMARY KEY, -- Using Clerk's string-based user IDs
  name TEXT,
  subscription_tier TEXT CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- RLS Policy using requesting_user_id() function instead of auth.uid()
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  USING (requesting_user_id() = id);
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  USING (requesting_user_id() = id);
```

#### Theme Requests Table

```sql
-- Custom enum types
CREATE TYPE theme_status AS ENUM ('pending', 'completed', 'failed');
CREATE TYPE theme_format AS ENUM ('hsl', 'oklch');
CREATE TYPE theme_model AS ENUM ('deepseek-chat', 'anthropic', 'google');

CREATE TABLE theme_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL, -- Changed to TEXT to match Clerk user IDs
  description TEXT NOT NULL,
  format theme_format NOT NULL DEFAULT 'hsl',
  include_dark_mode BOOLEAN NOT NULL DEFAULT false,
  include_sidebar BOOLEAN NOT NULL DEFAULT false,
  include_chart BOOLEAN NOT NULL DEFAULT false,
  image_path TEXT, -- Uses Supabase Storage path
  model theme_model NOT NULL DEFAULT 'deepseek-chat',
  status theme_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  processing_time_ms INTEGER,
  error_message TEXT
);

-- Indexes
CREATE INDEX idx_theme_requests_user_status ON theme_requests(user_id, status);
CREATE INDEX idx_theme_requests_created_at ON theme_requests(created_at DESC);

-- RLS Policies using requesting_user_id() function
ALTER TABLE theme_requests ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own requests"
  ON theme_requests FOR SELECT
  USING (requesting_user_id() = user_id);
CREATE POLICY "Users can create theme requests"
  ON theme_requests FOR INSERT
  WITH CHECK (requesting_user_id() = user_id);
```

#### Themes Table

```sql
CREATE TABLE themes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id UUID NOT NULL REFERENCES theme_requests(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL, -- Changed to TEXT to match Clerk user IDs
  name TEXT NOT NULL,
  description TEXT,
  format theme_format NOT NULL DEFAULT 'hsl',
  colors JSONB NOT NULL,
  dark_colors JSONB,
  analysis JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_favorite BOOLEAN NOT NULL DEFAULT false,
  is_public BOOLEAN NOT NULL DEFAULT false,
  version INTEGER NOT NULL DEFAULT 1,
  CONSTRAINT check_colors_format CHECK (jsonb_typeof(colors) = 'object')
);

-- Indexes
CREATE INDEX idx_themes_user_id ON themes(user_id);
CREATE INDEX idx_themes_user_favorite ON themes(user_id, is_favorite) WHERE is_favorite = true;
CREATE INDEX idx_themes_public ON themes(is_public, created_at DESC) WHERE is_public = true;
CREATE INDEX idx_themes_format ON themes(format);
CREATE INDEX idx_themes_colors ON themes USING gin (colors);
CREATE INDEX idx_themes_dark_colors ON themes USING gin (dark_colors);

-- RLS Policies using requesting_user_id() function
ALTER TABLE themes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can see their own themes"
  ON themes FOR SELECT
  USING (requesting_user_id() = user_id OR is_public = true);
CREATE POLICY "Users can update their own themes"
  ON themes FOR UPDATE
  USING (requesting_user_id() = user_id);
CREATE POLICY "Users can delete their own themes"
  ON themes FOR DELETE
  USING (requesting_user_id() = user_id);
```

#### Theme Versions Table

```sql
CREATE TABLE theme_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  theme_id UUID NOT NULL REFERENCES themes(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  colors JSONB NOT NULL,
  dark_colors JSONB,
  change_description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by TEXT NOT NULL, -- Changed to TEXT to match Clerk user IDs
  UNIQUE(theme_id, version_number)
);

-- Indexes
CREATE INDEX idx_theme_versions_theme_id ON theme_versions(theme_id);

-- RLS Policies using requesting_user_id() with join to themes table
ALTER TABLE theme_versions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can see versions of visible themes"
  ON theme_versions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM themes
      WHERE themes.id = theme_versions.theme_id
      AND (themes.user_id = requesting_user_id() OR themes.is_public = true)
    )
  );
```

### Storage Configuration

```sql
-- Supabase Storage bucket for theme images
-- Execute in Supabase dashboard or via API

-- Storage bucket creation (via Supabase dashboard)
-- Name: theme-images
-- Public/Private: Private

-- Storage RLS policies using requesting_user_id() function
CREATE POLICY "Users can upload their own images"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'theme-images' AND
    auth.uid() = (storage.foldername(name))[1]::uuid
  );

CREATE POLICY "Users can view their own images"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'theme-images' AND
    auth.uid() = (storage.foldername(name))[1]::uuid
  );
```

### Database Functions

```sql
-- Theme creation function updated for Clerk user IDs
CREATE OR REPLACE FUNCTION create_theme(
  p_user_id TEXT, -- Changed to TEXT to match Clerk user IDs
  p_description TEXT,
  p_format theme_format,
  p_include_dark_mode BOOLEAN,
  p_include_sidebar BOOLEAN,
  p_include_chart BOOLEAN,
  p_image_path TEXT,
  p_model theme_model
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_request_id UUID;
BEGIN
  INSERT INTO theme_requests (
    user_id,
    description,
    format,
    include_dark_mode,
    include_sidebar,
    include_chart,
    image_path,
    model,
    status
  ) VALUES (
    p_user_id,
    p_description,
    p_format,
    p_include_dark_mode,
    p_include_sidebar,
    p_include_chart,
    p_image_path,
    p_model,
    'pending'
  ) RETURNING id INTO v_request_id;

  RETURN v_request_id;
END;
$$;

-- Theme version creation function updated for Clerk
CREATE OR REPLACE FUNCTION create_theme_version(
  p_theme_id UUID,
  p_colors JSONB,
  p_dark_colors JSONB,
  p_change_description TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_version INTEGER;
  v_new_version_id UUID;
  v_user_id TEXT := requesting_user_id(); -- Using requesting_user_id() function
BEGIN
  -- Check ownership
  IF NOT EXISTS (SELECT 1 FROM themes WHERE id = p_theme_id AND user_id = v_user_id) THEN
    RAISE EXCEPTION 'Not authorized to create version for this theme';
  END IF;

  -- Get current version
  SELECT version INTO v_current_version FROM themes WHERE id = p_theme_id;

  -- Begin transaction
  BEGIN
    -- Create new version record
    INSERT INTO theme_versions (
      theme_id,
      version_number,
      colors,
      dark_colors,
      change_description,
      created_by
    ) VALUES (
      p_theme_id,
      v_current_version + 1,
      p_colors,
      p_dark_colors,
      p_change_description,
      v_user_id
    ) RETURNING id INTO v_new_version_id;

    -- Update theme with new data
    UPDATE themes
    SET
      colors = p_colors,
      dark_colors = p_dark_colors,
      version = v_current_version + 1,
      updated_at = NOW()
    WHERE id = p_theme_id;

    RETURN v_new_version_id;
  END;
END;
$$;

-- Theme completion function updated for Clerk
CREATE OR REPLACE FUNCTION complete_theme(
  p_request_id UUID,
  p_name TEXT,
  p_colors JSONB,
  p_dark_colors JSONB,
  p_analysis JSONB,
  p_processing_time_ms INTEGER
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_theme_id UUID;
  v_user_id TEXT;
  v_format theme_format;
  v_description TEXT;
BEGIN
  -- Get request details
  SELECT user_id, format, description
  INTO v_user_id, v_format, v_description
  FROM theme_requests
  WHERE id = p_request_id;

  -- Create theme
  INSERT INTO themes (
    request_id,
    user_id,
    name,
    description,
    format,
    colors,
    dark_colors,
    analysis
  ) VALUES (
    p_request_id,
    v_user_id,
    p_name,
    v_description,
    v_format,
    p_colors,
    p_dark_colors,
    p_analysis
  ) RETURNING id INTO v_theme_id;

  -- Update request status
  UPDATE theme_requests
  SET
    status = 'completed',
    processing_time_ms = p_processing_time_ms,
    updated_at = NOW()
  WHERE id = p_request_id;

  RETURN v_theme_id;
END;
$$;
```

### Materialized Views

```sql
-- Public themes showcase
CREATE MATERIALIZED VIEW public_themes_showcase AS
SELECT
  t.id,
  t.name,
  t.description,
  t.format,
  t.colors,
  t.dark_colors,
  t.created_at,
  up.name as creator_name,
  t.analysis->'colorNames' as color_names,
  t.analysis->'colorHarmonies' as color_harmonies
FROM themes t
JOIN user_profiles up ON t.user_id = up.id
WHERE t.is_public = true
ORDER BY t.created_at DESC;

-- Create index on the materialized view
CREATE INDEX idx_public_themes_showcase_created_at
ON public_themes_showcase(created_at DESC);

-- Refresh function
CREATE OR REPLACE FUNCTION refresh_public_themes_showcase()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public_themes_showcase;
  RETURN NULL;
END;
$$;

-- Trigger to refresh the view when public themes change
CREATE TRIGGER refresh_public_themes_showcase_trigger
AFTER INSERT OR UPDATE OR DELETE ON themes
FOR EACH STATEMENT
WHEN (pg_trigger_depth() = 0)
EXECUTE FUNCTION refresh_public_themes_showcase();
```

### Audit Logging

```sql
-- Audit log table
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  record_id UUID NOT NULL,
  operation TEXT NOT NULL,
  old_data JSONB,
  new_data JSONB,
  changed_by TEXT, -- Changed to TEXT to match Clerk user IDs
  changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index on the audit log
CREATE INDEX idx_audit_logs_table_record
ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_changed_at
ON audit_logs(changed_at DESC);

-- Audit function for themes updated for Clerk
CREATE OR REPLACE FUNCTION process_themes_audit()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_logs (table_name, record_id, operation, new_data, changed_by)
    VALUES ('themes', NEW.id, 'INSERT', to_jsonb(NEW), requesting_user_id());
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_logs (table_name, record_id, operation, old_data, new_data, changed_by)
    VALUES ('themes', NEW.id, 'UPDATE', to_jsonb(OLD), to_jsonb(NEW), requesting_user_id());
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_logs (table_name, record_id, operation, old_data, changed_by)
    VALUES ('themes', OLD.id, 'DELETE', to_jsonb(OLD), requesting_user_id());
  END IF;
  RETURN NULL;
END;
$$;

-- Create audit trigger
CREATE TRIGGER themes_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON themes
FOR EACH ROW EXECUTE FUNCTION process_themes_audit();
```

## Relationship Diagram (Enhanced)

```mermaid
erDiagram
    CLERK_USERS ||--o{ USER_PROFILES : has
    CLERK_USERS ||--o{ THEME_REQUESTS : creates
    CLERK_USERS ||--o{ THEMES : owns
    CLERK_USERS ||--o{ THEME_VERSIONS : modifies
    CLERK_USERS ||--o{ AUDIT_LOGS : generates
    THEME_REQUESTS ||--o| THEMES : generates
    THEMES ||--o{ THEME_VERSIONS : tracks_history
    STORAGE_OBJECTS ||--o{ THEME_REQUESTS : references
```

## Next.js Integration with Clerk and Supabase

### Setup Environment Variables

```bash
# Supabase related env vars
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Clerk related env vars
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
```

### Supabase Client Setup with Clerk

```typescript
// lib/supabase.ts
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client-side Supabase client with Clerk authentication
export function createClerkSupabaseClient() {
  const { useSession } = require("@clerk/nextjs");
  const { session } = useSession();

  return createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${session?.getToken() || ""}`,
      },
    },
  });
}

// Server-side Supabase client with Clerk authentication
export async function createServerSupabaseClient() {
  const { auth } = await import("@clerk/nextjs/server");

  return createClient(supabaseUrl, supabaseAnonKey, {
    async accessToken() {
      return (await auth()).getToken();
    },
  });
}

// Admin client (for privileged operations)
export const getAdminClient = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  return createClient(supabaseUrl, supabaseServiceKey);
};
```

### Real-time Subscription Example

```typescript
// components/ThemeStreaming.tsx
import { useEffect, useState } from "react";

import { createClerkSupabaseClient } from "@/lib/supabase";

export function ThemeStreaming({ requestId }: { requestId: string }) {
  const [status, setStatus] = useState<"pending" | "completed" | "failed">(
    "pending"
  );
  const supabase = createClerkSupabaseClient();

  useEffect(() => {
    // Set up real-time subscription
    const subscription = supabase
      .channel(`theme-request-${requestId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "theme_requests",
          filter: `id=eq.${requestId}`,
        },
        (payload) => {
          const newStatus = payload.new.status;
          setStatus(newStatus);

          if (newStatus === "completed") {
            // Redirect to theme page or refresh
            window.location.reload();
          }
        }
      )
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [requestId, supabase]);

  // Rest of component...
}
```

### Server Action Example with Clerk Integration

```typescript
// actions/theme.ts
"use server";

import { revalidatePath } from "next/cache";
import { auth } from "@clerk/nextjs/server";

import { createServerSupabaseClient, getAdminClient } from "@/lib/supabase";

export async function createTheme(formData: FormData) {
  const description = formData.get("description") as string;
  const format = formData.get("format") as "hsl" | "oklch";
  const includeDarkMode = formData.has("includeDarkMode");
  const includeSidebar = formData.has("includeSidebar");
  const includeChart = formData.has("includeChart");
  const imageFile = formData.get("image") as File;

  // Get current user from Clerk
  const { userId } = auth();

  if (!userId) {
    throw new Error("Authentication required");
  }

  // Get Supabase client with Clerk auth
  const supabase = await createServerSupabaseClient();

  // Upload image if provided
  let imagePath = null;
  if (imageFile.size > 0) {
    const fileExt = imageFile.name.split(".").pop();
    const fileName = `${userId}/${Date.now()}.${fileExt}`;

    const { data, error } = await supabase.storage
      .from("theme-images")
      .upload(fileName, imageFile);

    if (error) throw error;
    imagePath = data.path;
  }

  // Create theme request using database function
  const { data, error } = await getAdminClient().rpc("create_theme", {
    p_user_id: userId,
    p_description: description,
    p_format: format,
    p_include_dark_mode: includeDarkMode,
    p_include_sidebar: includeSidebar,
    p_include_chart: includeChart,
    p_image_path: imagePath,
    p_model: "anthropic",
  });

  if (error) throw error;

  // Trigger background process for theme generation
  const response = await fetch("/api/theme/generate", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ requestId: data }),
  });

  if (!response.ok) {
    throw new Error("Failed to trigger theme generation");
  }

  revalidatePath("/themes");
  return data; // Returns the request ID
}
```

## Clerk Integration Setup Steps

1. **Create Clerk Application**

   - Sign up for Clerk at https://clerk.com
   - Create a new application in the Clerk Dashboard
   - Configure authentication methods as needed

2. **Enable Supabase Integration in Clerk**

   - Navigate to the Supabase integration setup in Clerk Dashboard
   - Enable the Supabase integration to generate properly formatted JWTs
   - Save the Clerk domain provided on this page

3. **Configure Supabase for Clerk Authentication**

   - In Supabase Dashboard, go to Authentication > Sign In / Up
   - Add Clerk as a provider
   - Enter the Clerk domain from the previous step

4. **Add Clerk to Your Next.js App**

   - Install Clerk SDK: `npm install @clerk/nextjs`
   - Create or update `.env.local` with Clerk environment variables
   - Add Clerk provider to your app layout
   - Implement auth middleware for protected routes

5. **Update Database Schema for Clerk User IDs**
   - Execute the SQL to create the `requesting_user_id()` function
   - Modify tables and RLS policies to work with Clerk's string-based user IDs

## Deployment Considerations

1. **Migrations and Versioning**

   - Use Supabase CLI for local development and migrations
   - Add to your package.json:

   ```json
   "scripts": {
     "db:migration:new": "supabase migration new",
     "db:migration:apply": "supabase db push"
   }
   ```

2. **Edge Functions for AI Processing**

   - Create Supabase Edge Function for AI Processing:

   ```bash
   supabase functions new generate-theme
   ```

   - Deploy:

   ```bash
   supabase functions deploy generate-theme
   ```

3. **CI/CD Pipeline with Clerk Support**

   ```yaml
   - name: Setup Supabase CLI
     uses: supabase/setup-cli@v1
   - name: Deploy migrations
     run: supabase db push
   ```

4. **Webhook Setup for User Synchronization**
   - Consider setting up Clerk webhooks to synchronize user data to your Supabase database
   - Create webhook endpoints to handle user creation, update, and deletion events

## Security Best Practices

1. **API Security**

   - Use service role only on the server side
   - Implement JWT verification for sensitive operations
   - Set up a secure CORS policy

2. **Data Encryption**

   - Consider column-level encryption for sensitive data:

   ```sql
   CREATE EXTENSION IF NOT EXISTS pgcrypto;

   -- Example encryption function
   CREATE OR REPLACE FUNCTION encrypt_sensitive_data(p_data TEXT)
   RETURNS TEXT
   LANGUAGE plpgsql
   AS $$
   BEGIN
     RETURN encode(pgp_sym_encrypt(p_data, current_setting('app.encryption_key')), 'base64');
   END;
   $$;
   ```

3. **Rate Limiting**
   - Implement rate limiting for public API endpoints
   - Consider using Clerk's rate limiting features for authentication endpoints

## Notes for Implementation

1. Leverage Clerk's authentication features (social login, multi-factor authentication, etc.) while using Supabase for database operations.

2. The schema is designed with Row Level Security (RLS) as a first-class concept, ensuring data access is properly controlled at the database level.

3. Use Clerk's session token to authenticate Supabase requests instead of Supabase's built-in authentication.

4. Consider implementing Clerk webhooks to keep user profiles in sync with the Supabase database.

5. The latest native Supabase integration with Clerk (as of April 2025) eliminates the need to share JWT secrets between services.

6. Implement proper error handling for cases where token verification might fail.

7. Regularly review Clerk's documentation for updates on the Supabase integration, as it's been actively developed.

8. Take advantage of Clerk's enhanced authentication features like passwordless login, social providers, and multi-factor authentication while keeping your Supabase database and RLS policies.

9. Consider implementing a proper user onboarding flow that creates necessary database records when a new user signs up through Clerk.
