import { ChangeEvent, useRef, useState } from "react";

interface UseImageUploadOptions {
  onImageChange?: (imageData: string | undefined) => void;
}

interface UseImageUploadReturn {
  imagePreview: string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleUploadClick: () => void;
  handleFileUpload: (event: ChangeEvent<HTMLInputElement>) => void;
  handleDeleteImage: () => void;
  isImageUploaded: boolean;
}

/**
 * Custom hook to manage image upload functionality
 *
 * @param options Configuration options for the hook
 * @returns Object containing image upload state and handlers
 */
export function useImageUpload(
  options?: UseImageUploadOptions
): UseImageUploadReturn {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle click on the upload button
  const handleUploadClick = (): void => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file upload
  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>): void => {
    const file = event.target.files?.[0];

    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();

      reader.onload = (e) => {
        if (e.target?.result) {
          const imageData = e.target.result as string;
          setImagePreview(imageData);
          options?.onImageChange?.(imageData);
        }
      };

      reader.readAsDataURL(file);
    }

    // Reset the input so the same file can be uploaded again if needed
    if (event.target) {
      event.target.value = "";
    }
  };

  // Delete uploaded image
  const handleDeleteImage = (): void => {
    setImagePreview(null);
    options?.onImageChange?.(undefined);
  };

  return {
    imagePreview,
    fileInputRef: fileInputRef as React.RefObject<HTMLInputElement>,
    handleUploadClick,
    handleFileUpload,
    handleDeleteImage,
    isImageUploaded: !!imagePreview,
  };
}
