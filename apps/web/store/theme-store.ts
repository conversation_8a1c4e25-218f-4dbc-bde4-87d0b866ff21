import { create } from "zustand";
import { persist } from "zustand/middleware";

import { SITE_THEME_STORE_KEY } from "@/constants/site";
import {
  defaultDarkTheme,
  defaultLightTheme,
} from "@/constants/theme/default-themes";
import { AIThemeMode } from "@/schema/theme";

// Define a minimal type for themes in the store that includes only essential data
type StoreTheme = {
  id?: string; // Database ID
  name: string; // Theme name for display
  format: "hsl" | "oklch"; // Color format
  colors: Record<string, string>; // Light mode colors
  dark_colors?: Record<string, string>; // Optional dark mode colors
};

interface ThemeState {
  // Current mode (light, dark, system)
  mode: AIThemeMode | "system";
  // Currently selected theme
  currentTheme: StoreTheme;
  // List of available predefined themes
  availableThemes: StoreTheme[];
  // Custom user themes (including AI generated)
  customThemes: StoreTheme[];
  // Actions
  setMode: (mode: AIThemeMode | "system") => void;
  setCurrentTheme: (theme: StoreTheme) => void;
  addCustomTheme: (theme: StoreTheme) => void;
  removeCustomTheme: (themeId: string) => void;
}

// Create the store with persistence
export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      mode: "system",
      currentTheme: defaultLightTheme,
      availableThemes: [defaultLightTheme, defaultDarkTheme],
      customThemes: [],

      setMode: (mode) => set({ mode }),

      setCurrentTheme: (theme) => set({ currentTheme: theme }),

      addCustomTheme: (theme) =>
        set((state) => ({
          customThemes:
            theme.id && state.customThemes.some((t) => t.id === theme.id)
              ? state.customThemes.map((t) => (t.id === theme.id ? theme : t))
              : [...state.customThemes, theme],
        })),

      removeCustomTheme: (themeId) =>
        set((state) => ({
          customThemes: state.customThemes.filter((t) => t.id !== themeId),
          // Reset to default theme if the removed theme was selected
          currentTheme:
            state.currentTheme.id === themeId
              ? state.mode === "dark"
                ? defaultDarkTheme
                : defaultLightTheme
              : state.currentTheme,
        })),
    }),
    {
      name: SITE_THEME_STORE_KEY,
      // Only store essential theme data to minimize local storage usage
      partialize: (state) => ({
        mode: state.mode,
        currentTheme: {
          id: state.currentTheme.id,
          name: state.currentTheme.name,
          format: state.currentTheme.format,
          colors: state.currentTheme.colors,
          dark_colors: state.currentTheme.dark_colors,
        },
        customThemes: state.customThemes.map((theme) => ({
          id: theme.id,
          name: theme.name,
          format: theme.format,
          colors: theme.colors,
          dark_colors: theme.dark_colors,
        })),
      }),
    }
  )
);
