import { create } from "zustand";

// Define the store state type
interface ChatState {
  // Map of chat IDs to their titles
  chatTitles: Record<string, string>;
  // Current active chat ID
  currentChatId: string | null;

  // Actions
  updateChatTitle: (chatId: string, title: string) => void;
  setCurrentChatId: (chatId: string | null) => void;
  removeChatTitle: (chatId: string) => void;
}

/**
 * Chat store for managing chat titles across components
 *
 * This store provides real-time updates to chat titles without requiring
 * a page refresh. It complements the database storage by providing
 * immediate UI updates.
 */
export const useChatStore = create<ChatState>()((set) => ({
  // Initial state
  chatTitles: {},
  currentChatId: null,

  // Actions
  updateChatTitle: (chatId, title) =>
    set((state) => ({
      chatTitles: {
        ...state.chatTitles,
        [chatId]: title,
      },
    })),

  setCurrentChatId: (chatId) =>
    set(() => ({
      currentChatId: chatId,
    })),

  removeChatTitle: (chatId) =>
    set((state) => {
      // Create a new object without the specified chatId
      const { [chatId]: _, ...remainingTitles } = state.chatTitles;
      return { chatTitles: remainingTitles };
    }),
}));
