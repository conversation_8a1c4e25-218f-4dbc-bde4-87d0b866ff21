{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "typecheck": "tsc --noEmit", "supabase:types": "supabase gen types typescript --linked > lib/types/supabase.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.8", "@ai-sdk/deepseek": "^0.2.8", "@ai-sdk/google": "^1.2.8", "@ai-sdk/openai": "^1.3.21", "@ai-sdk/react": "^1.2.6", "@chromify/ui": "workspace:*", "@clerk/nextjs": "^6.13.0", "@hookform/resolvers": "^5.0.1", "@supabase/supabase-js": "^2.49.4", "ai": "^4.3.2", "date-fns": "^4.1.0", "dedent": "^1.5.3", "exa-js": "^1.6.13", "framer-motion": "^12.6.3", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "marked": "^15.0.11", "motion": "^12.6.3", "next": "^15.3.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "recharts": "^2.15.2", "resumable-stream": "^2.0.0", "sonner": "^2.0.3", "use-debounce": "^10.0.4", "use-stick-to-bottom": "^1.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@chromify/eslint-config": "workspace:*", "@chromify/typescript-config": "workspace:*", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.24.0", "supabase": "^2.20.12", "typescript": "^5.7.3"}}