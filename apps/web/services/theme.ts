"server only";

import { auth } from "@clerk/nextjs/server";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import { AITheme } from "@/schema/theme";

const client = createServerSupabaseClient();

const THEME_FIELDS = `
  id,
  request_id,
  name,
  theme_description,
  format,
  colors,
  dark_colors,
  created_at,
  is_favorite,
  is_public,
  shared_id,
  analysis,
  theme_requests!inner(user_prompt, include_sidebar, include_chart,border_radius)
`;

// Fetch theme data from database based on the ID in the URL
export const getTheme = async (themeId: string) => {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }
  const { data: themeData, error: themeError } = await client
    .from("themes")
    .select(THEME_FIELDS)
    .eq("request_id", themeId)
    .single();

  return { themeData, themeError };
};

// Fetch theme data status from database based on the ID in the URL
export const getThemeIdByRequest = async (requestId: string) => {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }
  const { data: requestData, error: requestError } = await client
    .from("theme_requests")
    .select("*")
    .eq("id", requestId)
    .single();
  return { requestData, requestError };
};

export async function recordThemeStreamingFailure(
  id: string,
  error: unknown
): Promise<void> {
  try {
    await client.rpc("fail_theme", {
      p_request_id: id,
      p_error_message: error instanceof Error ? error.message : "Unknown error",
    });
  } catch (dbError) {
    console.error("Failed to record theme failure:", dbError);
  }
}

/**
 * Sets up a timeout to automatically fail a theme request if it takes too long
 * @param requestId - The ID of the theme request
 * @param timeoutMs - Timeout in milliseconds (default: 5 minutes)
 * @returns A function to cancel the timeout
 */
export function setupThemeGenerationTimeout(
  requestId: string,
  timeoutMs = 5 * 60 * 1000 // 5 minutes default timeout
): () => void {
  const timeoutId = setTimeout(async () => {
    try {
      // Check if the request is still pending
      const { data } = await client
        .from("theme_requests")
        .select("status")
        .eq("id", requestId)
        .single();

      // Only fail the request if it's still pending
      if (data?.status === "pending") {
        await recordThemeStreamingFailure(
          requestId,
          new Error(
            `Theme generation timed out after ${timeoutMs / 1000} seconds`
          )
        );
        console.warn(
          `Theme request ${requestId} timed out and was marked as failed`
        );
      }
    } catch (error) {
      console.error("Error in theme generation timeout handler:", error);
    }
  }, timeoutMs);

  // Return a function to cancel the timeout
  return () => clearTimeout(timeoutId);
}

// Save theme to database with error handling
export async function saveThemeToDatabase(
  id: string,
  theme: AITheme | null,
  processingTime: number
) {
  if (!theme) return null;

  try {
    const { data, error } = await client.rpc("complete_theme", {
      p_request_id: id,
      p_name: theme.name || "AI Generated Theme",
      p_theme_description: theme.description || "",
      p_colors: theme.colors,
      p_dark_colors: theme.dark_colors || null,
      p_analysis: theme.analysis || [],
      p_primary_hue: theme.primaryHue || null,
      p_processing_time_ms: processingTime,
    });
    if (error) {
      console.error("Database Error:", error);
      throw error; // Re-throw to handle at a higher level if needed
    }
    return data;
  } catch (error) {
    console.error("Database Error:", error);
    throw error; // Re-throw to handle at a higher level if needed
  }
}
