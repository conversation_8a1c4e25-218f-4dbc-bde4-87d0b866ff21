"server only";

import { auth } from "@clerk/nextjs/server";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import { DBTheme } from "@/types/theme"; // Import the type

// Common theme fields to select
const THEME_FIELDS = `
  id,
  request_id,
  name,
  theme_description,
  format,
  colors,
  dark_colors,
  created_at,
  is_favorite,
  is_public,
  primary_hue
`;

/**
 * Fetches recent themes for the current user
 * @param limit Number of themes to fetch (default: 3)
 * @returns Array of recent themes
 */
export async function getRecentThemes(limit = 3): Promise<DBTheme[]> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }

  const client = createServerSupabaseClient();

  const { data, error } = await client
    .from("themes")
    .select(THEME_FIELDS)
    .eq("user_id", userId)
    .order("created_at", { ascending: false })
    .limit(limit);

  if (error) {
    console.error("Error fetching recent themes:", error);
    return [];
  }

  return data || [];
}

/**
 * Fetches starred (favorite) themes for the current user
 * @param limit Number of themes to fetch (default: 3)
 * @returns Array of starred themes
 */
export async function getStarredThemes(limit = 3): Promise<DBTheme[]> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }

  const client = createServerSupabaseClient();

  const { data, error } = await client
    .from("themes")
    .select(THEME_FIELDS)
    .eq("user_id", userId)
    .eq("is_favorite", true)
    .order("created_at", { ascending: false })
    .limit(limit);

  if (error) {
    console.error("Error fetching starred themes:", error);
    return [];
  }

  return data || [];
}

/**
 * Fetches published (public) themes for the current user
 * @param limit Number of themes to fetch (default: 3)
 * @returns Array of published themes
 */
export async function getPublishedThemes(limit = 3): Promise<DBTheme[]> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }

  const client = createServerSupabaseClient();

  const { data, error } = await client
    .from("themes")
    .select(THEME_FIELDS)
    .eq("user_id", userId)
    .eq("is_public", true)
    .order("created_at", { ascending: false })
    .limit(limit);

  if (error) {
    console.error("Error fetching published themes:", error);
    return [];
  }

  return data || [];
}

/**
 * Fetches theme statistics for the current user
 * @returns Object containing theme counts
 */
export async function getThemeStats(): Promise<{
  totalThemes: number;
  totalStarred: number;
  totalPublished: number;
}> {
  const { userId } = await auth();
  if (!userId) return { totalThemes: 0, totalStarred: 0, totalPublished: 0 };
  const client = createServerSupabaseClient();
  // Execute all count queries in parallel for better performance
  const [totalThemesResult, totalStarredResult, totalPublishedResult] =
    await Promise.all([
      client
        .from("themes")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId),
      client
        .from("themes")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId)
        .eq("is_favorite", true),
      client
        .from("themes")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId)
        .eq("is_public", true),
    ]);

  return {
    totalThemes: totalThemesResult.count || 0,
    totalStarred: totalStarredResult.count || 0,
    totalPublished: totalPublishedResult.count || 0,
  };
}

/**
 * Fetches theme creation history data for the current user
 * @param days Number of days to fetch data for (default: 10)
 * @returns Object containing theme creation and view data arrays
 */
export async function getThemeHistoryData(days = 10): Promise<{
  themeCreationData: number[];
  themeViewsData: number[];
}> {
  const { userId } = await auth();
  if (!userId) return { themeCreationData: [], themeViewsData: [] };

  // Get the date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Format dates for Supabase query
  const formattedStartDate = startDate.toISOString();
  const formattedEndDate = endDate.toISOString();
  const client = createServerSupabaseClient();
  try {
    // Get theme creation counts by day
    const { data: creationData, error: creationError } = await client
      .from("themes")
      .select("created_at")
      .eq("user_id", userId)
      .gte("created_at", formattedStartDate)
      .lte("created_at", formattedEndDate)
      .order("created_at", { ascending: true });

    if (creationError) {
      console.error("Error fetching theme creation data:", creationError);
      return {
        themeCreationData: Array(days).fill(0),
        themeViewsData: Array(days).fill(0),
      };
    }

    // Process the data to get counts per day
    const dailyCounts = Array(days).fill(0);

    creationData?.forEach((theme) => {
      const themeDate = new Date(theme.created_at);
      const dayDiff = Math.floor(
        (endDate.getTime() - themeDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      if (dayDiff >= 0 && dayDiff < days) {
        dailyCounts[days - 1 - dayDiff]++;
      }
    });

    // For now, we'll use a derived calculation for views since we don't have actual view data
    // In a real implementation, you would fetch this from a views or analytics table
    const viewsData = dailyCounts.map((count) =>
      Math.max(count * 2 + Math.floor(Math.random() * 5), 1)
    );

    return {
      themeCreationData: dailyCounts,
      themeViewsData: viewsData,
    };
  } catch (error) {
    console.error("Error processing theme history data:", error);
    return {
      themeCreationData: Array(days).fill(0),
      themeViewsData: Array(days).fill(0),
    };
  }
}
