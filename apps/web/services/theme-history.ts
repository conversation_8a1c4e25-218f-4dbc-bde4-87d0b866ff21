"server only";

import { auth } from "@clerk/nextjs/server";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import type { DBTheme } from "@/types/theme";

// Common theme fields to select
const THEME_FIELDS = `
  id,
  request_id,
  name,
  theme_description,
  format,
  colors,
  dark_colors,
  created_at,
  is_favorite,
  is_public,
  theme_requests!inner(user_prompt, include_sidebar, include_chart,border_radius)
`;

// Type for filter options
export interface ThemeFilterOptions {
  query?: string;
  format?: "hsl" | "oklch" | "all";
  hasDarkMode?: boolean;
  hasSidebar?: boolean;
  hasChart?: boolean;
  isFavorite?: boolean;
}

// Type for sort options
export type ThemeSortOption = "newest" | "oldest" | "name-asc" | "name-desc";

/**
 * Fetches paginated and filtered themes for the current user
 * @param page Current page number (1-based)
 * @param pageSize Number of themes per page
 * @param sortOption Sort option for themes
 * @param filterOptions Filter options for themes
 * @returns Object containing themes and pagination info
 */
export async function getFilteredThemes(
  page: number = 1,
  pageSize: number = 9,
  sortOption: ThemeSortOption = "newest",
  filterOptions: ThemeFilterOptions = {}
): Promise<{
  themes: DBTheme[];
  totalThemes: number;
  totalPages: number;
}> {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Authentication required");
  }

  const client = createServerSupabaseClient();
  let query = client
    .from("themes")
    .select(THEME_FIELDS, { count: "exact" })
    .eq("user_id", userId);

  // Apply text search filter if provided
  if (filterOptions.query) {
    query = query.or(
      `name.ilike.%${filterOptions.query}%,theme_description.ilike.%${filterOptions.query}%`
    );
  }

  // Apply format filter if provided and not 'all'
  if (filterOptions.format && filterOptions.format !== "all") {
    query = query.eq("format", filterOptions.format);
  }

  // Apply favorite filter if provided
  if (filterOptions.isFavorite) {
    query = query.eq("is_favorite", true);
  }

  // Apply dark mode filter if provided
  if (filterOptions.hasDarkMode) {
    query = query.not("dark_colors", "is", null);
  }

  // Apply sidebar filter if provided
  if (filterOptions.hasSidebar) {
    query = query.eq("theme_requests.include_sidebar", true);
  }

  // Apply chart filter if provided
  if (filterOptions.hasChart) {
    query = query.eq("theme_requests.include_chart", true);
  }

  // Apply sorting
  switch (sortOption) {
    case "oldest":
      query = query.order("created_at", { ascending: true });
      break;
    case "name-asc":
      query = query.order("name", { ascending: true });
      break;
    case "name-desc":
      query = query.order("name", { ascending: false });
      break;
    case "newest":
    default:
      query = query.order("created_at", { ascending: false });
      break;
  }

  // Apply pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  query = query.range(from, to);

  // Execute the query
  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching filtered themes:", error);
    return { themes: [], totalThemes: 0, totalPages: 0 };
  }

  // Calculate total pages
  const totalThemes = count || 0;
  const totalPages = Math.ceil(totalThemes / pageSize);

  return {
    themes: (data as unknown as DBTheme[]) || [],
    totalThemes,
    totalPages,
  };
}

/**
 * Fetches the total number of pages for themes with the given filters
 * @param pageSize Number of themes per page
 * @param filterOptions Filter options for themes
 * @returns Total number of pages
 */
/**
 * Fetches paginated and filtered favorite themes for the current user
 * @param page Current page number (1-based)
 * @param pageSize Number of themes per page
 * @param sortOption Sort option for themes
 * @param filterOptions Filter options for themes
 * @returns Object containing themes and pagination info
 */
export async function getFavoriteThemes(
  page: number = 1,
  pageSize: number = 9,
  sortOption: ThemeSortOption = "newest",
  filterOptions: ThemeFilterOptions = {}
): Promise<{
  themes: DBTheme[];
  totalThemes: number;
  totalPages: number;
}> {
  // Create a new filter options object with isFavorite set to true
  const favoriteFilterOptions: ThemeFilterOptions = {
    ...filterOptions,
    isFavorite: true,
  };

  // Reuse the getFilteredThemes function with isFavorite set to true
  return getFilteredThemes(page, pageSize, sortOption, favoriteFilterOptions);
}

export async function getThemeTotalPages(
  pageSize: number = 9,
  filterOptions: ThemeFilterOptions = {}
): Promise<number> {
  const { userId } = await auth();
  if (!userId) {
    return 0;
  }

  const client = createServerSupabaseClient();
  let query = client
    .from("themes")
    .select("id", { count: "exact", head: true })
    .eq("user_id", userId);

  // Apply text search filter if provided
  if (filterOptions.query) {
    query = query.or(
      `name.ilike.%${filterOptions.query}%,theme_description.ilike.%${filterOptions.query}%`
    );
  }

  // Apply format filter if provided and not 'all'
  if (filterOptions.format && filterOptions.format !== "all") {
    query = query.eq("format", filterOptions.format);
  }

  // Apply favorite filter if provided
  if (filterOptions.isFavorite) {
    query = query.eq("is_favorite", true);
  }

  // Apply dark mode filter if provided
  if (filterOptions.hasDarkMode) {
    query = query.not("dark_colors", "is", null);
  }

  // Apply sidebar filter if provided
  if (filterOptions.hasSidebar) {
    query = query.eq("theme_requests.include_sidebar", true);
  }

  // Apply chart filter if provided
  if (filterOptions.hasChart) {
    query = query.eq("theme_requests.include_chart", true);
  }

  // Execute the query
  const { count, error } = await query;

  if (error) {
    console.error("Error fetching theme count:", error);
    return 0;
  }

  // Calculate total pages
  return Math.ceil((count || 0) / pageSize);
}
