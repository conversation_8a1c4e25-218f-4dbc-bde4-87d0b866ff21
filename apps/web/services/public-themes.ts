"server only";

import { createClient as supabaseAdminClient } from "@supabase/supabase-js";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import { DBTheme } from "@/types/theme";

// Type for public theme filter options
export interface PublicThemeFilterOptions {
  query?: string;
  format?: "hsl" | "oklch" | "all";
  hasDarkMode?: boolean;
  hasSidebar?: boolean;
  hasChart?: boolean;
  primaryHue?: string;
}

// Type for public theme sort options
export type PublicThemeSortOption =
  | "newest"
  | "oldest"
  | "name-asc"
  | "name-desc";

/**
 * Fetches paginated and filtered public themes
 * @param page Current page number (1-based)
 * @param pageSize Number of themes per page
 * @param sortOption Sort option for themes
 * @param filterOptions Filter options for themes
 * @returns Object containing themes and pagination info
 */
export async function getPublicThemes(
  page: number = 1,
  pageSize: number = 9,
  sortOption: PublicThemeSortOption = "newest",
  filterOptions: PublicThemeFilterOptions = {},
  isAuthenticated: boolean = false
): Promise<{
  themes: DBTheme[];
  totalThemes: number;
  totalPages: number;
  isLimited: boolean;
}> {
  const client = supabaseAdminClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL ?? "",
    process.env.SUPABASE_SERVICE_ROLE_KEY ?? "",
    {
      auth: {
        persistSession: false,
      },
    }
  );

  // Start with base query for public themes
  let query = client
    .from("themes")
    .select(
      `
      id,
      request_id,
      name,
      theme_description,
      format,
      colors,
      dark_colors,
      created_at,
      is_favorite,
      is_public,
      shared_id,
      primary_hue,
      theme_requests!inner(user_prompt, include_sidebar, include_chart, border_radius)
    `,
      { count: "exact" }
    )
    .eq("is_public", true);

  // Apply text search filter if provided
  if (filterOptions.query) {
    query = query.or(
      `name.ilike.%${filterOptions.query}%,theme_description.ilike.%${filterOptions.query}%`
    );
  }

  // Apply format filter if provided and not 'all'
  if (filterOptions.format && filterOptions.format !== "all") {
    query = query.eq("format", filterOptions.format);
  }

  // Apply dark mode filter if provided
  if (filterOptions.hasDarkMode) {
    query = query.not("dark_colors", "is", null);
  }

  // Apply sidebar filter if provided
  if (filterOptions.hasSidebar) {
    query = query.eq("theme_requests.include_sidebar", true);
  }

  // Apply chart filter if provided
  if (filterOptions.hasChart) {
    query = query.eq("theme_requests.include_chart", true);
  }

  // Apply primary hue filter if provided
  if (filterOptions.primaryHue) {
    query = query.eq("primary_hue", filterOptions.primaryHue);
  }

  // Apply sorting
  switch (sortOption) {
    case "oldest":
      query = query.order("created_at", { ascending: true });
      break;
    case "name-asc":
      query = query.order("name", { ascending: true });
      break;
    case "name-desc":
      query = query.order("name", { ascending: false });
      break;
    case "newest":
    default:
      query = query.order("created_at", { ascending: false });
      break;
  }

  // Apply pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  query = query.range(from, to);

  // Execute the query
  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching public themes:", error);
    return { themes: [], totalThemes: 0, totalPages: 0, isLimited: false };
  }

  // Calculate total pages
  const totalThemes = count || 0;
  const totalPages = Math.ceil(totalThemes / pageSize);

  // Limit results for unauthenticated users
  let isLimited = false;
  let limitedData = data;

  if (!isAuthenticated && totalThemes > 18) {
    // If user is not authenticated and there are more than 18 themes (2 pages of 9),
    // limit the results to only show themes from the first 2 pages
    if (page > 2) {
      // If requesting a page beyond the limit, return empty
      limitedData = [];
    }
    isLimited = true;
  }

  return {
    themes: (limitedData as unknown as DBTheme[]) || [],
    totalThemes: isLimited ? Math.min(totalThemes, 18) : totalThemes,
    totalPages: isLimited ? Math.min(totalPages, 2) : totalPages,
    isLimited,
  };
}

/**
 * Fetches the total number of pages for public themes with the given filters
 * @param pageSize Number of themes per page
 * @param filterOptions Filter options for themes
 * @returns Total number of pages
 */
export async function getPublicThemeTotalPages(
  pageSize: number = 9,
  filterOptions: PublicThemeFilterOptions = {}
): Promise<number> {
  const client = createServerSupabaseClient();
  let query = client
    .from("themes")
    .select("id", { count: "exact", head: true })
    .eq("is_public", true);

  // Apply filters
  if (filterOptions.query) {
    query = query.or(
      `name.ilike.%${filterOptions.query}%,theme_description.ilike.%${filterOptions.query}%`
    );
  }

  if (filterOptions.format && filterOptions.format !== "all") {
    query = query.eq("format", filterOptions.format);
  }

  if (filterOptions.hasDarkMode) {
    query = query.not("dark_colors", "is", null);
  }

  if (filterOptions.primaryHue) {
    query = query.eq("primary_hue", filterOptions.primaryHue);
  }

  const { count, error } = await query;

  if (error) {
    console.error("Error getting public theme count:", error);
    return 0;
  }

  return Math.ceil((count || 0) / pageSize);
}

export async function getPublicThemeById(id: string): Promise<DBTheme | null> {
  const client = createServerSupabaseClient();
  const { data: theme } = await client
    .from("themes")
    .select(
      `
      id,
      name,
      theme_description,
      format,
      colors,
      dark_colors,
      created_at,
      analysis,
      primary_hue
    `
    )
    .eq("shared_id", id)
    .eq("is_public", true)
    .single();

  return theme as DBTheme;
}
