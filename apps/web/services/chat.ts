"server only";

import { notFound } from "next/navigation";
import { Message } from "ai";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import { Database } from "@/lib/types/supabase";

// Type definitions
export type ChatSession = Database["public"]["Tables"]["chat_sessions"]["Row"];
export type ChatMessage = Database["public"]["Tables"]["chat_messages"]["Row"];
export type ChatStream = Database["public"]["Tables"]["chat_streams"]["Row"];
export type ChatTheme = Database["public"]["Tables"]["chat_themes"]["Row"];
export type MessageRole = Database["public"]["Enums"]["message_role"];

// Type for chat session with messages
export interface ChatSessionWithMessages extends ChatSession {
  messages: ChatMessage[];
}

// Type for filter options
export interface ChatSessionFilterOptions {
  query?: string;
  isPinned?: boolean;
}

// Type for sort options
export type ChatSessionSortOption =
  | "newest"
  | "oldest"
  | "name-asc"
  | "name-desc";

/**
 * Creates a new chat session
 * @param title Optional title for the chat session (defaults to "New Chat")
 * @returns The ID of the newly created chat session
 */
export async function createChatSession(title?: string): Promise<string> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("create_chat_session", {
    p_title: title || undefined,
  });

  if (error) {
    console.error("Error creating chat session:", error);
    throw new Error(`Failed to create chat session: ${error.message}`);
  }

  return data;
}

/**
 * Saves a chat message to the database
 * @param sessionId The ID of the chat session
 * @param message The message to save
 * @returns The ID of the saved message
 */
export async function saveChatMessage(
  sessionId: string,
  message: Message
): Promise<string> {
  const client = createServerSupabaseClient();

  // Convert the message to the format expected by the database
  const { data, error } = await client.rpc("save_chat_message", {
    p_session_id: sessionId,
    p_message_id: message.id,
    p_role: message.role as MessageRole,
    p_content: message.content || "",
    p_parts: message.parts || null,
    p_tool_invocations: message.toolInvocations || null,
    p_created_at_client: message.createdAt
      ? new Date(message.createdAt).toISOString()
      : null,
  });

  if (error) {
    console.error("Error saving chat message:", error);
    throw new Error(`Failed to save chat message: ${error.message}`);
  }

  return data;
}

/**
 * Updates an existing chat message in the database
 * @param sessionId The ID of the chat session
 * @param message The updated message
 * @returns The ID of the updated message
 */
export async function updateChatMessage(
  sessionId: string,
  message: Message
): Promise<string> {
  const client = createServerSupabaseClient();

  // Update the message in the database
  const { data, error } = await client.rpc("update_chat_message", {
    p_session_id: sessionId,
    p_message_id: message.id,
    p_content: message.content || "",
    p_parts: message.parts || null,
    p_updated_at: new Date().toISOString(),
  });

  if (error) {
    console.error("Error updating chat message:", error);
    throw new Error(`Failed to update chat message: ${error.message}`);
  }

  return data;
}

/**
 * Loads chat messages for a session with pagination
 * @param sessionId The ID of the chat session
 * @param limit Maximum number of messages to return (default: 50)
 * @param offset Number of messages to skip (default: 0)
 * @returns Array of chat messages
 */
export async function loadChatMessages(
  sessionId: string,
  limit: number = 50,
  offset: number = 0
): Promise<ChatMessage[]> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("load_chat_messages", {
    p_session_id: sessionId,
    p_limit: limit,
    p_offset: offset,
  });
  if (!data) {
    return notFound();
  }
  if (error) {
    console.error("Error loading chat messages:", error);
    throw new Error(`Failed to load chat messages: ${error.message}`);
  }

  return data || [];
}

/**
 * Converts database chat messages to AI SDK Message format
 * @param messages Array of chat messages from the database
 * @returns Array of messages in AI SDK format
 */
// Define types for tool invocations and message parts
type ToolInvocation = {
  state: string;
  toolName: string;
  toolCallId: string;
  args: Record<string, unknown>;
  result?: Record<string, unknown>;
  step?: number;
};

type MessagePart = {
  type: string;
  text?: string;
  toolInvocation?: {
    state: string;
    toolName: string;
    toolCallId: string;
    args: Record<string, unknown>;
    result?: Record<string, unknown>;
    step?: number;
  };
};

export function convertToAiMessages(messages: ChatMessage[]): Message[] {
  return messages.map((message) => {
    // Get tool invocations from the database
    const dbToolInvocations =
      (message.tool_invocations as ToolInvocation[]) || [];

    // Process tool invocations to ensure they're in the expected format
    const processedToolInvocations = dbToolInvocations.map(
      (invocation: ToolInvocation) => {
        // If the invocation is in "call" state but we have a result in parts,
        // update it to "result" state with the result from parts
        if (invocation.state === "call") {
          // Look for a matching tool invocation in parts
          const parts = (message.parts as MessagePart[]) || [];
          const matchingPart = parts.find(
            (part: MessagePart) =>
              part.type === "tool-invocation" &&
              part.toolInvocation?.toolCallId === invocation.toolCallId
          );

          // If we found a matching part with a result, use that result
          if (matchingPart?.toolInvocation?.state === "result") {
            return {
              ...invocation,
              state: "result",
              result: matchingPart.toolInvocation.result,
            };
          }
        }

        return invocation;
      }
    );

    return {
      id: message.id,
      role: message.role,
      content: message.content || "",
      createdAt: message.created_at_client
        ? new Date(message.created_at_client)
        : new Date(message.created_at),
      parts: message.parts as unknown as Message["parts"],
      // Using toolInvocations despite deprecation warning for compatibility with existing code
      toolInvocations:
        processedToolInvocations as unknown as Message["toolInvocations"],
    };
  });
}

/**
 * Gets a chat session by ID
 * @param sessionId The ID of the chat session
 * @returns The chat session or null if not found
 */
export async function getChatSession(
  sessionId: string
): Promise<ChatSession | null> {
  const client = createServerSupabaseClient();
  const { data, error } = await client
    .from("chat_sessions")
    .select("*")
    .eq("id", sessionId)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // Record not found
      return null;
    }
    console.error("Error getting chat session:", error);
    throw new Error(`Failed to get chat session: ${error.message}`);
  }

  return data;
}

/**
 * Gets a chat session with its messages
 * @param sessionId The ID of the chat session
 * @param limit Maximum number of messages to return (default: 50)
 * @param offset Number of messages to skip (default: 0)
 * @returns The chat session with messages or null if not found
 */
export async function getChatSessionWithMessages(
  sessionId: string,
  limit: number = 50,
  offset: number = 0
): Promise<ChatSessionWithMessages | null> {
  const session = await getChatSession(sessionId);
  if (!session) {
    return null;
  }

  const messages = await loadChatMessages(sessionId, limit, offset);
  return {
    ...session,
    messages,
  };
}

/**
 * Updates the title of a chat session
 * @param sessionId The ID of the chat session
 * @param title The new title
 */
export async function updateChatTitle(
  sessionId: string,
  title: string
): Promise<void> {
  const client = createServerSupabaseClient();
  const { error } = await client.rpc("update_chat_title", {
    p_session_id: sessionId,
    p_title: title,
  });

  if (error) {
    console.error("Error updating chat title:", error);
    throw new Error(`Failed to update chat title: ${error.message}`);
  }
}

/**
 * Toggles the pinned status of a chat session
 * @param sessionId The ID of the chat session
 * @returns The new pinned status
 */
export async function toggleChatPinned(sessionId: string): Promise<boolean> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("toggle_chat_pinned", {
    p_session_id: sessionId,
  });

  if (error) {
    console.error("Error toggling chat pinned status:", error);
    throw new Error(`Failed to toggle chat pinned status: ${error.message}`);
  }

  return data;
}

/**
 * Deletes a chat session
 * @param sessionId The ID of the chat session
 */
export async function deleteChatSession(sessionId: string): Promise<void> {
  const client = createServerSupabaseClient();
  const { error } = await client.rpc("delete_chat_session", {
    p_session_id: sessionId,
  });

  if (error) {
    console.error("Error deleting chat session:", error);
    throw new Error(`Failed to delete chat session: ${error.message}`);
  }
}

/**
 * Gets paginated and filtered chat sessions for the current user
 * @param page Current page number (1-based)
 * @param pageSize Number of sessions per page
 * @param sortOption Sort option for sessions
 * @param filterOptions Filter options for sessions
 * @returns Object containing sessions and pagination info
 */
export async function getFilteredChatSessions(
  page: number = 1,
  pageSize: number = 10,
  sortOption: ChatSessionSortOption = "newest",
  filterOptions: ChatSessionFilterOptions = {}
): Promise<{
  sessions: ChatSession[];
  totalSessions: number;
  totalPages: number;
}> {
  const client = createServerSupabaseClient();

  // Start with base query
  let query = client.from("chat_sessions").select("*", { count: "exact" });

  // Apply text search filter if provided
  if (filterOptions.query) {
    query = query.ilike("title", `%${filterOptions.query}%`);
  }

  // Apply pinned filter if provided
  if (filterOptions.isPinned !== undefined) {
    query = query.eq("is_pinned", filterOptions.isPinned);
  }

  // Apply sorting
  switch (sortOption) {
    case "newest":
      query = query.order("last_message_at", { ascending: false });
      break;
    case "oldest":
      query = query.order("last_message_at", { ascending: true });
      break;
    case "name-asc":
      query = query.order("title", { ascending: true });
      break;
    case "name-desc":
      query = query.order("title", { ascending: false });
      break;
  }

  // Apply pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  query = query.range(from, to);

  // Execute query
  const { data, error, count } = await query;

  if (error) {
    console.error("Error getting chat sessions:", error);
    throw new Error(`Failed to get chat sessions: ${error.message}`);
  }

  // Calculate total pages
  const totalSessions = count || 0;
  const totalPages = Math.ceil(totalSessions / pageSize);

  return {
    sessions: data || [],
    totalSessions,
    totalPages,
  };
}

/**
 * Gets recent chat sessions for the sidebar, grouped by time periods
 * @param limit Maximum number of chats to return per time period
 * @returns Object containing chats grouped by time periods
 */
export async function getRecentChatSessions(limit: number = 5): Promise<{
  todayChats: ChatSession[];
  yesterdayChats: ChatSession[];
  pastWeekChats: ChatSession[];
}> {
  const client = createServerSupabaseClient();

  // Get all chats from the past week
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const { data, error } = await client
    .from("chat_sessions")
    .select("*")
    .gte("last_message_at", oneWeekAgo.toISOString())
    .order("last_message_at", { ascending: false });

  if (error) {
    console.error("Error getting recent chat sessions:", error);
    throw new Error(`Failed to get recent chat sessions: ${error.message}`);
  }

  // Get date boundaries for today and yesterday
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterdayStart = new Date(todayStart);
  yesterdayStart.setDate(yesterdayStart.getDate() - 1);

  // Sort chats into appropriate time buckets
  const todayChats: ChatSession[] = [];
  const yesterdayChats: ChatSession[] = [];
  const pastWeekChats: ChatSession[] = [];

  (data || []).forEach((chat) => {
    const chatDate = new Date(chat.last_message_at);

    if (chatDate >= todayStart) {
      if (todayChats.length < limit) {
        todayChats.push(chat);
      }
    } else if (chatDate >= yesterdayStart) {
      if (yesterdayChats.length < limit) {
        yesterdayChats.push(chat);
      }
    } else {
      if (pastWeekChats.length < limit) {
        pastWeekChats.push(chat);
      }
    }
  });

  return {
    todayChats,
    yesterdayChats,
    pastWeekChats,
  };
}

/**
 * Creates a new stream record for a chat session
 * @param sessionId The ID of the chat session
 * @param streamId The ID of the stream
 * @returns The ID of the stream record
 */
export async function createChatStream(
  sessionId: string,
  streamId: string
): Promise<string> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("create_chat_stream", {
    p_session_id: sessionId,
    p_stream_id: streamId,
  });

  if (error) {
    console.error("Error creating chat stream:", error);
    throw new Error(`Failed to create chat stream: ${error.message}`);
  }

  return data;
}

/**
 * Marks a stream as completed
 * @param sessionId The ID of the chat session
 * @param streamId The ID of the stream
 */
export async function completeChatStream(
  sessionId: string,
  streamId: string
): Promise<void> {
  const client = createServerSupabaseClient();
  const { error } = await client.rpc("complete_chat_stream", {
    p_session_id: sessionId,
    p_stream_id: streamId,
  });

  if (error) {
    console.error("Error completing chat stream:", error);
    throw new Error(`Failed to complete chat stream: ${error.message}`);
  }
}

/**
 * Gets the latest active stream for a chat session
 * @param sessionId The ID of the chat session
 * @returns The ID of the latest active stream or null if none found
 */
export async function getLatestChatStream(
  sessionId: string
): Promise<string | null> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("get_latest_chat_stream", {
    p_session_id: sessionId,
  });

  if (error) {
    console.error("Error getting latest chat stream:", error);
    throw new Error(`Failed to get latest chat stream: ${error.message}`);
  }

  return data || null;
}

/**
 * Links a chat session to a theme
 * @param sessionId The ID of the chat session
 * @param themeId The ID of the theme
 * @returns The ID of the link
 */
export async function linkChatToTheme(
  sessionId: string,
  themeId: string
): Promise<string> {
  const client = createServerSupabaseClient();
  const { data, error } = await client.rpc("link_chat_to_theme", {
    p_session_id: sessionId,
    p_theme_id: themeId,
  });

  if (error) {
    console.error("Error linking chat to theme:", error);
    throw new Error(`Failed to link chat to theme: ${error.message}`);
  }

  return data;
}

/**
 * Gets themes linked to a chat session
 * @param sessionId The ID of the chat session
 * @returns Array of theme IDs
 */
export async function getLinkedThemes(sessionId: string): Promise<string[]> {
  const client = createServerSupabaseClient();
  const { data, error } = await client
    .from("chat_themes")
    .select("theme_id")
    .eq("session_id", sessionId);

  if (error) {
    console.error("Error getting linked themes:", error);
    throw new Error(`Failed to get linked themes: ${error.message}`);
  }

  return (data || []).map((link) => link.theme_id);
}

/**
 * Loads chat messages for a session
 * @param id The ID of the chat session
 * @returns Array of messages in AI SDK format
 */
export async function loadChat(id: string): Promise<Message[]> {
  try {
    const messages = await loadChatMessages(id);
    return convertToAiMessages(messages);
  } catch (error) {
    console.error("Error loading chat:", error);
    return [];
  }
}

/**
 * Saves chat messages for a session
 * @param params Object containing chat ID and messages
 */
export async function saveChat({
  id,
  messages,
  title,
}: {
  id: string;
  messages: Message[];
  title?: string;
}): Promise<void> {
  console.log("saveChat called!");

  try {
    // Check if the chat session exists
    const session = await getChatSessionWithMessages(id);

    if (!session) {
      // Create a new chat session if it doesn't exist
      await createChatSession(title || "New Chat");
    } else if (title && title !== session.title) {
      // Update the title if it has changed
      await updateChatTitle(id, title);
    }

    // Get existing messages as a map for easy lookup
    const existingMessages =
      session?.messages.reduce((map, msg) => {
        map.set(msg.id, msg);
        return map;
      }, new Map()) || new Map();

    // Process each message
    for (const message of messages) {
      if (existingMessages.has(message.id)) {
        // Message exists - check if it needs updating
        const existingMessage = existingMessages.get(message.id);
        const needsUpdate =
          message.content !== existingMessage.content ||
          JSON.stringify(message.parts) !==
            JSON.stringify(existingMessage.parts);

        if (needsUpdate) {
          // Update the existing message
          await updateChatMessage(id, message);
        }
      } else {
        // New message - save it
        await saveChatMessage(id, message);
      }
    }
  } catch (error) {
    console.error("Error saving chat:", error);
    throw error; // Re-throw to allow proper error handling
  }
}

/**
 * Loads chat streams for a session
 * @param chatId The ID of the chat session
 * @returns Array of stream IDs
 */
export async function loadStreams(chatId: string): Promise<string[]> {
  try {
    const client = createServerSupabaseClient();
    const { data, error } = await client
      .from("chat_streams")
      .select("stream_id")
      .eq("session_id", chatId)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error loading streams:", error);
      return [];
    }

    return (data || []).map((stream) => stream.stream_id);
  } catch (error) {
    console.error("Error loading streams:", error);
    return [];
  }
}

/**
 * Appends a stream ID to a chat session
 * @param params Object containing chat ID and stream ID
 */
export async function appendStreamId({
  chatId,
  streamId,
}: {
  chatId: string;
  streamId: string;
}): Promise<void> {
  try {
    await createChatStream(chatId, streamId);
  } catch (error) {
    console.error("Error appending stream ID:", error);
  }
}

/**
 * Marks a stream as completed
 * @param params Object containing chat ID and stream ID
 */
export async function completeStream({
  chatId,
  streamId,
}: {
  chatId: string;
  streamId: string;
}): Promise<void> {
  try {
    await completeChatStream(chatId, streamId);
  } catch (error) {
    console.error("Error completing stream:", error);
  }
}
