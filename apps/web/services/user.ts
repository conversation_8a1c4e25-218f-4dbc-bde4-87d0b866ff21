"server only";

import { auth } from "@clerk/nextjs/server";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";

export type UserProfile = {
  id: string;
  name: string | null;
  subscription_tier: "free" | "pro" | "enterprise";
  created_at: string;
  updated_at: string;
};

/**
 * Fetches the user profile information for the current user
 * @returns Object containing user profile information
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  const { userId } = await auth();
  if (!userId) return null;

  const client = createServerSupabaseClient();

  const { data, error } = await client
    .from("user_profiles")
    .select("*")
    .eq("id", userId)
    .single();

  if (error) {
    console.error("Error fetching user profile:", error);
    return null;
  }

  return data;
}

/**
 * Get storage limits based on subscription tier
 * @param subscriptionTier The user's subscription tier
 * @returns Object containing storage limits
 */
export function getStorageLimits(subscriptionTier: string): {
  storageLimit: number;
  nextPlan: string;
} {
  switch (subscriptionTier) {
    case "pro":
      return {
        storageLimit: 1000, // 1000 themes for Pro users
        nextPlan: "Enterprise",
      };
    case "enterprise":
      return {
        storageLimit: 10000, // 10000 themes for Enterprise users
        nextPlan: "", // No next plan for Enterprise
      };
    case "free":
    default:
      return {
        storageLimit: 100, // 100 themes for Free users
        nextPlan: "Pro",
      };
  }
}
