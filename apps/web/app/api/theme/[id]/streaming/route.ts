import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { CoreMessage, Message, streamObject } from "ai";

import { claude } from "@/lib/ai/client";
import { createExtendColorsSystemPrompt } from "@/lib/ai/prompt";
import { AITheme, ThemeRequestSchema, ThemeSchema } from "@/schema/theme";
import {
  recordThemeStreamingFailure,
  saveThemeToDatabase,
  setupThemeGenerationTimeout,
} from "@/services/theme";

export const maxDuration = 60;

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  // Authentication check
  const user = await auth();

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate request data
  let validatedData;
  try {
    const body = await req.json();
    const parsed = ThemeRequestSchema.safeParse(body);
    validatedData = parsed;

    if (!validatedData.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validatedData.error.errors,
        },
        { status: 400 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to parse request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 400 }
    );
  }

  const {
    description,
    format,
    includeDarkMode,
    includeSidebar,
    includeChart,
    imageData,

    borderRadius,
  } = validatedData.data;
  const startingTime = Date.now();

  const model = claude;

  // Set up a timeout to automatically fail the request if it takes too long
  // This will be cleared if the generation completes successfully
  const cancelTimeout = setupThemeGenerationTimeout(id, 10 * 60 * 1000); // 10 minute timeout

  try {
    // Create messages array for the model input
    let messages: Array<CoreMessage> | Array<Omit<Message, "id">> = [];
    let prompt = "";
    if (imageData) {
      // Create messages array with image content for image-based theme generation
      messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Extract a color palette from this image and create a cohesive theme based on the dominant color.
            - ${
              includeDarkMode ? "include dark mode" : "do not include dark mode"
            }.
            - ${
              includeSidebar
                ? "Include sidebar colors"
                : "Do not include sidebar colors"
            }
            - ${
              includeChart
                ? "Include chart colors"
                : "Do not include chart colors"
            }
            - ${format === "oklch" ? "Use OKLCH format" : "Use HSL format"}
            - Radius : ${borderRadius}rem
          Ensure the theme is cohesive, accessible, and matches the mood described.`,
            },
            {
              type: "image",
              image: imageData,
            },
          ],
        },
      ];
    } else {
      prompt = `Create a color theme based on user's description: "${description}"
      - ${includeDarkMode ? "include dark mode" : "do not include dark mode"}.
      - ${includeSidebar ? "Include sidebar colors" : "Do not include sidebar colors"}
      - ${includeChart ? "Include chart colors" : "Do not include chart colors"}
      - ${format === "oklch" ? "Use OKLCH format" : "Use HSL format"}
      - Radius : ${borderRadius}rem

      Ensure the theme is cohesive, accessible, and matches the mood described. Be creative with the theme name.`;
    }

    // Common stream object configuration
    const streamConfig = {
      model,
      schema: ThemeSchema,
      system: createExtendColorsSystemPrompt({ format }),

      onFinish: async ({ object }: { object: AITheme | undefined }) => {
        // Type assertion to handle the object type
        const typedObject = object as AITheme | undefined;
        // Cancel the timeout since generation completed
        cancelTimeout();

        const processingTime = Date.now() - startingTime;
        if (typedObject) {
          try {
            // For testing error handling, uncomment the next line

            await saveThemeToDatabase(id, typedObject, processingTime);
          } catch (error) {
            console.error("Failed to save theme:", error);
            // Record the failure if saving fails but generation succeeded
            await recordThemeStreamingFailure(
              id,
              error instanceof Error
                ? error
                : new Error("Failed to save generated theme")
            );

            // Re-throw the error so it's propagated to the client
            throw error;
          }
        }
      },
      onError: async (error: unknown) => {
        // Cancel the timeout since we're handling the error explicitly
        cancelTimeout();

        console.error("Streaming Error:", error);
        await recordThemeStreamingFailure(id, error);
      },
    };
    // Add the messages or prompt based on the input type
    const streamOptions = imageData
      ? { ...streamConfig, messages }
      : { ...streamConfig, prompt };
    const result = streamObject(streamOptions);
    return result.toTextStreamResponse();
  } catch (error) {
    // Cancel the timeout since we're handling the error explicitly
    cancelTimeout();

    // Record the failure in the database
    await recordThemeStreamingFailure(id, error);

    console.error("API Error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
