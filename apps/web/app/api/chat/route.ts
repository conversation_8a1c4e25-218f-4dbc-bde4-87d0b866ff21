import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import {
  appendClientMessage,
  appendResponseMessages,
  createDataStreamResponse,
  createIdGenerator,
  generateId,
  generateObject,
  streamText,
  type CoreMessage,
  type Message,
} from "ai";
import { z } from "zod";

import { CHAT_MAX_STEPS, CHAT_MAX_TOKENS } from "@/constants/chat";
import { claude } from "@/lib/ai/client";
import { createThemeAssistantPromptWithCache } from "@/lib/ai/prompt";
import { tools } from "@/lib/ai/tools";
import {
  processToolResults,
  saveErrorState,
  ToolProcessingError,
} from "@/lib/ai/tools/utils";
import {
  appendStreamId,
  completeStream,
  loadChat,
  saveChat,
} from "@/services/chat";

// Helper function to prepare messages with cache control
function prepareMessagesWithCacheControl(
  userMessages: Message[]
): CoreMessage[] {
  // Get the system messages with cache control
  const systemMessages = createThemeAssistantPromptWithCache();

  // Convert user messages to CoreMessage format
  const coreUserMessages = userMessages.map((msg) => {
    // For the first user message, add cache control
    if (msg.role === "user" && userMessages.indexOf(msg) === 0) {
      return {
        role: "user",
        content: msg.content || "",
        ...(msg.parts ? { parts: msg.parts } : {}),
        providerOptions: {
          anthropic: { cacheControl: { type: "ephemeral" } },
        },
      };
    }

    // For other messages, keep as is
    return {
      role: msg.role as "user" | "assistant" | "system",
      content: msg.content || "",
      ...(msg.parts ? { parts: msg.parts } : {}),
    };
  }) as CoreMessage[];

  // Create a new array with system messages followed by user messages
  return [...(systemMessages as CoreMessage[]), ...coreUserMessages];
}

// Allow streaming responses up to 30 seconds
export const maxDuration = 60;

/**
 * Handles API errors and saves error state to the database
 * @param req The request object
 * @param error The error that occurred
 */
async function handleApiError(req: Request, error: unknown): Promise<void> {
  // Try to extract the chat ID from the request if possible
  let chatId: string | undefined;
  try {
    const requestData = await req.clone().json();
    chatId = requestData.id;
  } catch (parseError) {
    console.error("Could not parse request to get chat ID:", parseError);
  }

  // Save the error state to the database if we have a chat ID
  if (chatId) {
    try {
      // Try to load existing messages
      const existingMessages = await loadChat(chatId);

      // Save the error state
      await saveErrorState(
        chatId,
        existingMessages,
        error,
        "API error",
        "Chat with API Error"
      );
    } catch (loadError) {
      console.error("Error handling API error:", loadError);
    }
  }
}

export async function POST(req: Request) {
  // Authentication check
  // const { userId } = await auth();

  // if (!userId) {
  //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  // }
  try {
    const { message, id }: { message: Message; id: string } = await req.json();

    // Load the previous messages from the server
    const previousMessages = await loadChat(id);

    // append the new message to the previous messages:
    const messages = appendClientMessage({
      messages: previousMessages,
      message,
    });
    // Generate a stream ID for tracking and resuming
    const streamId = generateId();
    await appendStreamId({ chatId: id, streamId });

    // Create a data stream response
    const response = createDataStreamResponse({
      execute: async (dataStream) => {
        // Track errors that occur during processing
        const errors: ToolProcessingError[] = [];

        // Process any tool results in the messages
        const processedMessages = await processToolResults(
          {
            messages,
            dataStream,
            tools,
          },
          {
            // Form handlers
            forms: {
              askToFillForm: async (formData) => {
                // Return a structured response with the form data
                return {
                  success: true,
                  message: "Form submitted successfully",
                  data: formData,
                  // Create a user-friendly summary of the submitted data
                  // @ts-expect-error This is a valid form data object
                  summary: `Theme configuration: ${formData.description || ""}, Format: ${formData.format || "hsl"}, Dark Mode: ${formData.includeDarkMode ? "Yes" : "No"}, Border Radius: ${formData.borderRadius || "0.5"}rem`,
                };
              },
            },
            // Confirmation handlers
            confirmations: {
              // Handle confirmBaseColors tool
              confirmBaseColors: async (args, confirmed) => {
                // Extract baseColors and themeConfig from args if available
                const baseColors = args.baseColors || {};
                const themeConfig = args.themeConfig || {};

                if (confirmed) {
                  console.log("Base colors approved:", baseColors);

                  // Extract the base colors for the summary
                  const primaryColor = baseColors.primary || "primary color";
                  const backgroundColor =
                    baseColors.background || "background color";

                  // Return the approval with all the necessary data
                  return {
                    status: "approved",
                    message:
                      "User approved the base colors. You can now proceed to extend them into a full theme.",
                    baseColors: baseColors,
                    themeConfig: themeConfig,
                    approved: true,
                    // Add a summary of the approved colors for the AI
                    summary: `User approved the base colors with primary: ${primaryColor} and background: ${backgroundColor}. You can now proceed to extend these into a full theme.`,
                  };
                } else {
                  return {
                    status: "user_declined",
                    message:
                      "The user wants to refine the base colors. Please ask for more specific feedback.",
                  };
                }
              },
            },
            // Handle tool processing errors
            onToolError: async (error) => {
              console.error(`Tool error in ${error.toolName}:`, error.error);
              errors.push(error);

              // Save the error state immediately to ensure it's not lost
              await saveErrorState(
                id,
                messages,
                error.error,
                `Tool error in ${error.toolName}: ${error.errorType}`,
                "Chat with Tool Error"
              );

              // If this is a critical tool error, mark the stream as completed
              if (
                error.errorType === "base_colors_generation_failed" ||
                error.errorType === "theme_extension_failed"
              ) {
                await completeStream({ chatId: id, streamId });
              }
            },
          }
        );
        // Use the cached system prompt with Anthropic's cache control
        const result = streamText({
          model: claude,
          maxSteps: CHAT_MAX_STEPS,
          messages: prepareMessagesWithCacheControl(processedMessages),
          toolCallStreaming: true,
          tools: tools,
          experimental_generateMessageId: createIdGenerator({
            prefix: "msgs",
            size: 16,
          }),
          maxTokens: CHAT_MAX_TOKENS,
          onError: async (error) => {
            console.error("Error in theme generation:", error);

            // Save the error state to the database
            await saveErrorState(
              id,
              messages,
              error,
              "Error in theme generation",
              "Chat with Generation Error"
            );

            // Mark the stream as completed
            await completeStream({ chatId: id, streamId });
          },
          async onFinish({
            response,
            toolCalls,
            toolResults,
            providerMetadata,
            usage,
          }) {
            // Log cache metrics to verify caching is working
            const cacheMetrics = providerMetadata?.anthropic;
            console.log("\n==== CACHE METRICS ====");
            console.log("Raw cache metrics:", cacheMetrics);
            console.log("Provider metadata:", providerMetadata);
            console.log("Model used: Claude 3.7 Sonnet");
            console.log("Usage:", usage);
            // Log detailed cache information if available
            if (cacheMetrics) {
              const { cacheCreationInputTokens, cacheReadInputTokens } =
                cacheMetrics;
              console.log(
                `Cache creation tokens: ${cacheCreationInputTokens || 0}`
              );
              console.log(`Cache read tokens: ${cacheReadInputTokens || 0}`);
              const readTokens = Number(cacheReadInputTokens) || 0;
              console.log(`Cache hit: ${readTokens > 0 ? "Yes" : "No"}`);

              // Calculate token savings if cache was hit
              if (readTokens > 0) {
                const creationTokens = Number(cacheCreationInputTokens) || 0;
                const tokenSavings = creationTokens - readTokens;
                const savingsPercentage = (tokenSavings / creationTokens) * 100;
                console.log(
                  `Token savings: ${tokenSavings} (${savingsPercentage.toFixed(2)}%)`
                );
              }
            } else {
              console.log("No cache metrics available. Possible reasons:");
              console.log("1. Cache control not properly configured");
              console.log(
                "2. System message too short (< 1024 tokens for Claude 3.7 Sonnet)"
              );
              console.log(
                "3. First time running this prompt (cache being created)"
              );
            }
            console.log("==== END CACHE METRICS ====");
            console.log("toolCalls", toolCalls);
            console.log("toolResults", toolResults);
            const updatedMessages = appendResponseMessages({
              messages,
              responseMessages: response.messages,
            });

            // Save the processed messages immediately to persist tool results
            await saveChat({
              id,
              messages: updatedMessages,
              title: "New Chat",
            });

            // Generate a title for new chats
            let title: string | undefined;
            if (previousMessages.length === 0 && updatedMessages.length >= 2) {
              try {
                // Extract content from the first few messages
                const messagesToAnalyze = updatedMessages.slice(
                  0,
                  Math.min(3, updatedMessages.length)
                );
                const messageContent = messagesToAnalyze
                  .map((msg) => msg.content || "")
                  .join("\n");
                const { object: titleData } = await generateObject({
                  model: claude,
                  schema: z.object({
                    title: z
                      .string()
                      .describe("A concise, descriptive title for the chat"),
                  }),
                  messages: [
                    {
                      role: "system",
                      content:
                        "You are a helpful assistant that creates concise, descriptive titles for chat conversations.",
                      providerOptions: {
                        anthropic: { cacheControl: { type: "ephemeral" } },
                      },
                    },
                    {
                      role: "user",
                      content: `Create a short, descriptive title (maximum 50 characters) for a chat conversation based on these messages:\n${messageContent}\n\nThe title should capture the main topic or purpose of the conversation.`,
                    },
                  ],
                });
                title = titleData.title;
              } catch (error) {
                console.error("Error generating chat title:", error);
                title = "New Chat";
              }
            }
            dataStream.writeData({ chatTitle: title || "New Chat" });
            // Save the chat to the database
            await saveChat({
              id,
              messages: updatedMessages,
              title: title || "New Chat", // This will be undefined if not the first message
            });
          },
        });

        // Consume the stream to ensure it runs to completion & triggers onFinish
        // even when the client response is aborted
        result.consumeStream().catch(async (error) => {
          console.error("Error in stream:", error);

          // Save the error state to the database
          await saveErrorState(
            id,
            messages,
            error,
            "Stream error",
            "Chat with Stream Error"
          );

          // Mark the stream as completed
          await completeStream({ chatId: id, streamId });
        });

        result.mergeIntoDataStream(dataStream);
      },
      onError: (error) => {
        console.error("Error in theme generation:", error);

        // Save the error state to the database
        (async () => {
          await saveErrorState(
            id,
            messages,
            error,
            "Data stream error",
            "Chat with Data Stream Error"
          );

          // Mark the stream as completed
          await completeStream({ chatId: id, streamId });
        })();

        return "error";
      },
    });

    return response;
  } catch (error) {
    console.error("Error in POST handler:", error);

    // Handle API errors and save error state
    await handleApiError(req, error);

    return new Response(JSON.stringify({ error: "An error occurred" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
