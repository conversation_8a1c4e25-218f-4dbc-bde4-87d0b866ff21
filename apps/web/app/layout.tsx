import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";

import "@chromify/ui/globals.css";

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";

import { Toaster } from "@chromify/ui/components/sonner";
import { ThemeProvider } from "@/components/theme-provider";

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
});

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
});

export const metadata: Metadata = {
  metadataBase: new URL("https://chromify.cc"),
  title: {
    template: "%s | Chromify",
    default: "Chromify - AI-Powered Color Themes for Modern Web Development",
  },
  description:
    "Transform your web projects with stunning, accessible color schemes generated by AI. Chromify combines expert color theory with artificial intelligence to create perfect color palettes for any project.",
  keywords: [
    "color theme",
    "AI",
    "theme generator",
    "web development",
    "shadcn",
    "vibe coding",
  ],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON> Xi<PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://chromify.cc",
    siteName: "Chromify",
    title: "Chromify - AI-Powered Color Themes for Modern Web Development",
    description:
      "Transform your web projects with stunning, accessible color schemes generated by AI. Chromify combines expert color theory with artificial intelligence to create perfect color palettes for any project.",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Chromify - AI-Powered Color Themes for Modern Web Development",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Chromify - AI-Powered Color Themes for Modern Web Development",
    description:
      "Transform your web projects with stunning, accessible color schemes generated by AI. Chromify combines expert color theory with artificial intelligence to create perfect color palettes for any project.",
    images: ["/og-image.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased `}
        >
          <ThemeProvider>{children}</ThemeProvider>
          <Toaster richColors />
        </body>
      </html>
    </ClerkProvider>
  );
}
