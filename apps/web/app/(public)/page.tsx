import type { Metadata } from "next";

import { FeaturesSection } from "@/components/landing/features-section";
import { FinalCTA } from "@/components/landing/final-cta";
import { FormatShowcase } from "@/components/landing/format-showcase";
import { <PERSON> } from "@/components/landing/hero";
import { HowItWorks } from "@/components/landing/how-it-works";
import { PricingSection } from "@/components/landing/pricing-section";
import { ShowcaseSection } from "@/components/landing/showcase-section";
import { TestimonialsSection } from "@/components/landing/testimonials";
import { VibeCodingSection } from "@/components/landing/vibe-coding-section";

export const metadata: Metadata = {
  title: "Chromify - AI-Powered Color Themes for Modern Web Development",
  description:
    "Create stunning, accessible color schemes instantly with AI. Chromify combines expert color theory with artificial intelligence to generate perfect color palettes for your web projects.",
  openGraph: {
    images: [
      {
        url: "/og-home.jpg",
        width: 1200,
        height: 630,
        alt: "Chromify - AI-Powered Color Themes for Modern Web Development",
      },
    ],
  },
};

export default function Page() {
  return (
    <>
      <Hero />
      <FeaturesSection />
      <HowItWorks />
      <ShowcaseSection />
      <FormatShowcase />
      <VibeCodingSection />
      <TestimonialsSection />
      <PricingSection />
      <FinalCTA />
    </>
  );
}
