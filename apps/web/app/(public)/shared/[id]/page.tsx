import type { Metada<PERSON> } from "next";
import { notFound } from "next/navigation";

import { createServerSupabaseClient } from "@/lib/db/ssr-client";
import { getPublicThemeById } from "@/services/public-themes";
import type { DBTheme } from "@/types/theme";
import SharedBreadcrumb from "./_components/breabcrumb";
import ThemeDisplay from "./_components/theme-display";

type Params = Promise<{
  id: string;
}>;

export async function generateMetadata({
  params,
}: {
  params: Params;
}): Promise<Metadata> {
  const { id } = await params;
  const client = createServerSupabaseClient();

  // Fetch the theme using shared_id, only if it's public
  const { data: theme, error } = await client
    .from("themes")
    .select(`name, theme_description`)
    .eq("shared_id", id)
    .eq("is_public", true)
    .single();

  if (error || !theme) {
    return {
      title: "Theme Not Found",
      description:
        "The shared theme you're looking for doesn't exist or is no longer public.",
    };
  }

  return {
    title: `${theme.name} - Shared Color Theme | Chromify`,
    description:
      theme.theme_description ||
      "A beautiful color theme created with Chromify's AI-powered color generator.",
    openGraph: {
      title: `${theme.name} - Color Theme`,
      description:
        theme.theme_description ||
        "A beautiful color theme created with Chromify's AI-powered color generator.",
    },
  };
}

export default async function SharedThemePage({ params }: { params: Params }) {
  const { id } = await params;
  const theme = await getPublicThemeById(id);
  if (!theme) {
    return notFound();
  }
  return (
    <div className="w-full mx-auto container">
      <SharedBreadcrumb />
      <ThemeDisplay theme={theme as DBTheme} />
    </div>
  );
}
