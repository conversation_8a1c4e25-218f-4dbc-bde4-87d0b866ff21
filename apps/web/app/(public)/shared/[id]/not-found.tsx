import Link from "next/link";
import { But<PERSON> } from "@chromify/ui/components/button";

export default function NotFound() {
  return (
    <div className="container mx-auto py-16 text-center">
      <h1 className="text-4xl font-bold mb-4">Theme Not Found</h1>
      <p className="text-muted-foreground mb-8 max-w-md mx-auto">
        The shared theme you're looking for doesn't exist or is no longer public.
      </p>
      <Link href="/" passHref>
        <Button>Return Home</Button>
      </Link>
    </div>
  );
}
