import Link from "next/link";

import {
  B<PERSON><PERSON><PERSON>b,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@chromify/ui/components/breadcrumb";

function SharedBreadcrumb() {
  return (
    <div className="flex items-center justify-between -mt-4 h-12">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/market">Market</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />

          <BreadcrumbItem>
            <BreadcrumbPage>Shared Theme</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}

export default SharedBreadcrumb;
