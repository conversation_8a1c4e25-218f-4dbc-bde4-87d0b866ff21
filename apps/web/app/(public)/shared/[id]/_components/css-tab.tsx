"use client";

import Link from "next/link";
import { useAuth } from "@clerk/nextjs";
import { Check, Copy, LockI<PERSON> } from "lucide-react";
import { toast } from "sonner";
import { highlight } from "sugar-high";

import { But<PERSON> } from "@chromify/ui/components/button";
import { GradientButton } from "@chromify/ui/components/gradient-button";
import { useCopyToClipboard } from "@/hooks/use-copy-to-clipboard";

interface CssTabProps {
  css: string;
}

function CssTab({ css }: CssTabProps) {
  const { copyToClipboard, isCopied } = useCopyToClipboard();

  const { isLoaded, isSignedIn } = useAuth();
  if (!isLoaded) return null;
  return (
    <div className="mb-6">
      <div className="relative group">
        {!isSignedIn && (
          <>
            {/* Gradient overlay at the bottom */}
            <div className="absolute bottom-0 left-0 right-0 h-3/5 bg-gradient-to-t from-background via-background/95 to-transparent z-10 pointer-events-none rounded-b-md" />

            {/* Blur overlay with CTA */}
            <div className="absolute inset-0 z-20 flex flex-col items-center justify-center bg-background/40 backdrop-blur-sm rounded-md">
              <div className="bg-card p-6 rounded-lg border border-border shadow-lg max-w-md w-full text-center">
                <div className="bg-primary/10 p-3 rounded-full inline-flex mx-auto mb-4">
                  <LockIcon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-medium mb-2">
                  Unlock Complete CSS Access
                </h3>
                <p className="text-sm text-muted-foreground mb-6">
                  Sign up for a free account to view and copy the full CSS
                  variables for this theme
                </p>

                <GradientButton variant="glow" asChild>
                  <Link href="/sign-up">Sign up free</Link>
                </GradientButton>
              </div>
            </div>
          </>
        )}
        <pre className="bg-card shadow p-4 rounded-md text-sm overflow-auto h-[520]">
          <code
            className="space-y-1"
            dangerouslySetInnerHTML={{ __html: highlight(css) }}
          />
        </pre>

        {isSignedIn ? (
          // Copy button for authenticated users
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={() => {
              copyToClipboard(css);
              toast.success("Copied to clipboard");
            }}
            className="absolute opacity-0 group-hover:opacity-100 transition-opacity right-2 top-2"
          >
            {isCopied ? (
              <Check className="h-4 w-4" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>
        ) : (
          // Disabled button with lock icon for unauthenticated users
          <Button
            variant={"ghost"}
            size={"icon"}
            disabled
            className="absolute opacity-0 group-hover:opacity-100 transition-opacity right-2 top-2"
          >
            <LockIcon className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

export default CssTab;
