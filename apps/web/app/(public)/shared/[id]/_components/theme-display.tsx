import { Square } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@chromify/ui/components/tabs";
import ColorGrid from "@/app/(private)/theme/[id]/_components/display/palette-tab/color-grid";
import TailwindIcon from "@/components/icons/tailwind";
import ReportTab from "@/components/report/report-tab";
import ApplyThemeButtons from "@/components/theme/apply-theme-buttons";
import SwatchPreview from "@/components/theme/swatch-preview";
import ThemeVariantIcons from "@/components/theme/theme-variant-icons";
import { generateThemeCss } from "@/lib/utils";
import type { DBTheme } from "@/types/theme";
import CssTab from "./css-tab";

interface AuthAwareThemeDisplayProps {
  theme: DBTheme;
}

function ThemeDisplay({ theme }: AuthAwareThemeDisplayProps) {
  const hasDarkMode = !!theme.dark_colors;

  return (
    <div>
      {/* main heading */}
      <div className="flex flex-col gap-4 md:flex-row md:justify-between md:mb-6">
        <h1 className="text-2xl font-bold lg:text-4xl">{theme.name}</h1>
        <div className="hidden md:block">
          <ApplyThemeButtons theme={theme} />
        </div>
      </div>
      {/* theme config options */}
      <div className="flex flex-col gap-4 md:gap-6 my-4">
        <div className="flex justify-between items-center">
          <div className="flex gap-2 w-full items-center md:gap-4">
            <SwatchPreview
              themeColors={{
                colors: theme.colors,
                dark_colors: theme.dark_colors,
              }}
            />
            <div className="flex gap-2">
              <Badge variant="outline" className="px-3 py-1">
                <TailwindIcon /> {theme.format === "oklch" ? "v4" : "v3"}
              </Badge>
              <Badge variant="outline" className="px-3 py-1">
                {/* @ts-expect-error  radius is always present */}
                <Square /> {theme.colors.radius}
              </Badge>
            </div>
          </div>
          <div className="md:hidden">
            <ApplyThemeButtons theme={theme} />
          </div>
        </div>

        <ThemeVariantIcons
          includeDarkMode={theme.dark_colors !== null}
          includeCharts={theme.colors["chart-1"] !== null}
          includeSidebar={theme.colors.sidebar !== null}
        />
      </div>

      <p className="max-w-3xl my-4 md:my-6">{theme.theme_description}</p>
      {/* theme preview, report, and code tabs */}
      <Tabs defaultValue="colors" className="w-full">
        <TabsList>
          <TabsTrigger className="w-20 md:w-32" value="colors">
            Colors
          </TabsTrigger>
          {hasDarkMode && (
            <TabsTrigger className="w-26 md:w-32" value="dark-colors">
              Dark Colors
            </TabsTrigger>
          )}

          <TabsTrigger className="w-22 md:w-32" value="report">
            Report
          </TabsTrigger>
          <TabsTrigger className="w-20 md:w-32" value="code">
            Code
          </TabsTrigger>
        </TabsList>

        <TabsContent value="colors" className="space-y-4">
          <ColorGrid colors={theme.colors} format={theme.format} />
        </TabsContent>
        {hasDarkMode && (
          <TabsContent value="dark-colors" className="space-y-4">
            <ColorGrid
              colors={theme.dark_colors ?? theme.colors}
              format={theme.format}
            />
          </TabsContent>
        )}

        <TabsContent value="report" className="space-y-4">
          <ReportTab reportPoints={theme.analysis ?? []} />
        </TabsContent>

        <TabsContent value="code" className="space-y-4">
          <CssTab
            css={generateThemeCss({
              format: theme.format,
              colors: theme.colors,
              dark_colors: theme.dark_colors,
            })}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ThemeDisplay;
