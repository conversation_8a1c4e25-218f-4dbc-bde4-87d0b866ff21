import { Suspense } from "react";
import type { Metadata } from "next";

import TitleMeta from "@/components/title-meta";
import SearchFilter from "./_components/search-filter";
import ThemeCardsSkeleton from "./_components/skeleton";
import SortOptions from "./_components/sort-options";
import ThemeList from "./_components/theme-list";

type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export const metadata: Metadata = {
  title: "Theme Marketplace - Explore Community Color Themes",
  description:
    "Browse and discover beautiful color themes created by the Chromify community. Find inspiration for your next web development project.",
  openGraph: {
    images: [
      {
        url: "/og-marketplace.jpg",
        width: 1200,
        height: 630,
        alt: "Chromify Theme Marketplace",
      },
    ],
  },
};

async function Page(props: { searchParams: SearchParams }) {
  const searchParams = await props.searchParams;
  const suspenseKey = `${searchParams.query || ""}-${searchParams.page || "1"}-${searchParams.sort || "newest"}-${JSON.stringify(
    {
      format: searchParams.format,
      darkMode: searchParams.darkMode,
      sidebar: searchParams.sidebar,
      chart: searchParams.chart,
    }
  )}`;

  return (
    <div className="mx-auto container">
      <TitleMeta
        title="Theme Marketplace"
        description="Discover and explore public themes created by the community"
      />

      {/* Search and filter section */}
      <div className="mb-10">
        <SearchFilter />
      </div>

      {/* Sort options */}
      <div className="flex justify-end mb-6">
        <SortOptions />
      </div>

      {/* Theme cards with suspense - key changes with filters to trigger re-render */}
      <Suspense key={suspenseKey} fallback={<ThemeCardsSkeleton />}>
        <ThemeList searchParams={searchParams} />
      </Suspense>
    </div>
  );
}

export default Page;
