import Link from "next/link";
import { auth } from "@clerk/nextjs/server";
import { Share2 } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import {
  getPublicThemes,
  PublicThemeFilterOptions,
  PublicThemeSortOption,
} from "@/services/public-themes";
import MarketThemeCard from "./market-theme-card";
import Pagination from "./pagination";
import SignUpCTA from "./sign-up-cta";

type SearchParams = { [key: string]: string | string[] | undefined };

async function ThemeList(props: { searchParams: SearchParams }) {
  const searchParams = props.searchParams;
  const query = (searchParams?.query as string) || "";
  const currentPage = Number(searchParams?.page) || 1;
  const sortOption = (searchParams?.sort || "newest") as PublicThemeSortOption;

  // Check if user is authenticated
  const { userId } = await auth();
  const isAuthenticated = !!userId;

  // Parse filter options
  const filterOptions: PublicThemeFilterOptions = {
    query,
    format: searchParams?.format as "hsl" | "oklch" | "all" | undefined,
    hasDarkMode: searchParams?.darkMode === "true",
    hasSidebar: searchParams?.sidebar === "true",
    hasChart: searchParams?.chart === "true",
    primaryHue: searchParams?.primaryHue as string | undefined,
  };

  // This await will trigger the Suspense boundary
  const { themes, totalPages, isLimited } = await getPublicThemes(
    currentPage,
    9,
    sortOption,
    filterOptions,
    isAuthenticated
  );

  if (themes.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="p-8 text-center">
          <Share2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-medium mb-2">No themes found</h3>
          <p className="text-muted-foreground mb-6">
            {query
              ? "Try adjusting your search or filters."
              : "There are no public themes available yet."}
          </p>
          <Button asChild variant="outline">
            <Link href="/theme/create">Create a Theme</Link>
          </Button>
        </div>
      </div>
    );
  }

  // If a specific primary hue is selected, don't group by hue
  if (filterOptions.primaryHue) {
    return (
      <div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {themes.map((theme) => (
            <MarketThemeCard theme={theme} key={theme.id} />
          ))}
        </div>
        <div className="mt-10">
          <Pagination totalPages={totalPages} />
        </div>
      </div>
    );
  }

  // Group themes by primary hue when no specific hue is selected
  // const themesByHue = themes.reduce<Record<string, typeof themes>>(
  //   (acc, theme) => {
  //     const hue = theme.primary_hue || "other";
  //     if (!acc[hue]) {
  //       acc[hue] = [];
  //     }
  //     acc[hue].push(theme);
  //     return acc;
  //   },
  //   {}
  // );

  // Get all hues with themes
  //const hues = Object.keys(themesByHue).sort();

  return (
    <div>
      {/* {hues.length > 0 ? (
        <div className="space-y-10">
          {hues.map((hue) => (
            <div key={hue} className="space-y-4">
              <h2 className="text-xl font-semibold capitalize">
                {hue === "other" ? "Uncategorized" : hue}
              </h2>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {themesByHue[hue]?.map((theme) => (
                  <MarketThemeCard theme={theme} key={theme.id} />
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {themes.map((theme) => (
            <MarketThemeCard theme={theme} key={theme.id} />
          ))}
        </div>
      )} */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {themes.map((theme) => (
          <MarketThemeCard theme={theme} key={theme.id} />
        ))}
      </div>
      <div className="mt-10">
        <Pagination totalPages={totalPages} />
      </div>

      {/* Show sign-up CTA for unauthenticated users if results are limited */}
      {isLimited && !isAuthenticated && (
        <div className="mt-16">
          <SignUpCTA />
        </div>
      )}
    </div>
  );
}

export default ThemeList;
