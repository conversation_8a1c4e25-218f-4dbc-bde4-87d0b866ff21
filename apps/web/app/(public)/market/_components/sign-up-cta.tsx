"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { GradientButton } from "@chromify/ui/components/gradient-button";

export default function SignUpCTA() {
  return (
    <div className="w-full py-12 px-4 sm:px-6 lg:px-8 rounded-xl bg-gradient-to-br from-background via-background to-primary/10 border border-border">
      <div className="max-w-3xl mx-auto text-center">
        <div className="inline-flex items-center justify-center p-2 bg-primary/10 rounded-full mb-4">
          <LockIcon className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold tracking-tight sm:text-3xl mb-4">
          Unlock the full marketplace
        </h2>
        <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
          Sign up for free to access all themes in the marketplace, save your
          favorites, and create your own custom themes with our AI-powered
          generator.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <GradientButton asChild size="lg" variant="glow">
            <Link href="/sign-up">
              <Zap className="mr-2 h-4 w-4" />
              Sign up for free
            </Link>
          </GradientButton>
          <Button asChild variant="secondary" size="lg">
            <Link href="/sign-in">
              <Sparkles className="mr-2 h-4 w-4" />
              Sign in
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
