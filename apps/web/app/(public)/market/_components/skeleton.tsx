import { Skeleton } from "@chromify/ui/components/skeleton";

function ThemeCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array(9)
        .fill(null)
        .map((_, index) => (
          <div key={index} className="h-64 space-y-4 border p-4 rounded-2xl">
            <div className="flex justify-between">
              <Skeleton className="w-4/6 h-6" />
              <Skeleton className="w-6 h-6" />
            </div>

            <Skeleton className="w-28 h-6" />
            <div>
              <Skeleton className="w-full h-16" />
            </div>
            <Skeleton className="w-1/3 h-6" />
          </div>
        ))}
    </div>
  );
}
export default ThemeCardsSkeleton;
