"use client";

import { useEffect, useState, useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { FilterIcon, SearchIcon, X } from "lucide-react";
import { useDebouncedCallback } from "use-debounce";

import { Button } from "@chromify/ui/components/button";
import { Input } from "@chromify/ui/components/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@chromify/ui/components/sheet";
import { cn } from "@chromify/ui/lib/utils";
import FilterOptions from "./filter-options";

export default function SearchFilter() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [isPending, startTransition] = useTransition();
  const [open, setOpen] = useState(false);

  // Local state for search input
  const [searchValue, setSearchValue] = useState(
    searchParams.get("query")?.toString() || ""
  );

  // Get initial values from URL params
  const [mounted, setMounted] = useState(false);

  // Handle search input with debounce
  const handleSearch = useDebouncedCallback((term: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on new search

      if (term) {
        params.set("query", term);
      } else {
        params.delete("query");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  }, 500);

  // Handle clearing search input with debounce
  const handleClearSearch = useDebouncedCallback(() => {
    setSearchValue("");
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.delete("query");
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    });
  }, 300);

  // Check if any filters are active
  const hasActiveFilters =
    searchParams.has("format") ||
    searchParams.has("darkMode") ||
    searchParams.has("sidebar") ||
    searchParams.has("chart") ||
    searchParams.has("primaryHue");

  // Set mounted state to handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render client-side to avoid hydration mismatch
  if (!mounted) return null;

  return (
    <div className="flex flex-col gap-8 w-full">
      <div className="flex gap-2 items-center">
        {/* Search input with clear button */}
        <div className="relative flex-1">
          <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            disabled={isPending}
            placeholder="Search themes..."
            className={cn("pl-10", searchValue && "pr-8")}
            value={searchValue}
            onChange={(e) => {
              const value = e.target.value;
              setSearchValue(value);
              handleSearch(value);
            }}
          />
          {searchValue && (
            <Button
              disabled={isPending}
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={handleClearSearch}
              type="button"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Filter drawer trigger - only visible on mobile */}
        <div className="md:hidden">
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "gap-2",
                  hasActiveFilters &&
                    "bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
                )}
              >
                <FilterIcon className="h-4 w-4" />
                Filters
                {hasActiveFilters && (
                  <span className="ml-1 rounded-full bg-primary-foreground text-primary w-5 h-5 flex items-center justify-center text-xs font-medium">
                    {[
                      searchParams.has("format") ? 1 : 0,
                      searchParams.has("darkMode") ? 1 : 0,
                      searchParams.has("sidebar") ? 1 : 0,
                      searchParams.has("chart") ? 1 : 0,
                      searchParams.has("primaryHue") ? 1 : 0,
                    ].reduce((a, b) => a + b, 0)}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent className="w-[300px] sm:w-[400px] p-2">
              <SheetHeader>
                <SheetTitle>Filter Themes</SheetTitle>
                <SheetDescription>
                  Apply filters to find the perfect theme for your project.
                </SheetDescription>
              </SheetHeader>
              <div className="py-6">
                <FilterOptions />
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Filter options - visible on desktop */}
      <div className="hidden md:block">
        <FilterOptions />
      </div>
    </div>
  );
}
