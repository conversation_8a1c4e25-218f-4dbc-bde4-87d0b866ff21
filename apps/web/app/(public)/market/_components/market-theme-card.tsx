import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, PanelLeftIcon } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import {
  <PERSON>,
  CardContent,
  <PERSON>Footer,
  CardHeader,
} from "@chromify/ui/components/card";
import ThemeCardOptions from "@/components/theme/card/card-options";
import CopyButton from "@/components/theme/copy-button/default";
import { generateThemeCss } from "@/lib/utils";
import type { DBTheme } from "@/types/theme";

function MarketThemeCard({ theme }: { theme: DBTheme }) {
  return (
    <Card className="group relative overflow-hidden transition-all hover:shadow-lg hover:shadow-primary/5">
      <Link
        href={`/shared/${theme.shared_id}`}
        className="absolute inset-0 z-10"
        aria-label={`View ${theme.name} theme details`}
      />
      <CardHeader className="pb-4">
        <div className="space-y-1.5">
          <h3 className="font-semibold text-lg leading-none tracking-tight group-hover:text-primary transition-colors">
            {theme.name}
          </h3>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="relative aspect-[2/1] rounded-lg overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-5 dark:hidden">
            <div
              className="relative"
              style={{ backgroundColor: `${theme.colors.primary}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.colors.secondary}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.colors.accent}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.colors["muted"]} !important` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.colors["popover"]}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
          </div>

          {/* Dark mode colors (only shown in dark mode) */}
          <div className="absolute inset-0 hidden dark:grid dark:grid-cols-5">
            <div
              className="relative"
              style={{ backgroundColor: theme.dark_colors?.primary }}
            />
            <div
              className="relative size-full"
              style={{ backgroundColor: `${theme.dark_colors?.secondary}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.dark_colors?.accent}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{
                backgroundColor: `${theme.dark_colors?.["muted"]} `,
              }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
            <div
              className="relative"
              style={{ backgroundColor: `${theme.dark_colors?.["popover"]}` }}
            >
              <div
                className="absolute inset-0 shadow-[inset_0_0_0_1px_rgba(0,0,0,0.05)]"
                aria-hidden="true"
              />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center w-full gap-3">
          <div className="flex flex-wrap gap-4 relative ">
            <ThemeCardOptions
              format={theme.format}
              borderRaduis={theme.theme_requests?.border_radius || "0.5"}
            />

            <div className="flex items-center gap-2">
              {theme.dark_colors && (
                <Badge
                  variant="secondary"
                  className="h-6 px-2 text-xs font-medium inline-flex items-center"
                >
                  <MoonIcon className="size-3 mr-1" />
                  Dark Mode
                </Badge>
              )}
              {theme.colors["chart-1"] && (
                <Badge
                  variant="secondary"
                  className="h-6 px-2 text-xs font-medium inline-flex items-center"
                >
                  <BarChart className="size-3 mr-1" />
                  Charts
                </Badge>
              )}
              {theme.colors.sidebar && (
                <Badge
                  variant="secondary"
                  className="h-6 px-2 text-xs font-medium inline-flex items-center"
                >
                  <PanelLeftIcon className="size-3 mr-1" />
                  Sidebar
                </Badge>
              )}
            </div>
          </div>
          <div className="relative z-20 pointer-events-auto">
            <CopyButton
              css={generateThemeCss({
                format: theme.format,
                colors: theme.colors,
                dark_colors: theme.dark_colors,
              })}
            />
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

export default MarketThemeCard;
