"use client";

import { useEffect, useState, useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ChartLineIcon, Loader2, Moon, PanelLeftIcon, X } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";
import { Toggle } from "@chromify/ui/components/toggle";
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@chromify/ui/components/toggle-group";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";

export default function FilterOptions() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [isPending, startTransition] = useTransition();
  const [mounted, setMounted] = useState(false);

  // Handle format selection
  const handleFormatChange = (value: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on filter change

      if (value && value !== "all") {
        params.set("format", value);
      } else {
        params.delete("format");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Handle primary hue selection
  const handlePrimaryHueChange = (value: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on filter change

      if (value && value !== "all") {
        params.set("primaryHue", value);
      } else {
        params.delete("primaryHue");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Handle toggle filters
  const handleToggleFilter = (filterName: string, isActive: boolean) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on filter change

      if (isActive) {
        params.set(filterName, "true");
      } else {
        params.delete(filterName);
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    startTransition(() => {
      const params = new URLSearchParams();
      // Preserve search query if it exists
      const query = searchParams.get("query");
      if (query) {
        params.set("query", query);
      }
      // Preserve sort option if it exists
      const sortOption = searchParams.get("sort");
      if (sortOption) {
        params.set("sort", sortOption);
      }
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Check if any filters are active
  const hasActiveFilters =
    searchParams.has("format") ||
    searchParams.has("darkMode") ||
    searchParams.has("sidebar") ||
    searchParams.has("chart") ||
    searchParams.has("primaryHue");

  // Set mounted state to handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render client-side to avoid hydration mismatch
  if (!mounted) return null;

  return (
    <div className="flex flex-col gap-6 w-full">
      {/* Primary Hue filter */}
      <div className="w-full flex items-center gap-4">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handlePrimaryHueChange("all")}
            className="h-6"
            disabled={isPending}
          >
            All colors
          </Button>
        </div>
        <div className="overflow-x-auto">
          <ToggleGroup
            type="single"
            value={searchParams.get("primaryHue")?.toString() || ""}
            onValueChange={handlePrimaryHueChange}
            className="inline-flex flex-nowrap gap-4 md:flex-wrap rounded-full overflow-hidden"
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="red"
                    className="size-6 rounded-full p-1"
                    aria-label="Red"
                  >
                    <div className="rounded-full size-4 bg-red-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Red</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="orange"
                    className="size-6 rounded-full p-1"
                    aria-label="Orange"
                  >
                    <div className="rounded-full size-4 bg-orange-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Orange</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="amber"
                    className="size-6 rounded-full p-1"
                    aria-label="Amber"
                  >
                    <div className="rounded-full size-4 bg-amber-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Amber</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="yellow"
                    className="size-6 rounded-full p-1"
                    aria-label="Yellow"
                  >
                    <div className="rounded-full size-4 bg-yellow-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Yellow</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="lime"
                    className="size-6 rounded-full p-1"
                    aria-label="Lime"
                  >
                    <div className="rounded-full size-4 bg-lime-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Lime</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="green"
                    className="size-6 rounded-full p-1"
                    aria-label="Green"
                  >
                    <div className="rounded-full size-4 bg-green-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Green</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="emerald"
                    className="size-6 rounded-full p-1"
                    aria-label="Emerald"
                  >
                    <div className="rounded-full size-4 bg-emerald-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Emerald</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="green"
                    className="size-6 rounded-full p-1"
                    aria-label="Green"
                  >
                    <div className="rounded-full size-4 bg-teal-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Teal</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="cyan"
                    className="size-6 rounded-full p-1"
                    aria-label="Cyan"
                  >
                    <div className="rounded-full size-4 bg-cyan-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Cyan</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="sky"
                    className="size-6 rounded-full p-1"
                    aria-label="Sky"
                  >
                    <div className="rounded-full size-4 bg-sky-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Sky</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="blue"
                    className="size-6 rounded-full p-1"
                    aria-label="Blue"
                  >
                    <div className="rounded-full size-4 bg-blue-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Blue</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="indigo"
                    className="size-6 rounded-full p-1"
                    aria-label="Indigo"
                  >
                    <div className="rounded-full size-4 bg-indigo-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Indigo</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="violet"
                    className="size-6 rounded-full p-1"
                    aria-label="Violet"
                  >
                    <div className="rounded-full size-4 bg-violet-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Violet</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="purple"
                    className="size-6 rounded-full p-1"
                    aria-label="Purple"
                  >
                    <div className="rounded-full size-4 bg-purple-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Purple</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="fuchsia"
                    className="size-6 rounded-full p-1"
                    aria-label="Fuchsia"
                  >
                    <div className="rounded-full size-4 bg-fuchsia-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Fuchsia</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="pink"
                    className="size-6 rounded-full p-1"
                    aria-label="Pink"
                  >
                    <div className="rounded-full size-4 bg-pink-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Pink</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="rose"
                    className="size-6 rounded-full p-1"
                    aria-label="Rose"
                  >
                    <div className="rounded-full size-4 bg-rose-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Rose</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="gray"
                    className="size-6 rounded-full p-1"
                    aria-label="Gray"
                  >
                    <div className="rounded-full size-4 bg-gray-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Gray</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="slate"
                    className="size-6 rounded-full p-1"
                    aria-label="Slate"
                  >
                    <div className="rounded-full size-4 bg-slate-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Slate</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ToggleGroupItem
                    value="stone"
                    className="size-6 rounded-full p-1"
                    aria-label="Stone"
                  >
                    <div className="rounded-full size-4 bg-stone-500" />
                  </ToggleGroupItem>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Stone</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </ToggleGroup>
        </div>
      </div>
      <div className="flex flex-col md:flex-row items-center gap-6">
        {/* Format filter */}
        <div className="relative w-full md:w-[180px]">
          <Select
            defaultValue={searchParams.get("format")?.toString() || "all"}
            onValueChange={handleFormatChange}
            disabled={isPending}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Formats</SelectItem>
              <SelectItem value="hsl">HSL (Tailwind v3)</SelectItem>
              <SelectItem value="oklch">OKLCH (Tailwind v4)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Toggle filters with Clear All button */}
        <div className="flex justify-between items-center w-full">
          <div className="flex flex-wrap gap-2">
            <Toggle
              variant={searchParams.has("darkMode") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("darkMode")}
              onPressedChange={(pressed) =>
                handleToggleFilter("darkMode", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <Moon className="h-4 w-4" />
              <span>Dark Mode</span>
            </Toggle>

            <Toggle
              variant={searchParams.has("sidebar") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("sidebar")}
              onPressedChange={(pressed) =>
                handleToggleFilter("sidebar", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <PanelLeftIcon className="h-4 w-4" />
              <span>Sidebar</span>
            </Toggle>

            <Toggle
              variant={searchParams.has("chart") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("chart")}
              onPressedChange={(pressed) =>
                handleToggleFilter("chart", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <ChartLineIcon className="h-4 w-4" />
              <span>Charts</span>
            </Toggle>
          </div>

          {/* Clear All button - only shown when filters are active */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAllFilters}
              disabled={isPending}
              className="text-muted-foreground hover:text-foreground"
            >
              {isPending ? (
                <Loader2 className="h-3 w-3 mr-2 animate-spin" />
              ) : (
                <X className="h-3 w-3 mr-2" />
              )}
              Clear All
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
