import type { <PERSON>ada<PERSON> } from "next";

import { Footer } from "@/components/site/footer";
import { ChromifyHeader } from "@/components/site/header";

export const metadata: Metadata = {
  title: "Chromify - AI-Powered Color Themes for Modern Web Development",
  description:
    "Transform your web projects with stunning, accessible color schemes generated by AI. Chromify combines expert color theory with artificial intelligence to create perfect color palettes for any project.",
};

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col">
      <ChromifyHeader />
      <main className="flex-grow flex-1 min-h-screen my-20">{children}</main>
      <Footer />
    </div>
  );
}
