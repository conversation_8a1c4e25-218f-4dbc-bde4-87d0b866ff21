"use client";

import * as React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@clerk/nextjs";
import { ArrowRight, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@chromify/ui/components/alert-dialog";
import { But<PERSON> } from "@chromify/ui/components/button";
import { Input } from "@chromify/ui/components/input";
import { Label } from "@chromify/ui/components/label";
import { createUserProfile } from "@/lib/actions/user";

function OnboardingDialog({ onClose }: { onClose: () => void }) {
  const [step, setStep] = useState(1);
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useUser();
  const router = useRouter();

  const stepContent = [
    {
      title: "Welcome to Chromify",
      description:
        "The AI-powered color system generator for modern web development. Create sophisticated, accessible color schemes with the power of AI and expert color theory.",
      icon: "sparkles",
      colors: {
        primary: "#8B5CF6",
        secondary: "#3B82F6",
        accent: "#10B981",
        background: "#F9FAFB",
      },
    },
    {
      title: "AI-Powered Generation",
      description:
        "Describe your vision in plain language and let our AI create the perfect color scheme for your project. The AI understands both color theory and development contexts, delivering sophisticated, accessible, and visually harmonious color systems.",
      icon: "lightbulb",
      colors: {
        primary: "#EC4899",
        secondary: "#8B5CF6",
        accent: "#FBBF24",
        background: "#FFFBEB",
      },
    },
    {
      title: "Modern Format Support",
      description:
        "Generate color themes in the format that matches your tech stack: HSL format for Tailwind CSS v3 projects or OKLCH format for Tailwind CSS v4 projects with enhanced color perception.",
      icon: "code",
      colors: {
        primary: "#111827",
        secondary: "#4B5563",
        accent: "#D1D5DB",
        background: "#F3F4F6",
      },
    },
    {
      title: "Light & Dark Modes",
      description:
        "Every color scheme comes with perfectly balanced light and dark variants that maintain your brand identity while ensuring proper contrast and readability in both modes.",
      icon: "palette",
      colors: {
        primary: "#2563EB",
        secondary: "#7C3AED",
        accent: "#06B6D4",
        background: "#0F172A",
      },
    },
    {
      title: "Complete Your Profile",
      description: "Tell us your name to personalize your experience. ",
      icon: "user",
      colors: {
        primary: "#0047AB",
        secondary: "#6082B6",
        accent: "#36454F",
        background: "#F5F5F5",
      },
    },
  ] as const;

  const totalSteps = stepContent.length;

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError("Please enter your name");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const res = await createUserProfile({
        subscription_tier: "free",
        name: name,
      });

      if (res?.success) {
        // Reloads the user's data from the Clerk API
        await user?.reload();
        onClose();
        router.push("/dashboard");
      } else {
        setError(res?.error || "Failed to create profile");
      }
    } catch (err) {
      setError("An unexpected error occurred");
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Ensure step is within bounds
  const safeStep = Math.max(1, Math.min(step, totalSteps));
  const currentStep = stepContent[safeStep - 1] || stepContent[0];

  return (
    <AlertDialog
      open={true}
      onOpenChange={(open) => {
        if (!open) onClose();
        if (open) setStep(1);
      }}
    >
      <AlertDialogContent className="gap-0 p-0 max-w-md w-full overflow-hidden">
        <div className="relative">
          {/* Navigation Arrows */}
          <Button
            onClick={() => setStep(step - 1)}
            className="absolute -left-0 -bottom-4  h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm border border-muted flex items-center justify-center shadow-sm hover:bg-muted/20 transition-colors z-10"
            size="icon"
            variant="ghost"
            disabled={step === 1}
            aria-label="Previous step"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button
            onClick={() => setStep(step + 1)}
            className="absolute -right-0 -bottom-4 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm border border-muted flex items-center justify-center shadow-sm hover:bg-muted/20 transition-colors z-10"
            size="icon"
            variant="ghost"
            disabled={step === totalSteps}
            aria-label="Next step"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {step < totalSteps ? (
            <div
              className="w-full h-48 rounded-t-lg overflow-hidden"
              style={{
                background: `linear-gradient(135deg, ${currentStep.colors.primary} 0%, ${currentStep.colors.secondary} 50%, ${currentStep.colors.accent} 100%)`,
              }}
            ></div>
          ) : (
            <div
              style={{
                background: `linear-gradient(135deg, ${currentStep.colors.primary} 0%, ${currentStep.colors.secondary} 50%, ${currentStep.colors.accent} 100%)`,
              }}
              className="w-full h-48 flex flex-col items-center justify-center px-6 pt-6 pb-2 bg-background border-b"
            >
              <div className="text-2xl font-semibold text-center mb-2">
                {currentStep.title}
              </div>
              <div className="text-sm text-center max-w-xs">
                {currentStep.description}
              </div>
            </div>
          )}
        </div>

        <div className="px-6 pb-6 pt-5">
          {step < totalSteps && (
            <AlertDialogHeader className="mb-6">
              <div className="min-h-[120px]">
                <div className="flex items-center gap-2 mb-2">
                  <AlertDialogTitle>{currentStep.title}</AlertDialogTitle>
                </div>
                <div className="overflow-y-auto">
                  <AlertDialogDescription className="text-sm">
                    {currentStep.description}
                  </AlertDialogDescription>
                </div>
              </div>
            </AlertDialogHeader>
          )}
          <div className="flex flex-col items-center gap-4 relative">
            {step === totalSteps && (
              <div className="flex flex-col gap-3 w-full max-w-sm mx-auto">
                <div className="space-y-3 p-5">
                  <div>
                    <Label
                      htmlFor="name"
                      className="text-sm font-medium block mb-2"
                    >
                      Your Name
                    </Label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your name"
                      className="w-full"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      autoFocus
                    />
                    <p className="text-xs text-muted-foreground mt-2">
                      We'll use this to personalize your experience
                    </p>
                  </div>
                  {error && (
                    <p className="text-destructive text-sm mt-2">{error}</p>
                  )}
                </div>
              </div>
            )}
            <div className="flex justify-center">
              {step === totalSteps && (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full max-w-sm mx-auto bg-primary hover:bg-primary/90"
                >
                  {isSubmitting ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="-me-1 ms-2 animate-spin" />
                      Submitting...
                    </span>
                  ) : (
                    <span className="flex items-center justify-center">
                      Complete Setup
                      <ArrowRight
                        className="-me-1 ms-2 opacity-60 transition-transform group-hover:translate-x-0.5"
                        size={16}
                        strokeWidth={2}
                        aria-hidden="true"
                      />
                    </span>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
        {/* Step indicator text */}
        <div className="text-xs absolute top-2 right-2 z-50 font-medium bg-muted/30 px-3 py-1.5 rounded-full flex items-center gap-1.5">
          <span className="font-semibold">{step}</span>
          <span className="text-muted-foreground">of</span>
          <span className="font-semibold">{totalSteps}</span>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default function Page() {
  const [show, setShow] = React.useState(true);

  // If dialog is closed, we'll redirect to dashboard
  const handleClose = () => {
    setShow(false);
  };

  return (
    <div className="flex-grow h-screen min-h-svw container">
      {show && <OnboardingDialog onClose={handleClose} />}
    </div>
  );
}
