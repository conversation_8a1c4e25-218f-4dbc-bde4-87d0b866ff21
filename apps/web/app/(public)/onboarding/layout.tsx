import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";

export const metadata: Metadata = {
  title: "Welcome to Chromify - Complete Your Setup",
  description:
    "Complete your Chromify onboarding to start creating beautiful, AI-generated color themes for your web development projects.",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  if ((await auth()).sessionClaims?.metadata.onboardingComplete === true) {
    redirect("/");
  }

  return <>{children}</>;
}
