import { <PERSON>, <PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import {
  getFavoriteThemes,
  ThemeFilterOptions,
  ThemeSortOption,
} from "@/services/theme-history";
import { Pagination } from "../../_shared/components";
import { FavoriteCard } from "./favorite-card";

type SearchParams = { [key: string]: string | string[] | undefined };

async function ThemeList(props: { searchParams: SearchParams }) {
  const searchParams = props.searchParams;
  const query = (searchParams?.query as string) || "";
  const currentPage = Number(searchParams?.page) || 1;
  const sortOption = (searchParams?.sort || "newest") as ThemeSortOption;

  // Parse filter options
  const filterOptions: ThemeFilterOptions = {
    query,
    format: searchParams?.format as "hsl" | "oklch" | "all" | undefined,
    hasDarkMode: searchParams?.darkMode === "true",
    hasSidebar: searchParams?.sidebar === "true",
    hasChart: searchParams?.chart === "true",
  };

  // This await will trigger the Suspense boundary
  const { themes, totalPages } = await getFavoriteThemes(
    currentPage,
    6,
    sortOption,
    filterOptions
  );

  if (themes.length === 0) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="p-8 text-center">
          <Star className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-xl font-medium mb-2">No favorites found</h3>
          <p className="text-muted-foreground mb-6">
            {query
              ? "Try adjusting your search or filters."
              : "Star your favorite themes to see them here."}
          </p>
          <Button asChild variant="outline">
            <Link href="/theme/history">Browse Your Themes</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {themes.map((theme) => (
          <FavoriteCard theme={theme} key={theme.id} />
        ))}
      </div>
      <Pagination totalPages={totalPages} />
    </div>
  );
}

export default ThemeList;
