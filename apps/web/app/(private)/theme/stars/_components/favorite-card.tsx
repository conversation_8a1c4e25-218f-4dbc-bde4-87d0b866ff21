import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@chromify/ui/components/card";
import { Separator } from "@chromify/ui/components/separator";
import { cn } from "@chromify/ui/lib/utils";
import ThemeCardConfig from "@/components/theme/card/card-config";
import ThemeCardOptions from "@/components/theme/card/card-options";
import DeleteThemeButton from "@/components/theme/delete-button/default";
import FavoriteButton from "@/components/theme/favi-button/default";
import SwatchPreview from "@/components/theme/swatch-preview";
import type { DBTheme } from "@/types/theme";
import ActiveBadge from "../../history/_components/active-badge";

interface FavoriteCardProps {
  theme: DBTheme;
}

export function FavoriteCard({ theme }: FavoriteCardProps) {
  // Format the date
  const createdAt = new Date(theme.created_at);
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });

  // Get the user prompt
  const userPrompt = theme.theme_requests?.user_prompt || "";
  const hasUserPrompt = !!userPrompt;

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all relative group",
        "border shadow-sm hover:border-primary/30 hover:shadow-md"
      )}
    >
      <CardHeader className="relative">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <SwatchPreview
              themeColors={{
                colors: theme.colors,
                dark_colors: theme.dark_colors,
              }}
            />
            <Button
              asChild
              variant={"link"}
              className="px-0 text-base text-foreground"
            >
              <Link href={`/theme/${theme.request_id}`}>{theme.name}</Link>
            </Button>

            <ActiveBadge themeName={theme.name} />
          </div>
          <FavoriteButton themeId={theme.id} isFavorite={theme.is_favorite} />
        </div>
      </CardHeader>
      <CardContent className="space-y-4 -mt-5 flex-1">
        {/* Theme Options Section, Tailwind version and border radius */}
        <ThemeCardOptions
          format={theme.format}
          borderRaduis={theme.theme_requests?.border_radius || "0.5"}
        />
        {/* Other options */}
        <ThemeCardConfig theme={theme} />
        {/* User Prompt Section */}
        {hasUserPrompt && (
          <>
            <div className="mb-2">
              <p className="text-sm text-muted-foreground line-clamp-2">
                {userPrompt}
              </p>
            </div>

            <Separator className="my-2" />
          </>
        )}
      </CardContent>
      <CardFooter>
        <time className="text-xs block text-muted-foreground text-center">
          Created {timeAgo}
        </time>
        <div className="absolute bottom-3 right-3">
          <div className="flex items-center gap-2">
            <DeleteThemeButton
              themeId={theme.id}
              isFavorite={theme.is_favorite}
              themeName={theme.name}
            />
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
