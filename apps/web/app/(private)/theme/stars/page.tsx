import type { Metadata } from "next";

import {
  SearchFilter,
  SortOptions,
  ThemePageLayout,
} from "../_shared/components";
import ThemeList from "./_components/theme-list";

type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export const metadata: Metadata = {
  title: "Favorite Themes - Your Saved Color Palettes",
  description:
    "Access your starred and favorite color themes for quick reference and inspiration. Easily find and reuse your most-loved color palettes.",
};

async function FavoritesPage(props: { searchParams: SearchParams }) {
  const searchParams = await props.searchParams;
  const suspenseKey = `${searchParams.query || ""}-${searchParams.page || "1"}-${searchParams.sort || "newest"}-${JSON.stringify(
    {
      format: searchParams.format,
      darkMode: searchParams.darkMode,
      sidebar: searchParams.sidebar,
      chart: searchParams.chart,
    }
  )}`;

  return (
    <ThemePageLayout
      title="Your Favorite Themes"
      description="Access your starred themes for quick reference and inspiration."
      searchFilter={<SearchFilter showFavoriteFilter={false} />}
      sortOptions={<SortOptions />}
      themeList={<ThemeList searchParams={searchParams} />}
      suspenseKey={suspenseKey}
    />
  );
}

export default FavoritesPage;
