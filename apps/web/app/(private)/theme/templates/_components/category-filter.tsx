"use client";

import { LucideIcon } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

interface CategoryFilterProps {
  categories: {
    id: string;
    name: string;
    icon: LucideIcon;
  }[];
  activeCategory: string | null;
  onCategoryChange: (categoryId: string | null) => void;
}

export function CategoryFilter({
  categories,
  activeCategory,
  onCategoryChange,
}: CategoryFilterProps) {
  return (
    <div className="flex flex-wrap gap-2 mb-8 p-4">
      <Button
        variant={activeCategory === null ? "default" : "outline"}
        size="sm"
        onClick={() => onCategoryChange(null)}
        className="rounded-full"
      >
        All Categories
      </Button>

      {categories.map((category) => {
        const Icon = category.icon;
        return (
          <Button
            key={category.id}
            variant={activeCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => onCategoryChange(category.id)}
            className={cn(
              "rounded-full",
              activeCategory === category.id &&
                "bg-primary text-primary-foreground"
            )}
          >
            <Icon className="mr-1 h-3.5 w-3.5" />
            {category.name}
          </Button>
        );
      })}
    </div>
  );
}

export default CategoryFilter;
