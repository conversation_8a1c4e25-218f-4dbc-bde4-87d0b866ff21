"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowRight, LucideIcon } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { But<PERSON> } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { cn } from "@chromify/ui/lib/utils";

interface QuickStartTemplateProps {
  title: string;
  description: string;
  prompt: string;
  icon: LucideIcon;
  category?: string;
  colors?: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

export function QuickStartTemplate({
  title,
  description,
  prompt,
  icon: Icon,
  category,
  colors = {
    primary: "#8B5CF6",
    secondary: "#3B82F6",
    accent: "#10B981",
    background: "#F9FAFB",
  },
}: QuickStartTemplateProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    // Encode the prompt for URL
    const encodedPrompt = encodeURIComponent(prompt);
    router.push(`/theme/create?prompt=${encodedPrompt}`);
  };

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all cursor-pointer relative",
        "border hover:border-primary/30",
        isHovered ? "shadow-md" : "shadow-sm"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Color swatches preview */}
      <div className="absolute top-0 right-0 h-16 w-16 overflow-hidden">
        <div className="absolute top-0 right-0 h-32 w-32 -rotate-45 transform origin-top-right">
          <div
            className="absolute top-0 h-2 w-full"
            style={{ backgroundColor: colors.primary }}
          ></div>
          <div
            className="absolute top-2 h-2 w-full"
            style={{ backgroundColor: colors.secondary }}
          ></div>
          <div
            className="absolute top-4 h-2 w-full"
            style={{ backgroundColor: colors.accent }}
          ></div>
          <div
            className="absolute top-6 h-2 w-full"
            style={{ backgroundColor: colors.background }}
          ></div>
        </div>
      </div>

      {category && (
        <Badge
          variant="outline"
          className="absolute top-2 left-2 text-[10px] bg-background/80 backdrop-blur-sm z-10"
        >
          {category}
        </Badge>
      )}

      <CardHeader className="pb-2 pt-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <CardTitle className="text-base">{title}</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <CardDescription className="line-clamp-2 text-xs">
          {description}
        </CardDescription>
      </CardContent>

      <CardFooter className="pt-0 pb-3 flex justify-end">
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-0 h-auto text-xs text-primary gap-1 opacity-0 -translate-x-2",
            "transition-all duration-200",
            isHovered && "opacity-100 translate-x-0"
          )}
        >
          <span>Use template</span>
          <ArrowRight className="h-3 w-3" />
        </Button>
      </CardFooter>
    </Card>
  );
}

export default QuickStartTemplate;
