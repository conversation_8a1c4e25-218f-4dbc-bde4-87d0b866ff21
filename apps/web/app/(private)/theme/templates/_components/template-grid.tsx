"use client";

import { Filter, LucideIcon } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import TemplateCard from "./template-card";

interface Template {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: LucideIcon;
  category: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

interface Category {
  id: string;
  name: string;
  icon: LucideIcon;
}

interface TemplateGridProps {
  categories: Record<string, Category>;
  filteredTemplates: Template[];
}

export function TemplateGrid({
  categories,
  filteredTemplates,
}: TemplateGridProps) {
  return (
    <div>
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-12 border rounded-lg bg-card/50">
          <Filter className="h-10 w-10 mx-auto mb-3 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-medium mb-2">No templates found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Try adjusting your search or filter criteria
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            Reset filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <TemplateCard
              key={template.id}
              id={template.id}
              title={template.title}
              description={template.description}
              prompt={template.prompt}
              icon={template.icon}
              category={template.category}
              categoryName={categories[template.category]?.name || ""}
              colors={template.colors}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default TemplateGrid;
