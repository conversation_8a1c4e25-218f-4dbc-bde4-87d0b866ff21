"use client";

import { useRef } from "react";
import { ChevronLeft, ChevronRight, LucideIcon } from "lucide-react";

import QuickStartTemplate from "@/app/(private)/theme/templates/_components/quick-start-template";
import { promptTemplates } from "@/constants/theme/prompt-templates";

function MoreTemplates() {
  const promptsScrollRef = useRef<HTMLDivElement>(null);

  const scrollLeft = (containerRef: React.RefObject<HTMLDivElement | null>) => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const scrollRight = (
    containerRef: React.RefObject<HTMLDivElement | null>
  ) => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };
  return (
    <div className="relative">
      <h3 className="text-sm font-medium mb-3">More Theme Ideas</h3>

      <div className="relative group">
        <div
          ref={promptsScrollRef}
          className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide snap-x snap-mandatory"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {promptTemplates.map((template) => (
            <div
              key={template.title}
              className="min-w-[260px] snap-start flex-shrink-0"
            >
              <QuickStartTemplate
                title={template.title}
                description={template.description}
                prompt={template.prompt}
                icon={template.icon as LucideIcon}
                category={template.category}
                colors={template.colors}
              />
            </div>
          ))}
        </div>

        {/* Scroll buttons */}
        <button
          onClick={() => scrollLeft(promptsScrollRef)}
          className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-3 bg-background shadow-md rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
          aria-label="Scroll left"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
        <button
          onClick={() => scrollRight(promptsScrollRef)}
          className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-3 bg-background shadow-md rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
          aria-label="Scroll right"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}

export default MoreTemplates;
