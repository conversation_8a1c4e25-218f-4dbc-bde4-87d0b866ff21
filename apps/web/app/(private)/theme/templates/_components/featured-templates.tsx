import { LucideIcon, Spark<PERSON> } from "lucide-react";

import Template<PERSON>ard from "./template-card";

// Featured template IDs - these would typically be curated by the team
const FEATURED_TEMPLATE_IDS = [
  "tech-dashboard",
  "minimal-elegant",
  "playful-interface",
  "luxury-gold",
  "cyberpunk-neon",
];

interface Template {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: LucideIcon;
  category: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

interface Category {
  id: string;
  name: string;
  icon: LucideIcon;
}

interface FeaturedTemplatesProps {
  templates: Template[];
  categories: Record<string, Category>;
}

export function FeaturedTemplates({
  templates,
  categories,
}: FeaturedTemplatesProps) {
  // Filter templates to only include featured ones
  const featuredTemplates = templates.filter((template) =>
    FEATURED_TEMPLATE_IDS.includes(template.id)
  );

  return (
    <div className="mb-10 w-full">
      <div className="flex items-center mb-6">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Sparkles className="h-4 w-4 text-primary" />
          </div>
          <h2 className="text-xl font-semibold">Featured Templates</h2>
        </div>
      </div>

      {/* Use CSS Grid for responsive card layout */}
      <div className="grid grid-flow-col auto-cols-[calc(100%-2rem)] sm:auto-cols-[calc(50%-1rem)] md:auto-cols-[calc(33.333%-1rem)] lg:auto-cols-[calc(35%-1rem)] gap-4 overflow-x-auto pb-4 snap-x">
        {featuredTemplates.map((template) => (
          <div key={template.id} className="snap-start">
            <TemplateCard
              id={template.id}
              title={template.title}
              description={template.description}
              prompt={template.prompt}
              icon={template.icon}
              category={template.category}
              categoryName={categories[template.category]?.name || ""}
              colors={template.colors}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default FeaturedTemplates;
