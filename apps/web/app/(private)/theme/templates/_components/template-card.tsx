"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowRight, LucideIcon } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";

interface TemplateCardProps {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: LucideIcon;
  category: string;
  categoryName: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

export function TemplateCard({
  title,
  description,
  prompt,
  icon: Icon,
  categoryName,
  colors,
}: TemplateCardProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    // Encode the prompt for URL
    const encodedPrompt = encodeURIComponent(prompt);
    router.push(`/theme/create?prompt=${encodedPrompt}`);
  };

  // Get main colors for display
  const mainColors = [
    { name: "Primary", color: colors.primary },
    { name: "Secondary", color: colors.secondary },
    { name: "Accent", color: colors.accent },
    { name: "Background", color: colors.background },
  ];

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all cursor-pointer h-full flex flex-col relative",
        "border hover:border-primary/30",
        isHovered ? "shadow-md" : "shadow-sm"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Color swatches preview */}
      <div className="absolute top-0 right-0 h-16 w-16 overflow-hidden z-0">
        <div className="absolute top-0 right-0 h-32 w-32 -rotate-45 transform origin-top-right">
          <div
            className="absolute top-0 h-2 w-full"
            style={{ backgroundColor: colors.primary }}
          ></div>
          <div
            className="absolute top-2 h-2 w-full"
            style={{ backgroundColor: colors.secondary }}
          ></div>
          <div
            className="absolute top-4 h-2 w-full"
            style={{ backgroundColor: colors.accent }}
          ></div>
          <div
            className="absolute top-6 h-2 w-full"
            style={{ backgroundColor: colors.background }}
          ></div>
        </div>
      </div>

      <Badge
        variant="outline"
        className="absolute top-2 left-2 text-[10px] bg-background/80 backdrop-blur-sm z-10"
      >
        {categoryName}
      </Badge>

      <CardHeader className="pb-2 pt-6 z-20">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <CardTitle className="text-base">{title}</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="pb-2 flex-grow px-6">
        <CardDescription className="line-clamp-2 text-xs">
          {description}
        </CardDescription>

        {/* Color swatch preview - similar to theme-card */}
        <div className="relative mt-4">
          <div className="flex gap-1">
            {mainColors.slice(0, 3).map((colorItem, index) => (
              <TooltipProvider key={index}>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <div
                      className={cn(
                        "h-6 w-6 rounded-md border shadow-sm transition-transform",
                        isHovered && "transform hover:scale-110"
                      )}
                      style={{ backgroundColor: colorItem.color }}
                    />
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="text-xs">
                    <p>
                      {colorItem.name}: {colorItem.color}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-0 pb-3 flex justify-end px-6">
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "p-0 h-auto text-xs text-primary gap-1 opacity-0 -translate-x-2 hover:text-primary hover:bg-transparent",
            "transition-all duration-200",
            isHovered && "opacity-100 translate-x-0"
          )}
        >
          <span>Use template</span>
          <ArrowRight className="h-3 w-3" />
        </Button>
      </CardFooter>
    </Card>
  );
}

export default TemplateCard;
