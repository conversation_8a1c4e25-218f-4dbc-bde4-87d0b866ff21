"use client";

import { useEffect, useState } from "react";
import { Filter, LucideIcon } from "lucide-react";

import TitleMeta from "@/components/title-meta";
import colorThemeData from "@/constants/theme/t";
import CategoryFilter from "./_components/category-filter";
import FeaturedTemplates from "./_components/featured-templates";
import SearchBar from "./_components/search-bar";
import TemplateGrid from "./_components/template-grid";

// Define types based on the data structure
interface TemplateColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
}

interface Template {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: LucideIcon;
  category: string;
  colors: TemplateColors;
}

interface Category {
  id: string;
  name: string;
  icon: LucideIcon;
}

function Page() {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>(
    colorThemeData.allTemplates as Template[]
  );

  // Filter templates based on category and search query
  useEffect(() => {
    let filtered = colorThemeData.allTemplates as Template[];

    // Filter by category if one is selected
    if (activeCategory) {
      filtered = filtered.filter(
        (template) => template.category === activeCategory
      );
    }

    // Filter by search query if one exists
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (template) =>
          template.title.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query) ||
          template.prompt.toLowerCase().includes(query)
      );
    }

    setFilteredTemplates(filtered);
  }, [activeCategory, searchQuery]);

  return (
    <div>
      <TitleMeta
        title="Prompt Templates"
        description="Jump-start your theme creation with curated prompt templates. Browse,
          preview, and customize professional prompts tailored for any project."
      />

      <FeaturedTemplates
        templates={colorThemeData.allTemplates as Template[]}
        categories={colorThemeData.categories as Record<string, Category>}
      />

      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-primary/10">
              <Filter className="h-4 w-4 text-primary" />
            </div>
            <h2 className="text-xl font-semibold">All Templates</h2>
          </div>
          <div className="text-sm text-muted-foreground bg-muted/20 px-3 py-1 rounded-full">
            {filteredTemplates.length} of {colorThemeData.allTemplates.length}{" "}
            templates
          </div>
        </div>
        <div className="flex-1">
          <SearchBar onSearch={setSearchQuery} />
        </div>

        <div>
          <CategoryFilter
            categories={Object.values(colorThemeData.categories) as Category[]}
            activeCategory={activeCategory}
            onCategoryChange={setActiveCategory}
          />
          <TemplateGrid
            categories={colorThemeData.categories as Record<string, Category>}
            filteredTemplates={filteredTemplates}
          />
        </div>
      </div>
    </div>
  );
}

export default Page;
