"use client";

import { useState } from "react";
import { Brush, Code, Layers, Palette } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { cn } from "@chromify/ui/lib/utils";

// Template definitions - same as in dashboard
const templates = [
  {
    title: "Modern Dashboard",
    description:
      "Clean, professional dashboard with subtle gradients and clear hierarchy",
    prompt:
      "Create a modern dashboard theme with subtle gradients, professional look, and clear visual hierarchy",
    icon: Layers,
    category: "Business",
    colors: {
      primary: "#3B82F6",
      secondary: "#6366F1",
      accent: "#10B981",
      background: "#F9FAFB",
    },
  },
  {
    title: "Playful Interface",
    description:
      "Vibrant, energetic colors for creative applications and youth-oriented products",
    prompt:
      "Design a playful, vibrant interface with energetic colors suitable for creative applications",
    icon: Palette,
    category: "Creative",
    colors: {
      primary: "#EC4899",
      secondary: "#8B5CF6",
      accent: "#FBBF24",
      background: "#FFFBEB",
    },
  },
  {
    title: "Minimal & Elegant",
    description:
      "Sophisticated, understated design with careful attention to typography and spacing",
    prompt:
      "Generate a minimal, elegant theme with sophisticated color choices and understated design",
    icon: Brush,
    category: "Minimal",
    colors: {
      primary: "#111827",
      secondary: "#4B5563",
      accent: "#D1D5DB",
      background: "#F3F4F6",
    },
  },
  {
    title: "Tech Dashboard",
    description:
      "Modern tech-inspired theme with dark mode and vibrant accents",
    prompt:
      "Create a tech-inspired dashboard theme with dark mode support and vibrant accent colors",
    icon: Code,
    category: "Technical",
    colors: {
      primary: "#2563EB",
      secondary: "#7C3AED",
      accent: "#06B6D4",
      background: "#0F172A",
    },
  },
];

interface TemplateCardProps {
  title: string;
  description: string;
  icon: React.ElementType;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
  isSelected: boolean;
  onClick: () => void;
}

function TemplateCard({
  title,
  description,
  icon: Icon,
  colors,
  isSelected,
  onClick,
}: TemplateCardProps) {
  return (
    <Card
      className={cn(
        "cursor-pointer transition-all",
        isSelected
          ? "border-primary shadow-md ring-2 ring-primary/20"
          : "hover:border-primary/30 hover:shadow-sm"
      )}
      onClick={onClick}
    >
      {/* Color swatches preview */}
      <div className="absolute top-0 right-0 h-16 w-16 overflow-hidden">
        <div className="absolute top-0 right-0 h-32 w-32 -rotate-45 transform origin-top-right">
          <div
            className="absolute top-0 h-2 w-full"
            style={{ backgroundColor: colors.primary }}
          ></div>
          <div
            className="absolute top-2 h-2 w-full"
            style={{ backgroundColor: colors.secondary }}
          ></div>
          <div
            className="absolute top-4 h-2 w-full"
            style={{ backgroundColor: colors.accent }}
          ></div>
          <div
            className="absolute top-6 h-2 w-full"
            style={{ backgroundColor: colors.background }}
          ></div>
        </div>
      </div>

      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <CardTitle className="text-base">{title}</CardTitle>
        </div>
      </CardHeader>

      <CardContent>
        <CardDescription>{description}</CardDescription>
      </CardContent>
    </Card>
  );
}

interface TemplateSelectorProps {
  onSelectTemplate: (prompt: string) => void;
  onClose: () => void;
}

export default function TemplateSelector({
  onSelectTemplate,
  onClose,
}: TemplateSelectorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);

  const handleSelectTemplate = () => {
    if (selectedTemplate !== null && templates[selectedTemplate]) {
      onSelectTemplate(templates[selectedTemplate].prompt);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Choose a Template</CardTitle>
        <CardDescription>
          Select a pre-designed template to start with
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {templates.map((template, index) => (
            <TemplateCard
              key={template.title}
              title={template.title}
              description={template.description}
              icon={template.icon}
              colors={template.colors}
              isSelected={selectedTemplate === index}
              onClick={() => setSelectedTemplate(index)}
            />
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2 border-t p-4">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button
          onClick={handleSelectTemplate}
          disabled={selectedTemplate === null}
        >
          Use Template
        </Button>
      </CardFooter>
    </Card>
  );
}
