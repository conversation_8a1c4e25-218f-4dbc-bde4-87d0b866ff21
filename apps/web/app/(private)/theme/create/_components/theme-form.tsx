"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { ArrowUp, Loader2, Wrench<PERSON>con } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Card, CardContent } from "@chromify/ui/components/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@chromify/ui/components/form";
import { Label } from "@chromify/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@chromify/ui/components/popover";
import {
  RadioGroup,
  RadioGroupItem,
} from "@chromify/ui/components/radio-group";
import { Separator } from "@chromify/ui/components/separator";
import { Switch } from "@chromify/ui/components/switch";
import { Textarea } from "@chromify/ui/components/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";
import { ImageUploader } from "@/components/ImageUploader";
import { createThemeRequest } from "@/lib/actions/theme";
import type { ThemeFormValues } from "@/schema/theme";
import { ThemeFormSchema } from "@/schema/theme";
import TemplateSelector from "./template-selector";

function CreateThemeForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const promptParam = searchParams.get("prompt");
  const templateParam = searchParams.get("template");

  // Define default values with prompt from URL if available
  const defaultValues: ThemeFormValues = {
    description: promptParam || "",
    format: "hsl",
    includeDarkMode: true,
    includeSidebar: true,
    includeChart: true,
    imageData: undefined,
    borderRadius: "0.5",
  };

  const form = useForm<ThemeFormValues>({
    resolver: zodResolver(ThemeFormSchema),
    defaultValues,
  });

  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showTemplateSelector, setShowTemplateSelector] = useState(
    templateParam === "true"
  );

  useEffect(() => {
    if (error) {
      toast.error(error, {
        position: "top-right",
        closeButton: true,
      });
    }
  }, [error]);

  // Set the description field when promptParam changes
  useEffect(() => {
    if (promptParam) {
      form.setValue("description", promptParam);
    }
  }, [promptParam, form]);

  const handleSubmit = async (values: ThemeFormValues) => {
    try {
      setIsPending(true);
      setError(null);
      const result = await createThemeRequest(values);

      if (result?.success) {
        // Handle successful response - redirect programmatically
        router.push(`/theme/${result?.id}`);
      } else {
        // Show error message from server
        setError(result?.error || "Failed to create theme");
      }
    } catch (error) {
      // Handle any unexpected client-side errors
      setError(
        error instanceof Error ? error.message : "An unexpected error occurred"
      );
    } finally {
      setIsPending(false);
    }
  };

  const watchDescription = form.watch("description");
  const watchImageData = form.watch("imageData");

  // Check if description has actual content (not just HTML tags)
  const hasTextContent =
    watchDescription.replace(/<[^>]*>/g, "").trim().length > 0;
  const isSubmitDisabled = !hasTextContent && !watchImageData;

  // Handle template selection
  const handleSelectTemplate = (prompt: string) => {
    form.setValue("description", prompt);
    setShowTemplateSelector(false);
  };

  // Close template selector
  const handleCloseTemplateSelector = () => {
    setShowTemplateSelector(false);
    // If we came from a template link but didn't select anything, clear the URL parameter
    if (templateParam === "true") {
      router.replace("/theme/create");
    }
  };

  return (
    <>
      {showTemplateSelector ? (
        <TemplateSelector
          onSelectTemplate={handleSelectTemplate}
          onClose={handleCloseTemplateSelector}
        />
      ) : (
        <Card className="w-full p-0">
          <CardContent className="p-0">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)}>
                <div className="rounded-lg p-4">
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Textarea
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="What do you want your theme to look like?"
                            className="bg-transparent border-none focus-visible:ring-0 focus-visible:ring-offset-0 resize-none px-2 py-1"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-between items-center mt-2">
                    {/* Left side action buttons */}
                    <motion.div layout>
                      <div className="flex">
                        {/* Image Uploader Component */}
                        <FormField
                          control={form.control}
                          name="imageData"
                          render={() => (
                            <FormItem>
                              <ImageUploader
                                previewSize="lg"
                                onImageChange={(imageData) =>
                                  form.setValue("imageData", imageData)
                                }
                                initialImage={form.watch("imageData")}
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* Settings Popover */}
                        <Popover>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <PopoverTrigger asChild>
                                <Button variant="outline" size="icon">
                                  <WrenchIcon className="h-4 w-4" />
                                </Button>
                              </PopoverTrigger>
                            </TooltipTrigger>
                            <TooltipContent side="bottom">
                              Options
                            </TooltipContent>
                          </Tooltip>

                          <PopoverContent className="w-80" align="start">
                            <div className="space-y-4">
                              {/* Format options - RadioGroup */}
                              <FormField
                                control={form.control}
                                name="format"
                                render={({ field }) => (
                                  <FormItem className="space-y-2">
                                    <FormLabel>Tailwind Version</FormLabel>
                                    <FormControl>
                                      <RadioGroup
                                        onValueChange={field.onChange}
                                        value={field.value}
                                        className="flex flex-col space-y-1"
                                      >
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem
                                            value="hsl"
                                            id="hsl-format"
                                          />
                                          <Label
                                            htmlFor="hsl-format"
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                          >
                                            V3 (HSL Format)
                                          </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem
                                            value="oklch"
                                            id="oklch-format"
                                          />
                                          <Label
                                            htmlFor="oklch-format"
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                          >
                                            V4 (OKLCH Format)
                                          </Label>
                                        </div>
                                      </RadioGroup>
                                    </FormControl>
                                  </FormItem>
                                )}
                              />
                              <Separator />
                              <FormField
                                control={form.control}
                                name="borderRadius"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Border Radius</FormLabel>
                                    <div className="flex items-center gap-2 mt-2">
                                      {["0", "0.3", "0.5", "0.75", "1"].map(
                                        (radius) => (
                                          <Button
                                            key={radius}
                                            type="button"
                                            variant={
                                              field.value === radius
                                                ? "default"
                                                : "outline"
                                            }
                                            size="sm"
                                            className={cn("flex-1")}
                                            style={{
                                              borderRadius: `${radius}rem`,
                                            }}
                                            onClick={() =>
                                              field.onChange(radius)
                                            }
                                          >
                                            {radius}
                                          </Button>
                                        )
                                      )}
                                    </div>
                                    <FormDescription className="text-xs mt-2">
                                      Controls the roundness of UI elements in
                                      the generated theme
                                    </FormDescription>
                                  </FormItem>
                                )}
                              />
                              <Separator />
                              {/* Toggle switches */}
                              <FormField
                                control={form.control}
                                name="includeDarkMode"
                                render={({ field }) => (
                                  <div className="flex items-center justify-between">
                                    <label
                                      htmlFor="dark-mode"
                                      className="text-sm"
                                    >
                                      Include Dark Mode
                                    </label>
                                    <Switch
                                      id="dark-mode"
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </div>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="includeSidebar"
                                render={({ field }) => (
                                  <div className="flex items-center justify-between">
                                    <label
                                      htmlFor="sidebar"
                                      className="text-sm"
                                    >
                                      Include Sidebar
                                    </label>
                                    <Switch
                                      id="sidebar"
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </div>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="includeChart"
                                render={({ field }) => (
                                  <div className="flex items-center justify-between">
                                    <label htmlFor="chart" className="text-sm">
                                      Include Chart
                                    </label>
                                    <Switch
                                      id="chart"
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </div>
                                )}
                              />
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </motion.div>
                    {/* Right side - Model select and Submit button */}
                    <div className="flex items-center gap-2">
                      {/* Submit button */}
                      <Button
                        type="submit"
                        variant="default"
                        size="icon"
                        disabled={isSubmitDisabled || isPending}
                        aria-label="Create theme"
                      >
                        {isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <ArrowUp className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </>
  );
}

export default CreateThemeForm;
