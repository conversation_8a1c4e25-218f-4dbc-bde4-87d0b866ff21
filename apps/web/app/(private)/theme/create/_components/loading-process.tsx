"use client";

import { useEffect, useState } from "react";

import { cn } from "@chromify/ui/lib/utils";

interface LoadingProcessProps {
  totalSteps: number;
  simulationSpeed?: number;
  className?: string;
}

export function LoadingProcess({
  totalSteps,
  simulationSpeed = 1000,
  className,
}: LoadingProcessProps) {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [isActive, setIsActive] = useState<boolean>(false);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isActive && currentStep < totalSteps) {
      interval = setInterval(() => {
        setCurrentStep((prev) => {
          const nextStep = prev + 1;
          if (nextStep >= totalSteps) {
            setIsActive(false);
          }
          return nextStep;
        });
      }, simulationSpeed);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, currentStep, totalSteps, simulationSpeed]);

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) return "completed";
    if (stepIndex === currentStep && isActive) return "current";
    return "pending";
  };

  // Steps for theme generation (example)
  const steps = [
    "Analyzing inputs",
    "Extracting color palette",
    "Generating theme variations",
    "Optimizing contrast ratios",
    "Finalizing theme",
  ].slice(0, totalSteps);

  // Only show while active
  if (!isActive && currentStep === 0) return null;

  return (
    <div className={cn("mb-6", className)}>
      <div className="space-y-4">
        <div className="flex justify-between">
          <h3 className="text-sm font-medium">Processing</h3>
          <span className="text-xs text-muted-foreground">
            Step {currentStep + 1} of {totalSteps}
          </span>
        </div>

        <div className="space-y-2">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className={cn(
                  "h-2 w-2 rounded-full",
                  getStepStatus(index) === "completed" && "bg-primary",
                  getStepStatus(index) === "current" &&
                    "bg-primary animate-pulse",
                  getStepStatus(index) === "pending" && "bg-muted"
                )}
              />
              <span
                className={cn(
                  "text-xs",
                  getStepStatus(index) === "completed" && "text-primary",
                  getStepStatus(index) === "current" && "font-medium",
                  getStepStatus(index) === "pending" && "text-muted-foreground"
                )}
              >
                {step}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
