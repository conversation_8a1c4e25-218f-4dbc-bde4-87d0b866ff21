"use client";

import React, { useEffect, useRef } from "react";

const ContentEditable = React.forwardRef<
  HTMLDivElement,
  {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
  } & Omit<React.HTMLAttributes<HTMLDivElement>, "onChange">
>((props, ref) => {
  const { value, onChange, placeholder, className, ...rest } = props;
  const internalRef = useRef<HTMLDivElement>(null);

  // Update the div content when the value prop changes
  useEffect(() => {
    const element =
      ref && typeof ref !== "function" ? ref.current : internalRef.current;

    if (element && element.innerHTML !== value) {
      element.innerHTML = value;
    }
  }, [value, ref]);

  return (
    <div
      ref={ref || internalRef}
      contentEditable={true}
      className={`min-h-[100px] focus:outline-none ${className || ""}`}
      onInput={(e) => {
        const target = e.target as HTMLDivElement;
        onChange(target.innerHTML);
      }}
      onBlur={(e) => {
        const target = e.target as HTMLDivElement;
        onChange(target.innerHTML);
      }}
      data-placeholder={placeholder}
      {...rest}
    />
  );
});

export default ContentEditable;
