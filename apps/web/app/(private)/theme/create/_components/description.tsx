const Description = () => {
  return (
    <div className="mx-auto sm:max-w-screen-md relative h-[500px]">
      <div className="space-y-4 relative h-full text-center">
        <h1 className="font-bold text-4xl text-transparent bg-clip-text bg-size-200 animate-gradient-x relative">
          <span
            style={{
              backgroundImage:
                "linear-gradient(120deg, var(--primary), hsl(146, 55%, 65%), hsl(156, 60%, 45%))",
            }}
            className="bg-clip-text text-transparent"
          >
            Craft Your{" "}
          </span>
          {/* Special treatment for "Color" */}
          <span
            className="inline-block text-transparent bg-clip-text font-extrabold"
            style={{
              backgroundImage:
                "linear-gradient(90deg, hsl(330, 70%, 60%), var(--primary), hsl(48, 70%, 50%))",
              textShadow: "0 0 12px var(--primary) / 0.3",
              animation: "none", // Override the animation for this word only
            }}
          >
            Color
          </span>{" "}
          <span
            style={{
              backgroundImage:
                "linear-gradient(240deg, var(--primary), hsl(146, 55%, 65%), hsl(156, 60%, 45%))",
            }}
            className="bg-clip-text text-transparent"
          >
            Story
          </span>
        </h1>

        <p className="text-muted-foreground mb-6 max-w-md text-center mx-auto">
          The more details you provide, the better your results.
          <br /> Include brand colors, mood, and any specific requirements.
        </p>
      </div>
      <div
        className="absolute top-12 left-1/2 transform -translate-x-1/2 -translate-y-1/2 size-80 rounded-full"
        style={{
          background:
            "radial-gradient(circle, rgba(105, 229, 174, 0.2) 0%, rgba(40, 163, 112, 0.1) 40%, rgba(255, 255, 255, 0) 70%)",
          filter: "blur(15px)",
        }}
      />
    </div>
  );
};

export default Description;
