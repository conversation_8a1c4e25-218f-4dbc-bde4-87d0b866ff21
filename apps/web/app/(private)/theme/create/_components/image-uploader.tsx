"use client";

import { useRef, useState } from "react";
import { Maximize2, Upload, X } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { Dialog, DialogContent } from "@chromify/ui/components/dialog";

interface ImageUploaderProps {
  value?: string;
  onChange: (value: string | undefined) => void;
}

export function ImageUploader({ value, onChange }: ImageUploaderProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(
    value || null
  );
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert("File size exceeds 5MB limit");
      return;
    }

    const reader = new FileReader();
    reader.onloadend = () => {
      const base64data = reader.result as string;
      setImagePreview(base64data);
      onChange(base64data);
    };
    reader.readAsDataURL(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const clearImage = () => {
    setImagePreview(null);
    onChange(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openImageDialog = () => {
    setDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleImageUpload}
            accept="image/*"
            className="hidden"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={triggerFileInput}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            Add image for inspiration
          </Button>
          <span className="text-xs ml-2 text-muted-foreground">
            JPG, PNG, WebP up to 5MB
          </span>
        </div>
      </div>

      {imagePreview && (
        <div className="flex items-start gap-2">
          <div className="relative border rounded-md overflow-hidden">
            <div
              className="relative cursor-pointer w-24 h-24"
              onClick={openImageDialog}
            >
              <img
                src={imagePreview}
                alt="Uploaded preview"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/0 hover:bg-black/20 flex items-center justify-center transition-colors">
                <Maximize2 className="h-4 w-4 opacity-0 hover:opacity-100" />
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={clearImage}
              className="absolute top-1 right-1 h-6 w-6 rounded-full bg-background/80 hover:bg-background"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-xs text-muted-foreground pt-2">
            We&apos;ll use colors from this image to inspire your theme
          </p>
        </div>
      )}

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-3xl">
          <div className="relative w-full">
            <img
              src={imagePreview || ""}
              alt="Uploaded image"
              className="w-full max-h-[70vh] object-contain rounded-lg"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
