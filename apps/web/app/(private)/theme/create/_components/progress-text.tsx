"use client";

import { useEffect, useState } from "react";
import { AnimatePresence, cubicBezier, motion } from "framer-motion";

const ProgressAnimatedText = ({
  isComplete,
  progress,
  lastUpdatedField,
  isLoading,
}: {
  isComplete: boolean;
  progress: {
    overall: number;
    metadata: number;
    colors: number;
    dark_colors: number;
  };
  lastUpdatedField: string | null;
  isLoading: boolean;
}) => {
  // Default message when not loading
  const [message, setMessage] = useState("Ready to generate your theme");

  // Update messages based on lastUpdatedField and progress state
  useEffect(() => {
    if (isComplete) {
      setMessage("Your theme is ready to use!");
      return;
    }

    if (!isLoading) {
      setMessage("Ready to generate your theme");
      return;
    }

    if (lastUpdatedField) {
      // Map different field patterns to friendly messages
      if (lastUpdatedField.startsWith("metadata")) {
        setMessage(`Generating theme metadata`);
      } else if (lastUpdatedField === "analysis") {
        setMessage("Analyzing color relationships");
      } else if (lastUpdatedField.startsWith("colors")) {
        setMessage(`Creating color palette`);
      } else if (lastUpdatedField.startsWith("dark_colors")) {
        setMessage(`Designing dark mode colors`);
      } else if (lastUpdatedField === "description") {
        setMessage("Crafting theme description");
      } else {
        setMessage(`Generating theme`);
      }
    } else {
      // Generic message when loading without a specific field
      setMessage(`Generating theme...`);
    }
  }, [isLoading, lastUpdatedField, progress, isComplete]);

  // Smooth custom easing function for opacity
  const smoothFade = cubicBezier(0.4, 0, 0.6, 1);

  const textVariants = {
    hidden: {
      opacity: 0,
    },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: smoothFade,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.5,
        ease: smoothFade,
      },
    },
  };

  return (
    <div className="w-full max-w-72">
      <div className="relative flex items-center justify-end w-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={message} // Use message as key to trigger animation when message changes
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={textVariants}
            className="absolute w-full text-center"
          >
            <span
              className="animate-shimmer inline-block text-transparent"
              style={{
                backgroundClip: "text",
                backgroundImage:
                  "linear-gradient(-35deg, var(--foreground) 0%, var(--accent) 50%, var(--foreground) 100%)",
                backgroundSize: "200% 150%",
                backgroundPosition: "-100% 0",
              }}
            >
              {message}
            </span>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ProgressAnimatedText;
