import { Suspense } from "react";
import type { Metadata } from "next";

import Description from "./_components/description";
import CreateThemeForm from "./_components/theme-form";

export const metadata: Metadata = {
  title: "Create New Theme - AI-Powered Color Generator",
  description:
    "Generate beautiful, accessible color themes for your web projects using AI. Customize your theme with dark mode, sidebar colors, and more.",
};

const Page: React.FC = async () => {
  return (
    <div className="overflow-hidden relative h-screen">
      <div className="flex items-center justify-center translate-y-1/2">
        <Description />
      </div>
      <div className="max-w-2xl mx-auto w-full absolute bottom-12 inset-x-0">
        <Suspense fallback={<div>Loading...</div>}>
          <CreateThemeForm />
        </Suspense>
      </div>
    </div>
  );
};

export default Page;
