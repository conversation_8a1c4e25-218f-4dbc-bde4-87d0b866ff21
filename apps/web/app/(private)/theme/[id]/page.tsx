import type { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";

import { getTheme, getThemeIdByRequest } from "@/services/theme";
import type { DBTheme } from "@/types/theme";
import ThemeDisplay from "./_components/display";
import ThemeDisplayBreadcrumb from "./_components/display/display-breadcrumb";
import ErrorState from "./_components/error-state";
import ThemeStreaming from "./_components/streaming";

type Params = Promise<{
  id: string;
}>;

export async function generateMetadata({
  params,
}: {
  params: Params;
}): Promise<Metadata> {
  const { id } = await params;

  // Fetch theme data from database based on the ID in the URL
  const { requestData, requestError } = await getThemeIdByRequest(id);

  // Handle not found case
  if (requestError || !requestData) {
    return {
      title: "Theme Not Found",
      description:
        "The theme you're looking for doesn't exist or has been removed.",
    };
  }

  if (requestData.status === "pending") {
    return {
      title: "Generating Theme - Please Wait",
      description:
        "Your AI-powered color theme is currently being generated. Please wait a moment.",
    };
  }

  if (requestData.status === "failed") {
    return {
      title: "Theme Generation Failed",
      description:
        "There was an error generating your color theme. Please try again with a different prompt.",
    };
  }

  const { themeData, themeError } = await getTheme(id);

  if (themeError || !themeData) {
    return {
      title: "Theme Not Found",
      description:
        "The theme you're looking for doesn't exist or has been removed.",
    };
  }

  return {
    title: `${themeData.name} - Theme Details`,
    description:
      themeData.theme_description ||
      `A custom color theme with ${themeData.format.toUpperCase()} colors for your web projects.`,
  };
}

async function ThemePage({ params }: { params: Params }) {
  const { id } = await params;

  // Fetch theme data from database based on the ID in the URL
  const { requestData, requestError } = await getThemeIdByRequest(id);

  // Handle not found case
  if (requestError || !requestData) {
    return notFound();
  }

  if (requestData.status === "pending") {
    return (
      <ThemeStreaming
        id={requestData.id}
        themeGenerationRequest={{
          description: requestData.user_prompt,
          format: requestData.format,
          includeDarkMode: requestData.include_dark_mode,
          includeSidebar: requestData.include_sidebar,
          includeChart: requestData.include_chart,
          imageData: requestData.image_path,
          model: requestData.model,
          borderRadius: requestData.border_radius || "0.5",
        }}
      />
    );
  }
  if (requestData.status === "failed") {
    return (
      <ErrorState
        themeId={id}
        requestId={id}
        errorMessage={requestData.error_message || undefined}
        themeRequest={{
          description: requestData.user_prompt,
          format: requestData.format,
          includeDarkMode: requestData.include_dark_mode,
          includeSidebar: requestData.include_sidebar,
          includeChart: requestData.include_chart,
          model: requestData.model || "deepseek-chat",
          borderRadius: requestData.border_radius || "0.5",
        }}
      />
    );
  }

  const { themeData, themeError } = await getTheme(id);

  if (themeError || !themeData) {
    return notFound();
  }
  return (
    <div className="-mt-6">
      <ThemeDisplayBreadcrumb
        themeId={themeData.id}
        isPublic={themeData.is_public}
        shareId={themeData.shared_id || ""}
        description={themeData.name}
        isFavorite={themeData.is_favorite}
      />

      <ThemeDisplay theme={themeData as unknown as DBTheme} />
    </div>
  );
}

export default ThemePage;
