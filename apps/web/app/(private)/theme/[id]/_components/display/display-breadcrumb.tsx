import Link from "next/link";

import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@chromify/ui/components/breadcrumb";
import FavoriteButton from "@/components/theme/favi-button/default";
import MakePublicButton from "@/components/theme/publish-button/default";

interface ThemeDisplayBreadcrumbProps {
  themeId: string;
  isPublic: boolean;
  shareId?: string | null;
  description: string;
  isFavorite: boolean;
}

function ThemeDisplayBreadcrumb({
  description,
  isPublic,
  themeId,
  shareId,
  isFavorite,
}: ThemeDisplayBreadcrumbProps) {
  return (
    <div className="flex items-center justify-between h-12">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/theme/create">New Theme</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>{description}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex items-center gap-2">
        <MakePublicButton
          themeId={themeId}
          isPublic={isPublic || false}
          shareId={shareId}
        />
        <FavoriteButton themeId={themeId} isFavorite={isFavorite} />
      </div>
    </div>
  );
}

export default ThemeDisplayBreadcrumb;
