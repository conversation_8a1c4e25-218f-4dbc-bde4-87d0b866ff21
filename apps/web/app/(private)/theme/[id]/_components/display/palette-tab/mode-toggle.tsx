"use client";

import { <PERSON>, Sun } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Switch } from "@chromify/ui/components/switch";
import { Toggle } from "@chromify/ui/components/toggle";
import { AIThemeMode } from "@/schema/theme";

interface ThemeModeToggleProps {
  activeMode: AIThemeMode;
  setActiveMode: (mode: AIThemeMode) => void;
  hasDarkMode: boolean;
}

export function ThemeModeToggle({
  activeMode,
  setActiveMode,
  hasDarkMode,
}: ThemeModeToggleProps) {
  return (
    <div className="flex items-center justify-end space-x-2">
      <Button
        size="icon"
        onClick={() => setActiveMode("light")}
        disabled={activeMode === "light"}
      >
        <Sun className="h-4 w-4" />
      </Button>
      {hasDarkMode && (
        <Button
          size="icon"
          onClick={() => setActiveMode("dark")}
          disabled={activeMode === "dark" || !hasDarkMode}
        >
          <Moon className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}

export function ThemeModeToggleSwitch({
  activeMode,
  setActiveMode,
  hasDarkMode,
}: ThemeModeToggleProps) {
  if (!hasDarkMode) return null;

  return (
    <div className="flex items-center md:justify-end space-x-2">
      <Sun className="h-4 w-4" />
      <Switch
        checked={activeMode === "dark"}
        onCheckedChange={(checked) => setActiveMode(checked ? "dark" : "light")}
        disabled={!hasDarkMode}
        aria-label="Toggle dark mode"
      />
      <Moon className="h-4 w-4" />
    </div>
  );
}

export function ThemeModeToggleSegmented({
  activeMode,
  setActiveMode,
  hasDarkMode,
}: ThemeModeToggleProps) {
  return (
    <div className="flex items-center space-x-1">
      <Toggle
        pressed={activeMode === "light"}
        onPressedChange={() => setActiveMode("light")}
        aria-label="Toggle light mode"
        size="sm"
      >
        <Sun className="h-4 w-4" />
      </Toggle>
      {hasDarkMode && (
        <Toggle
          pressed={activeMode === "dark"}
          onPressedChange={() => setActiveMode("dark")}
          aria-label="Toggle dark mode"
          size="sm"
          disabled={!hasDarkMode}
        >
          <Moon className="h-4 w-4" />
        </Toggle>
      )}
    </div>
  );
}
