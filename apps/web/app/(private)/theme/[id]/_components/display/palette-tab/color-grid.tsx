import { AIColor, AIColorFormat } from "@/schema/theme";

interface ColorGridProps {
  colors: AIColor;
  format: AIColorFormat;
}

function ColorGrid({ colors, format }: ColorGridProps) {
  // Define color groups with functional organization
  const groups = [
    {
      id: "global-base",
      title: "Global Foundation",
      description:
        "Core colors that establish the theme's overall visual foundation",
      colors: [
        "background",
        "foreground",
        "muted",
        "muted-foreground",
        "border",
      ],
    },
    {
      id: "content-containers",
      title: "Content Containers",
      description:
        "Colors for cards, popovers, modals, and other content containers",
      colors: ["card", "card-foreground", "popover", "popover-foreground"],
    },
    {
      id: "interactive-elements",
      title: "Interactive Elements",
      description:
        "Colors for buttons, controls, and other interactive components",
      colors: [
        "primary",
        "primary-foreground",
        "secondary",
        "secondary-foreground",
        "accent",
        "accent-foreground",
        "destructive",
        "destructive-foreground",
      ],
    },
    {
      id: "form-elements",
      title: "Form Controls",
      description: "Colors for form elements, inputs, and interactive controls",
      colors: ["input", "ring"],
    },
    {
      id: "navigation",
      title: "Navigation & Layout",
      description:
        "Colors specific to navigation components like sidebars and menus",
      colors: colors
        ? Object.keys(colors).filter((key) => key.startsWith("sidebar"))
        : [],
    },
    {
      id: "data-visualization",
      title: "Data Visualization",
      description:
        "Color series designed for charts, graphs, and data representation",
      colors: colors
        ? Object.keys(colors).filter((key) => key.startsWith("chart"))
        : [],
    },
  ];

  return (
    <div className="space-y-12">
      {groups.map((group) => {
        // Filter to only include colors that exist in the theme
        const availableColors = group.colors.filter(
          (key) => colors && key in colors
        );

        // Only render the group if it has available colors
        if (availableColors.length === 0) return null;

        return (
          <ColorSection
            key={group.id}
            title={group.title}
            description={group.description}
            colors={availableColors}
            colorData={colors}
            colorFormat={format}
          />
        );
      })}
    </div>
  );
}
export default ColorGrid;

// ColorSection component - updated to use direct color values instead of CSS variables
function ColorSection({
  title,
  description,
  colors,
  colorData,
  colorFormat,
}: {
  title: string;
  description: string;
  colors: string[];
  colorData: Record<string, string> | undefined;
  colorFormat: string;
}) {
  // Protect against missing data
  if (!colorData) return null;

  // Map v3 variable names to v4 for display purposes
  const getDisplayName = (key: string) => {
    // Variable name mapping from v3 to v4
    const variableNameMap: Record<string, string> = {
      "sidebar-background": "sidebar",
      // Add any other variable name changes here
    };

    // Only apply mapping if we're in v4 mode
    if (colorFormat === "v4" && variableNameMap[key]) {
      return variableNameMap[key];
    }

    return key;
  };

  return (
    <div className="mt-4">
      <div className="mb-6">
        <h2 className="text-xl md:text-2xl font-bold">{title}</h2>
        <p className="text-muted-foreground">{description}</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {colors.map((key) => {
          // Skip if the color value doesn't exist
          if (!colorData[key]) return null;

          // Get the display name (might be transformed in v4)
          const displayName = getDisplayName(key);
          const colorValue = colorData[key];

          // Handle non-color values like border radius
          if (key === "radius") {
            return (
              <div
                key={key}
                className="group relative overflow-hidden rounded-xl border bg-card transition-all hover:shadow-md"
              >
                {/* <div className="h-24 w-full flex items-end p-3 border-b border-border">
                  <div className="font-mono text-xs opacity-80">
                    {colorValue}
                  </div>
                </div> */}
                <div className="p-3">
                  <div>{displayName}</div>
                </div>
              </div>
            );
          }

          return (
            <div
              key={key}
              className="group relative overflow-hidden rounded-xl border transition-all hover:shadow-md"
            >
              <div
                className="h-24 w-full border-b border-border"
                style={{ backgroundColor: colorValue }}
              />
              <div className="p-3 space-y-1.5">
                <div className="font-medium">{displayName}</div>
                <div className="font-mono text-xs opacity-80">{colorValue}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
