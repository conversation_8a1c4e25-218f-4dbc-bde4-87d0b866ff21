import { motion } from "framer-motion";

import { ThemeState } from "../types";

interface ReportTabProps {
  state: ThemeState;
}

/**
 * Processes report points to remove repetitive prefixes
 * Handles cases like:
 * "The primary color is"
 * "The primary color is a serene blue,"
 * "The primary color is a serene blue, chosen for its calming effect and reliability"
 * Where each line builds on the previous one
 */
function processReportPoints(points: string[]): string[] {
  if (!points.length) return points;

  // Simple direct approach to handle the specific pattern
  const result: string[] = [];
  const added = new Set<string>();

  // First pass: identify and mark points that are prefixes of other points
  const isPrefixOfAnother = new Set<string>();

  for (let i = 0; i < points.length; i++) {
    const current = points[i];
    if (!current) continue;

    for (let j = 0; j < points.length; j++) {
      if (i === j) continue; // Skip comparing with self

      const other = points[j];
      if (!other) continue;

      // If current is a prefix of other and they're not identical
      if (other.startsWith(current) && current !== other) {
        isPrefixOfAnother.add(current);
        break;
      }
    }
  }

  // Second pass: add only points that aren't prefixes of other points
  for (const point of points) {
    if (!point || added.has(point)) continue;

    // Skip if this point is a prefix of another point
    if (isPrefixOfAnother.has(point)) continue;

    result.push(point);
    added.add(point);
  }

  return result;
}

function ReportTab({ state }: ReportTabProps) {
  const rawReportPoints = state.displayTheme.analysis || [];
  const reportPoints = processReportPoints(rawReportPoints);

  return (
    <motion.ul
      className="space-y-4 my-6 list-disc list-inside"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {reportPoints.map((point, index) => (
        <motion.li
          key={index}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: index * 0.1 }}
        >
          {point}
        </motion.li>
      ))}
    </motion.ul>
  );
}

export default ReportTab;
