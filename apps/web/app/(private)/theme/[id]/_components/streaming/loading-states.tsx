import React from "react";
import { motion } from "framer-motion";

import ThemeSkeleton from "../loading-skeleton";

export const InitialLoading = () => {
  return (
    <div className="container mx-auto">
      <ThemeSkeleton description="Loading..." showText={false} />
    </div>
  );
};

export const ContentLoading = ({ message }: { message: string }) => {
  return (
    <motion.div
      className="p-4 border rounded-md"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        {message}
      </motion.div>
    </motion.div>
  );
};
