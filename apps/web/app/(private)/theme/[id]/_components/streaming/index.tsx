"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { experimental_useObject as useObject } from "@ai-sdk/react";
import { toast } from "sonner";

import { AITheme, ThemeGenerationRequest, ThemeSchema } from "@/schema/theme";
import ThemeSkeleton from "../loading-skeleton";
import StreamingPreview from "./streaming-preview";

// Helper function to wait for a specified time
const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const ThemeStreaming = ({
  id,
  themeGenerationRequest,
}: {
  id: string;
  themeGenerationRequest: ThemeGenerationRequest;
}) => {
  const router = useRouter();
  // Track if we've shown the initial skeleton
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  // Track if the streaming is complete from the API perspective

  const {
    object,
    submit,
    stop,
    error: streamingError,
    isLoading,
  } = useObject<AITheme>({
    api: `/api/theme/${id}/streaming`,
    schema: ThemeSchema,
    onError: (error) => {
      console.error("Streaming error:", error);
      toast.error(
        `Theme generation failed: ${error.message || "Unknown error"}`
      );

      // Add a short delay and then refresh the page to show the error state
      setTimeout(() => {
        router.refresh();
      }, 1500); // Delay to allow the user to see the toast message
    },
    onFinish: async (data: { object?: AITheme; error?: string | unknown }) => {
      // Check for schema validation errors
      if ("error" in data && data.error) {
        const errorMessage =
          typeof data.error === "string"
            ? data.error
            : "Schema validation failed";
        toast.error(`Theme generation failed: ${errorMessage}`);
        return;
      }

      if (data.object && !data.error) {
        toast.success("Theme generated successfully");

        // Short delay to ensure database operations complete
        await wait(300);

        router.refresh();
      }
    },
  });

  // Effect to handle streaming errors
  useEffect(() => {
    if (streamingError) {
      console.error("Streaming error detected:", streamingError);

      // Dispatch the error to the StreamingPreview component
      // We'll need to pass this information to the StreamingPreview component
      toast.error(
        `Theme generation failed: ${streamingError.message || "Unknown error"}`
      );
    }
  }, [streamingError]);

  // Submit the request when component mounts
  useEffect(() => {
    // Start the request immediately
    submit({
      ...themeGenerationRequest,
      id: id,
    });
    setInitialLoadComplete(true);
    // Set a short timeout to transition from skeleton to streaming UI
    // This ensures the skeleton is visible for at least a brief moment
    // const timer = setTimeout(() => {
    //   setInitialLoadComplete(true);
    // }, 800); // Short delay for visual continuity

    // return () => clearTimeout(timer);
  }, [id]);

  // Show skeleton during initial load to match the loading.tsx skeleton
  if (!initialLoadComplete) {
    return (
      <div className="container mx-auto">
        <ThemeSkeleton
          description={
            themeGenerationRequest.description || "Generating your theme..."
          }
          showText={false}
        />
      </div>
    );
  }

  return (
    <StreamingPreview
      streamingTheme={(object as AITheme) || null}
      updateInterval={150}
      onStop={stop}
      initialDescription={themeGenerationRequest.description}
      isApiStreamingComplete={!isLoading}
      requestId={id}
      streamingError={streamingError}
    />
  );
};

export default ThemeStreaming;
