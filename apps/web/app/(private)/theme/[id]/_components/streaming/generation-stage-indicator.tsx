import React from "react";
import { motion } from "framer-motion";

import ShimmerText from "@/components/shimmer-text";
import { GenerationStage } from "./types";

interface GenerationStageIndicatorProps {
  currentStage: GenerationStage;
  themeName?: string; // Theme name to display when complete
}

const GenerationStageIndicator = ({
  currentStage,
  themeName,
}: GenerationStageIndicatorProps) => {
  const stageMessages = {
    initializing: "Initializing theme generation...",
    metadata: "Creating theme concept and structure...",
    colors: "Generating light mode color palette...",
    dark_colors: "Adapting colors for dark mode...",
    report: "Creating theme report...",
    complete: "Theme generation complete!",
    failed: "Theme generation failed",
  };

  // Define the order of stages for progress indication
  // const stageOrder: GenerationStage[] = [
  //   "initializing",
  //   "metadata",
  //   "lightColors",
  //   "dark_colors",
  //   "report",
  //   "complete",
  // ];

  // Calculate current progress
  // const currentIndex = stageOrder.indexOf(currentStage);
  // const progress = Math.max(
  //   0,
  //   Math.min(100, (currentIndex / (stageOrder.length - 1)) * 100)
  // );

  // Display theme name if complete and name is provided
  const isComplete = currentStage === "complete";
  const isFailed = currentStage === "failed";
  const displayText =
    isComplete && themeName ? themeName : stageMessages[currentStage];

  return (
    <div className="flex items-center space-x-3">
      {/* Circular progress indicator - only show when not complete */}
      {/* {!isComplete && (
        <div className="relative h-10 w-10 flex-shrink-0">

          <svg className="w-full h-full" viewBox="0 0 100 100">

            <circle
              className="text-muted stroke-current"
              strokeWidth="8"
              cx="50"
              cy="50"
              r="40"
              fill="transparent"
            />

            <motion.circle
              className="text-primary stroke-current"
              strokeWidth="8"
              strokeLinecap="round"
              cx="50"
              cy="50"
              r="40"
              fill="transparent"
              initial={{ strokeDasharray: 251.2, strokeDashoffset: 251.2 }}
              animate={{
                strokeDashoffset: 251.2 - (progress / 100) * 251.2,
              }}
              transition={{ duration: 0.5 }}
            />

            <text
              x="50"
              y="50"
              fontSize="18"
              textAnchor="middle"
              dominantBaseline="middle"
              className="fill-primary font-medium"
            >
              {Math.round(progress)}%
            </text>
          </svg>
        </div>
      )} */}

      {/* Stage message or theme name */}
      <motion.div
        className="flex items-center overflow-hidden"
        animate={{ opacity: 1 }}
        initial={{ opacity: 0.8 }}
        transition={{
          duration: 0.5,
          ease: [0.4, 0.0, 0.2, 1], // Use a smoother easing function
        }}
      >
        {/* Using a single ShimmerText component that doesn't re-mount */}
        <ShimmerText>
          {isComplete && themeName ? (
            <motion.span
              className="font-medium text-sm"
              key="complete"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4 }}
            >
              Your theme is ready to use!
            </motion.span>
          ) : isFailed ? (
            <motion.span
              className="font-medium text-sm text-destructive"
              key="failed"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4 }}
            >
              {displayText}
            </motion.span>
          ) : (
            <motion.span
              className="font-medium text-sm"
              key={currentStage} // Only re-animate when stage changes, not on every text change
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4 }}
            >
              {displayText}
            </motion.span>
          )}
        </ShimmerText>
      </motion.div>
    </div>
  );
};

export default GenerationStageIndicator;
