import { motion } from "framer-motion";

import { AIThemeMode } from "@/schema/theme";
import { ThemeState } from "../types";

interface ColorGridProps {
  state: ThemeState;
  mode: AIThemeMode;
}

export function ColorGrid({ state, mode }: ColorGridProps) {
  // Get the appropriate color set based on the selected mode
  const colorSet =
    mode === "light"
      ? state.displayTheme.colors
      : state.displayTheme.dark_colors;

  // Define color groups with functional organization
  const groups = [
    {
      id: "global-base",
      title: "Global Foundation",
      description:
        "Core colors that establish the theme's overall visual foundation",
      colors: [
        "background",
        "foreground",
        "muted",
        "muted-foreground",
        "border",
      ],
    },
    {
      id: "content-containers",
      title: "Content Containers",
      description:
        "Colors for cards, popovers, modals, and other content containers",
      colors: ["card", "card-foreground", "popover", "popover-foreground"],
    },
    {
      id: "interactive-elements",
      title: "Interactive Elements",
      description:
        "Colors for buttons, controls, and other interactive components",
      colors: [
        "primary",
        "primary-foreground",
        "secondary",
        "secondary-foreground",
        "accent",
        "accent-foreground",
        "destructive",
        "destructive-foreground",
      ],
    },
    {
      id: "form-elements",
      title: "Form Controls",
      description: "Colors for form elements, inputs, and interactive controls",
      colors: ["input", "ring"],
    },
    {
      id: "navigation",
      title: "Navigation & Layout",
      description:
        "Colors specific to navigation components like sidebars and menus",
      colors: colorSet
        ? Object.keys(colorSet).filter((key) => key.startsWith("sidebar"))
        : [],
    },
    {
      id: "data-visualization",
      title: "Data Visualization",
      description:
        "Color series designed for charts, graphs, and data representation",
      colors: colorSet
        ? Object.keys(colorSet).filter((key) => key.startsWith("chart"))
        : [],
    },
  ];

  return (
    <motion.div
      className="space-y-12"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {groups.map((group) => {
        // Filter to only include colors that exist in the theme
        const availableColors = group.colors.filter(
          (key) => colorSet && key in colorSet
        );

        // Only render the group if it has available colors
        if (availableColors.length === 0) return null;

        return (
          <ColorSection
            key={group.id}
            title={group.title}
            description={group.description}
            colors={availableColors}
            colorData={colorSet}
            colorFormat={state.displayTheme.format}
            index={groups.indexOf(group)}
          />
        );
      })}
    </motion.div>
  );
}

// ColorSection component - updated to use direct color values instead of CSS variables
function ColorSection({
  title,
  description,
  colors,
  colorData,
  colorFormat,
  index,
}: {
  title: string;
  description: string;
  colors: string[];
  colorData: Record<string, string> | undefined;
  colorFormat: string;
  index: number;
}) {
  // Protect against missing data
  if (!colorData) return null;

  // Map v3 variable names to v4 for display purposes
  const getDisplayName = (key: string) => {
    // Variable name mapping from v3 to v4
    const variableNameMap: Record<string, string> = {
      "sidebar-background": "sidebar",
      // Add any other variable name changes here
    };

    // Only apply mapping if we're in v4 mode
    if (colorFormat === "v4" && variableNameMap[key]) {
      return variableNameMap[key];
    }

    return key;
  };

  return (
    <motion.section
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <div className="space-y-1">
        <h3 className="text-lg font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {colors.map((key, colorIndex) => {
          // Skip if the color value doesn't exist
          if (!colorData[key]) return null;

          // Get the display name (might be transformed in v4)
          const displayName = getDisplayName(key);
          const colorValue = colorData[key];

          // Handle non-color values like border radius
          if (key === "radius") {
            return (
              <motion.div
                key={key}
                className="group relative overflow-hidden rounded-xl border bg-card transition-all hover:shadow-md"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.1 + colorIndex * 0.05,
                }}
              >
                <div className="h-24 w-full flex items-end p-3 border-b border-border">
                  <div className="font-mono text-xs opacity-80">
                    {colorValue}
                  </div>
                </div>
                <div className="p-3">
                  <div>{displayName}</div>
                </div>
              </motion.div>
            );
          }

          // Regular color swatch
          return (
            <motion.div
              key={key}
              className="group relative overflow-hidden rounded-xl border bg-card transition-all hover:shadow-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{
                duration: 0.3,
                delay: index * 0.1 + colorIndex * 0.05,
              }}
            >
              <div
                className="h-24 w-full border-b border-border"
                style={{ backgroundColor: colorValue }}
              />
              <div className="p-3 space-y-1.5">
                <div className="font-medium">{displayName}</div>
                <div className="font-mono text-xs opacity-80">{colorValue}</div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </motion.section>
  );
}
