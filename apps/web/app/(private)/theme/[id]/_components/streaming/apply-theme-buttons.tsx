"use client";

import { <PERSON><PERSON><PERSON>, SunI<PERSON> } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { useTheme } from "@/components/theme-provider";
import { useThemeStore } from "@/store/theme-store";
import type { DBTheme } from "@/types/theme";

type ApplyThemeButtonsProps = {
  theme: DBTheme;
  isLoading: boolean;
  hasDarkMode: boolean;
};

function ApplyThemeButtons({
  theme,
  isLoading,
  hasDarkMode,
}: ApplyThemeButtonsProps) {
  const { setTheme, setMode } = useTheme();
  const { addCustomTheme } = useThemeStore();

  // Handle save of theme
  const handleApplyLightTheme = () => {
    // Create a minimal store theme with only essential data
    const storeTheme = {
      id: theme.id || `${theme.name}-${Date.now()}`, // Use ID from database or generate a unique one
      name: theme.name,
      format: theme.format,
      colors: theme.colors,
      dark_colors: theme.dark_colors,
    };

    // Add to custom themes
    addCustomTheme(storeTheme);

    // Apply the theme
    setTheme(storeTheme);
    setMode("light");
  };

  const handleApplyDarkTheme = () => {
    // Create a minimal store theme with only essential data
    const storeTheme = {
      id: theme.id || `${theme.name}-${Date.now()}`, // Use ID from database or generate a unique one
      name: theme.name,
      format: theme.format,
      colors: theme.colors,
      dark_colors: theme.dark_colors,
    };

    // Add to custom themes
    addCustomTheme(storeTheme);

    // Apply the theme
    setTheme(storeTheme);
    setMode("dark");
  };
  return (
    <div className="flex gap-2 mb-4">
      <Button
        aria-label="Apply light theme"
        size={"icon"}
        disabled={isLoading}
        onClick={handleApplyLightTheme}
      >
        <SunIcon />
      </Button>
      {hasDarkMode && (
        <Button
          aria-label="Apply dark theme"
          size={"icon"}
          disabled={isLoading}
          onClick={handleApplyDarkTheme}
        >
          <MoonIcon />
        </Button>
      )}
    </div>
  );
}

export default ApplyThemeButtons;
