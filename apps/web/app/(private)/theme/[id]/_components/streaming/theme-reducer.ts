import { AITheme } from "@/schema/theme";
import {
  THEME_ACTIONS,
  ThemeAction,
  ThemeColors,
  ThemeData,
  ThemeState,
} from "./types";

// Initial state for the theme reducer
export const initialThemeState: ThemeState = {
  // What we actually display to the user
  displayTheme: {
    name: "",
    description: "",
    format: "hsl", // Default to hsl format as per AITheme schema
    colors: {} as AITheme["colors"], // Cast to AITheme colors type
    dark_colors: {} as AITheme["dark_colors"], // Cast to AITheme dark_colors type
    analysis: [], // Will be renamed to report in schema, keeping for compatibility
    primaryHue: "teal", // Default to teal as per AITheme schema
  },

  // Buffer for collecting changes before updating display
  buffer: {
    name: "",
    description: "",
    format: "hsl", // Default to hsl format as per AITheme schema
    colors: {} as AITheme["colors"], // Cast to AITheme colors type
    dark_colors: {} as AITheme["dark_colors"], // Cast to AITheme dark_colors type
    analysis: [], // Will be renamed to report in schema, keeping for compatibility
    primaryHue: "teal", // Default to blue as per AITheme schema
  },

  // Tracking section completion for UI feedback
  completion: {
    basicInfo: false,
    colors: false,
    dark_colors: false,
    report: false, // Changed from analysis to report
  },

  // Stream status
  status: {
    isStreaming: false,
    isComplete: false,
    lastUpdateTime: null,
    currentStage: "initializing",
    displayStage: "initializing",
  },
};

// Reducer function for handling theme state updates
export const themeReducer = (
  state: ThemeState,
  action: ThemeAction
): ThemeState => {
  if (action.type === THEME_ACTIONS.STREAM_STARTED) {
    return {
      ...initialThemeState,
      status: {
        isStreaming: true,
        isComplete: false,
        lastUpdateTime: Date.now(),
        currentStage: "initializing",
        displayStage: "initializing",
      },
    };
  }

  if (action.type === THEME_ACTIONS.METADATA_RECEIVED) {
    if (!action.payload) return state;

    const payload = action.payload as {
      name?: string;
      description?: string;
      format?: "hsl" | "oklch";
    };

    return {
      ...state,
      buffer: {
        ...state.buffer,
        name: payload.name || state.buffer.name,
        description: payload.description || state.buffer.description,
        format: payload.format || state.buffer.format,
      },
      completion: {
        ...state.completion,
        basicInfo: !!(
          (payload.name || state.buffer.name) &&
          (payload.description || state.buffer.description)
        ),
      },
      status: {
        ...state.status,
        lastUpdateTime: Date.now(),
        currentStage: "metadata",
      },
    };
  }

  if (action.type === THEME_ACTIONS.LIGHT_COLORS_CHUNK) {
    if (!action.payload) return state;

    // Create a copy of the current colors
    const updatedLightColors = { ...state.buffer.colors } as Record<
      string,
      string
    >;

    // Add new color values
    Object.entries(action.payload as ThemeColors).forEach(([key, value]) => {
      updatedLightColors[key] = value;
    });

    return {
      ...state,
      buffer: {
        ...state.buffer,
        colors: updatedLightColors as unknown as AITheme["colors"],
      },
      completion: {
        ...state.completion,
        colors: Object.keys(updatedLightColors).length >= 5,
      },
      status: {
        ...state.status,
        lastUpdateTime: Date.now(),
        currentStage: "colors",
      },
    };
  }

  if (action.type === THEME_ACTIONS.DARK_COLORS_CHUNK) {
    if (!action.payload) return state;

    // Create a copy of the current dark colors
    const updatedDarkColors = { ...state.buffer.dark_colors } as Record<
      string,
      string
    >;

    // Add new color values
    Object.entries(action.payload as ThemeColors).forEach(([key, value]) => {
      updatedDarkColors[key] = value;
    });

    return {
      ...state,
      buffer: {
        ...state.buffer,
        dark_colors: updatedDarkColors as unknown as AITheme["colors"],
      },
      completion: {
        ...state.completion,
        dark_colors: Object.keys(updatedDarkColors).length >= 5,
      },
      status: {
        ...state.status,
        lastUpdateTime: Date.now(),
        currentStage: "dark_colors",
      },
    };
  }

  if (action.type === THEME_ACTIONS.REPORT_CHUNK) {
    if (!action.payload) return state;

    // Extract report points from payload with type safety
    const payload = action.payload as { reportPoints: string[] };
    const newReportPoints = payload.reportPoints || [];

    // For report, we use a Set to avoid duplicate entries
    const reportSet = new Set([
      ...state.buffer.analysis, // Still using analysis field for compatibility
      ...newReportPoints,
    ]);

    return {
      ...state,
      buffer: {
        ...state.buffer,
        analysis: [...reportSet], // Still using analysis field for compatibility
      },
      completion: {
        ...state.completion,
        report: reportSet.size > 0, // Updated to use report instead of analysis
      },
      status: {
        ...state.status,
        lastUpdateTime: Date.now(),
        currentStage: "report", // Updated from analysis to report
      },
    };
  }

  if (action.type === THEME_ACTIONS.BUFFER_TO_DISPLAY) {
    // Move buffered data to display with a clean merge
    return {
      ...state,
      displayTheme: {
        ...state.displayTheme,
        ...state.buffer,
        // Ensure name is transferred from buffer to display
        name: state.buffer.name || state.displayTheme.name,
      },
      status: {
        ...state.status,
        // Update the display stage from the current stage
        displayStage: state.status.currentStage,
      },
    };
  }

  if (action.type === THEME_ACTIONS.STREAM_COMPLETED) {
    // Create a final merged theme with all buffer data
    const finalTheme: ThemeData = {
      ...state.buffer,
      name: state.buffer.name || state.displayTheme.name || "Generated Theme",
    };

    return {
      ...state,
      displayTheme: finalTheme,
      buffer: finalTheme, // Sync buffer with display for consistency
      status: {
        isStreaming: false,
        isComplete: true,
        lastUpdateTime: Date.now(),
        currentStage: "complete",
        displayStage: "complete",
      },
    };
  }

  if (action.type === THEME_ACTIONS.STREAM_FAILED) {
    if (!action.payload) return state;

    // Extract error details with proper typing
    const payload = action.payload as {
      message: string;
      code?: string;
    };

    return {
      ...state,
      // Keep the current display state to show what was generated before the error
      status: {
        isStreaming: false,
        isComplete: false,
        lastUpdateTime: Date.now(),
        currentStage: "failed",
        displayStage: "failed",
        error: {
          message: payload.message || "An unknown error occurred",
          code: payload.code,
        },
      },
    };
  }

  if (action.type === THEME_ACTIONS.RESET) {
    return initialThemeState;
  }

  return state;
};
