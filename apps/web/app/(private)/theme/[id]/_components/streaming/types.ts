import { AITheme } from "@/schema/theme";

// Define action types
export const THEME_ACTIONS = {
  STREAM_STARTED: "STREAM_STARTED",
  METADATA_RECEIVED: "METADATA_RECEIVED",
  LIGHT_COLORS_CHUNK: "LIGHT_COLORS_CHUNK",
  DARK_COLORS_CHUNK: "DARK_COLORS_CHUNK",
  REPORT_CHUNK: "REPORT_CHUNK",
  BUFFER_TO_DISPLAY: "BUFFER_TO_DISPLAY",
  STREAM_COMPLETED: "STREAM_COMPLETED",
  STREAM_FAILED: "STREAM_FAILED",
  RESET: "RESET",
};

// We need a more flexible type for streaming chunks
export type ThemeColors = Record<string, string>;

// Use AITheme structure for ThemeData to ensure compatibility
export type ThemeData = AITheme;

export type GenerationStage =
  | "initializing"
  | "metadata"
  | "colors"
  | "dark_colors"
  | "report"
  | "complete"
  | "failed";

export interface ThemeState {
  displayTheme: ThemeData;
  buffer: ThemeData;
  completion: {
    basicInfo: boolean;
    colors: boolean;
    dark_colors: boolean;
    report: boolean;
  };
  status: {
    isStreaming: boolean;
    isComplete: boolean;
    lastUpdateTime: number | null;
    currentStage: GenerationStage; // Internal stage tracking (updated immediately)
    displayStage: GenerationStage; // Buffered stage for UI display (updated with BUFFER_TO_DISPLAY)
    error?: {
      message: string;
      code?: string;
    };
  };
}

export type ActionType = (typeof THEME_ACTIONS)[keyof typeof THEME_ACTIONS];

export interface ThemeAction {
  type: ActionType;
  payload?: Record<string, unknown>;
}

export interface ThemeReducerRendererProps {
  streamingTheme: AITheme | null; // Using any to accommodate the type from useObject
  updateInterval?: number;
  onStop?: () => void; // Function to stop the streaming process
  initialDescription?: string; // Initial description to show before streaming data arrives
  isApiStreamingComplete?: boolean; // Whether the API streaming is complete
  requestId?: string; // The ID of the theme request
  streamingError?: Error | null; // Error from the streaming process
}
