"use client";

import { useState } from "react";

import { AIThemeMode } from "@/schema/theme";
import { ThemeModeToggleSwitch } from "../../../../../../../components/theme/mode-toggle";
import { ContentLoading } from "../loading-states";
import { ThemeState } from "../types";
import { ColorGrid } from "./color-grid";

interface PaletteTabProps {
  state: ThemeState;
  mode?: AIThemeMode;
}

function PaletteTab({ state, mode = "light" }: PaletteTabProps) {
  // If no mode is provided, use the internal state with toggle
  const [activeMode, setActiveMode] = useState<AIThemeMode>(mode);

  // If mode is provided as a prop, use it directly without toggle
  const displayMode = mode || activeMode;

  // Only show the toggle when no mode is explicitly provided
  const showToggle = mode === undefined;

  return (
    <div className="space-y-6">
      {showToggle && (
        <ThemeModeToggleSwitch
          state={state}
          activeMode={activeMode}
          setActiveMode={setActiveMode}
        />
      )}

      {displayMode === "light" && (
        <>
          {state.completion.colors ? (
            <ColorGrid state={state} mode="light" />
          ) : null}
        </>
      )}

      {displayMode === "dark" && (
        <>
          {state.completion.dark_colors ? (
            <ColorGrid state={state} mode="dark" />
          ) : (
            <ContentLoading message="Generating dark theme colors..." />
          )}
        </>
      )}
    </div>
  );
}

export default PaletteTab;
