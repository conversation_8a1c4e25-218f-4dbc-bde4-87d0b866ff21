import React, { useEffect, useReducer, useRef, useState } from "react";
import { motion } from "framer-motion";
import _ from "lodash";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Skeleton } from "@chromify/ui/components/skeleton";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@chromify/ui/components/tabs";
import ThemeVariantIcons from "@/components/theme/theme-variant-icons";
import { AITheme } from "@/schema/theme";
import CssTab from "./css";
import { ContentLoading, InitialLoading } from "./loading-states";
import PaletteTab from "./palette";
import ReportTab from "./report";
import StreamingBreadcrumb from "./streaming-breadcrumb";
import { initialThemeState, themeReducer } from "./theme-reducer";
import { THEME_ACTIONS, ThemeColors, ThemeReducerRendererProps } from "./types";

const UpdatedStreamingPreview = ({
  streamingTheme,
  updateInterval = 200,
  initialDescription,
  onStop,
  isApiStreamingComplete = false,
  streamingError,
}: ThemeReducerRendererProps) => {
  // Initialize state with description if available
  const customInitialState = initialDescription
    ? {
        ...initialThemeState,
        buffer: {
          ...initialThemeState.buffer,
          description: initialDescription,
        },
        displayTheme: {
          ...initialThemeState.displayTheme,
          description: initialDescription,
        },
      }
    : initialThemeState;

  const [state, dispatch] = useReducer(themeReducer, customInitialState);
  const [activeTab, setActiveTab] = useState("colors");

  // Reference to track previous theme data for diffing
  const prevThemeRef = useRef<Partial<AITheme>>({});

  // Interval for controlled UI updates
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Process incoming theme data
  useEffect(() => {
    if (!streamingTheme) return;

    // Start the stream if this is the first data we're receiving
    if (!state.status.isStreaming && !state.status.isComplete) {
      dispatch({ type: THEME_ACTIONS.STREAM_STARTED });
    }

    // Process metadata (name, description, format)
    if (
      streamingTheme.name !== undefined &&
      (streamingTheme.name !== prevThemeRef.current.name ||
        streamingTheme.description !== prevThemeRef.current.description ||
        streamingTheme.format !== prevThemeRef.current.format)
    ) {
      dispatch({
        type: THEME_ACTIONS.METADATA_RECEIVED,
        payload: {
          name: streamingTheme.name,
          description: streamingTheme.description,
          format: streamingTheme.format,
        },
      });
    }

    // Process light colors
    if (
      streamingTheme.colors &&
      !_.isEqual(streamingTheme.colors, prevThemeRef.current.colors)
    ) {
      // Compute only the differences to minimize processing
      const colorsDiff: ThemeColors = {};
      Object.entries(streamingTheme.colors).forEach(([key, value]) => {
        if (
          prevThemeRef.current.colors?.[
            key as keyof typeof prevThemeRef.current.colors
          ] !== value
        ) {
          colorsDiff[key] = value as string;
        }
      });

      if (Object.keys(colorsDiff).length > 0) {
        dispatch({
          type: THEME_ACTIONS.LIGHT_COLORS_CHUNK,
          payload: colorsDiff,
        });
      }
    }

    // Process dark colors
    if (
      streamingTheme.dark_colors &&
      !_.isEqual(streamingTheme.dark_colors, prevThemeRef.current.dark_colors)
    ) {
      // Compute only the differences
      const dark_colorsDiff: ThemeColors = {};
      Object.entries(streamingTheme.dark_colors).forEach(([key, value]) => {
        if (
          prevThemeRef.current.dark_colors?.[
            key as keyof typeof prevThemeRef.current.dark_colors
          ] !== value
        ) {
          dark_colorsDiff[key] = value as string;
        }
      });

      if (Object.keys(dark_colorsDiff).length > 0) {
        dispatch({
          type: THEME_ACTIONS.DARK_COLORS_CHUNK,
          payload: dark_colorsDiff,
        });
      }
    }

    // Process report (previously analysis)
    if (
      streamingTheme.analysis &&
      !_.isEqual(streamingTheme.analysis, prevThemeRef.current.analysis)
    ) {
      // Find only new report points
      const currentAnalysisSet = new Set(prevThemeRef.current.analysis || []);
      const newReportPoints = (streamingTheme.analysis || []).filter(
        (point: string) => !currentAnalysisSet.has(point)
      );

      if (newReportPoints.length > 0) {
        dispatch({
          type: THEME_ACTIONS.REPORT_CHUNK,
          payload: { reportPoints: newReportPoints } as Record<string, unknown>,
        });
      }
    }

    // Update our reference
    prevThemeRef.current = { ...streamingTheme };

    // If we get a fully formed theme all at once, mark stream as complete
    const isComplete =
      streamingTheme.name &&
      streamingTheme.description &&
      streamingTheme.colors &&
      Object.keys(streamingTheme.colors).length > 10 &&
      streamingTheme.dark_colors &&
      Object.keys(streamingTheme.dark_colors).length > 10 &&
      streamingTheme.analysis &&
      streamingTheme.analysis.length >= 4;

    if (isComplete && state.status.isStreaming) {
      // Ensure the theme name is in the buffer before completing
      if (streamingTheme.name && !state.buffer.name) {
        dispatch({
          type: THEME_ACTIONS.METADATA_RECEIVED,
          payload: {
            name: streamingTheme.name,
            description: streamingTheme.description || state.buffer.description,
            format: streamingTheme.format || state.buffer.format,
          },
        });

        // Force a buffer to display update
        dispatch({ type: THEME_ACTIONS.BUFFER_TO_DISPLAY });
      }

      dispatch({ type: THEME_ACTIONS.STREAM_COMPLETED });
    }
  }, [streamingTheme, state.status.isStreaming, state.status.isComplete]);

  // Set up interval for controlled UI updates
  useEffect(() => {
    // Don't update if we're not streaming
    if (!state.status.isStreaming) return;

    // Clear existing interval
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
    }

    // Set up new interval
    updateIntervalRef.current = setInterval(() => {
      // If we've received updates since last display update,
      // update the display
      if (state.status.lastUpdateTime && state.status.lastUpdateTime > 0) {
        dispatch({ type: THEME_ACTIONS.BUFFER_TO_DISPLAY });
      }
    }, updateInterval);

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [state.status.isStreaming, state.status.lastUpdateTime, updateInterval]);

  // Handle streaming errors
  // This effect is triggered when streamingError is set from the parent component
  // It updates the local state to show an error UI with retry options
  useEffect(() => {
    if (streamingError && state.status.isStreaming) {
      // Dispatch error to the reducer to update the UI state
      dispatch({
        type: THEME_ACTIONS.STREAM_FAILED,
        payload: {
          message: streamingError.message || "An unknown error occurred",
          code: "GENERATION_FAILED",
        },
      });

      // Note: The parent component will refresh the page after a delay
      // which will show the server-rendered error state
    }
  }, [streamingError, state.status.isStreaming]);

  // Handle API streaming completion
  useEffect(() => {
    // If the API streaming is complete and we're still streaming in our state
    if (isApiStreamingComplete && state.status.isStreaming) {
      // Error handling is done through the streamingError prop

      // Ensure we have the latest data in the buffer
      if (streamingTheme) {
        // Process any final metadata
        if (streamingTheme.name) {
          dispatch({
            type: THEME_ACTIONS.METADATA_RECEIVED,
            payload: {
              name: streamingTheme.name,
              description:
                streamingTheme.description || state.buffer.description,
              format: streamingTheme.format || state.buffer.format,
            },
          });
        }

        // Force a buffer to display update
        dispatch({ type: THEME_ACTIONS.BUFFER_TO_DISPLAY });
      }

      // Mark the stream as complete in our reducer
      dispatch({ type: THEME_ACTIONS.STREAM_COMPLETED });
    }
  }, [isApiStreamingComplete, state.status.isStreaming, streamingTheme]);

  // Show appropriate loading state or error
  if (
    !state.status.isStreaming &&
    !state.status.isComplete &&
    state.status.displayStage !== "failed"
  ) {
    // Initial state before streaming starts
    return <InitialLoading />;
  }

  // Show error state if generation failed
  if (state.status.displayStage === "failed" && state.status.error) {
    return (
      <div className="container mx-auto">
        <div className="bg-destructive/10 border border-destructive rounded-lg p-6">
          <h2 className="text-xl font-bold text-destructive mb-4">
            Theme Generation Failed
          </h2>
          <p className="mb-4">{state.status.error.message}</p>
          <p className="text-sm text-muted-foreground mb-6">
            Error code: {state.status.error.code || "UNKNOWN"}
          </p>
          <div className="flex gap-4">
            <Button
              variant="destructive"
              onClick={() => {
                // Reset the state
                dispatch({ type: THEME_ACTIONS.RESET });
                // Stop the current streaming process if it's still running
                if (onStop) onStop();
                // Redirect to create a new theme
                window.location.href = "/theme/create";
              }}
            >
              Create New Theme
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                // Reload the page to retry
                window.location.reload();
              }}
            >
              Retry Generation
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Loading state while waiting for initial data
  if (state.status.isStreaming && !state.completion.basicInfo) {
    return <InitialLoading />;
  }

  // Determine if we have dark mode, charts, and sidebar
  const hasDarkMode =
    state.completion.dark_colors &&
    Object.keys(state.displayTheme.dark_colors || {}).length > 0;

  const hasCharts = Object.keys(state.displayTheme.colors || {}).some((key) =>
    key.startsWith("chart")
  );

  const hasSidebar = Object.keys(state.displayTheme.colors || {}).some((key) =>
    key.startsWith("sidebar")
  );

  return (
    <>
      <StreamingBreadcrumb
        description={state.displayTheme.description || "Generating theme..."}
        isLoading={state.status.isStreaming && !isApiStreamingComplete}
        cancelStreaming={onStop || (() => {})}
        currentStage={
          isApiStreamingComplete ? "complete" : state.status.displayStage
        }
        themeName={
          state.status.isComplete || isApiStreamingComplete
            ? state.displayTheme.name || "Generated Theme"
            : undefined
        }
      />

      <div className="w-full mx-auto space-y-6 container">
        {/* Theme title and description */}
        <div className="flex justify-between">
          <motion.h1
            className="text-2xl font-bold lg:text-4xl"
            key={state.displayTheme.name} // Re-animate when name changes
            initial={{ opacity: 0.6 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {state.displayTheme.name || <Skeleton className="h-8 w-40" />}
          </motion.h1>
        </div>

        <ThemeVariantIcons
          includeDarkMode={hasDarkMode}
          includeCharts={hasCharts}
          includeSidebar={hasSidebar}
        />

        <motion.div
          className="my-6 max-w-3xl"
          key={state.displayTheme.description} // Re-animate when description changes
          initial={{ opacity: 0.6 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          {state.displayTheme.description || (
            <div className="w-full space-y-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-6 w-1/2" />
            </div>
          )}
        </motion.div>

        {/* Tabs for colors, dark-colors, report, and code */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList>
            <TabsTrigger className="w-20 md:w-32" value="colors">
              Colors
            </TabsTrigger>
            {hasDarkMode && (
              <TabsTrigger className="w-26 md:w-32" value="dark-colors">
                Dark Colors
              </TabsTrigger>
            )}
            <TabsTrigger
              className="w-22 md:w-32"
              value="report"
              disabled={!state.completion.report}
            >
              Report
            </TabsTrigger>
            <TabsTrigger
              className="w-20 md:w-32"
              value="code"
              disabled={!state.completion.colors}
            >
              Code
            </TabsTrigger>
          </TabsList>

          <TabsContent value="colors" className="space-y-4">
            {state.completion.colors ? (
              <PaletteTab state={state} mode="light" />
            ) : (
              <div className="mt-10">
                <div className="mb-6">
                  <Skeleton className="h-8 w-40 mb-2" />
                  <Skeleton className="h-5 w-full max-w-lg" />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {Array(6)
                    .fill(0)
                    .map((_, i) => (
                      <div
                        key={i}
                        className="group relative overflow-hidden rounded-xl"
                      >
                        <Skeleton className="h-24 w-full" />
                      </div>
                    ))}
                </div>
              </div>
            )}
          </TabsContent>

          {hasDarkMode && (
            <TabsContent value="dark-colors" className="space-y-4">
              {state.completion.dark_colors ? (
                <PaletteTab state={state} mode="dark" />
              ) : (
                <ContentLoading message="Generating dark theme colors..." />
              )}
            </TabsContent>
          )}

          <TabsContent value="report" className="space-y-4">
            {state.completion.report ? (
              <ReportTab state={state} />
            ) : (
              <ContentLoading message="Generating theme report..." />
            )}
          </TabsContent>

          <TabsContent value="code" className="space-y-4">
            {state.completion.colors ? (
              <CssTab state={state} />
            ) : (
              <ContentLoading message="Generating CSS variables..." />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default UpdatedStreamingPreview;
