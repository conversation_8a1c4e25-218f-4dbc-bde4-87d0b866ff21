import { memo } from "react";
import { Loader2 } from "lucide-react";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@chromify/ui/components/breadcrumb";
import GenerationStageIndicator from "./generation-stage-indicator";
import { GenerationStage } from "./types";

interface StreamingBreadcrumbProps {
  description: string;
  isLoading: boolean;
  cancelStreaming?: () => void;
  currentStage: GenerationStage;
  themeName?: string; // Theme name to display when complete
}

/**
 * StreamingBreadcrumb component that displays the current generation stage
 * Memoized with a custom comparison function to prevent unnecessary re-renders
 */
const StreamingBreadcrumb = memo(
  ({ currentStage, isLoading, themeName }: StreamingBreadcrumbProps) => {
    return (
      <div className="flex items-center justify-between mb-6 h-12">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              Color Themes
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>
                <GenerationStageIndicator
                  currentStage={currentStage}
                  themeName={themeName}
                />
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {isLoading && (
          <div className="flex items-center gap-2">
            <Loader2 className="animate-spin size-3" />
          </div>
        )}
      </div>
    );
  }
);

export default StreamingBreadcrumb;
