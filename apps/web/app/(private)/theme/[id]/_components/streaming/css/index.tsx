import { useState } from "react";
import { motion } from "framer-motion";
import { Check, Copy } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { generateThemeCss } from "@/lib/utils";
import { ThemeState } from "../types";

interface CssTabProps {
  state: ThemeState;
}

function CssTab({ state }: CssTabProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopyClick = () => {
    navigator.clipboard.writeText(fullCss);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };
  const fullCss = generateThemeCss({
    format: state.buffer.format,
    colors: state.buffer.colors,
    dark_colors: state.buffer.dark_colors,
  });

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <pre className="bg-accent/20 p-4 rounded-md text-sm overflow-auto max-h-[520px]">
        <code>{fullCss}</code>
      </pre>

      <Button
        variant="ghost"
        size="icon"
        onClick={handleCopyClick}
        className="absolute right-2 top-2"
      >
        {isCopied ? (
          <Check className="h-4 w-4" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
    </motion.div>
  );
}

export default CssTab;
