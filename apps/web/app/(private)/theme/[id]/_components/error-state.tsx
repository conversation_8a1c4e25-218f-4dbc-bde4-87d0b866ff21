"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { Button } from "@chromify/ui/components/button";
import { regenerateTheme } from "@/lib/actions/theme";

interface ErrorStateProps {
  themeId?: string; // Optional, may not be used
  requestId: string; // This is the primary ID we'll use
  errorMessage?: string;
  themeRequest: {
    description: string;
    format: "hsl" | "oklch";
    includeDarkMode: boolean;
    includeSidebar: boolean;
    includeChart: boolean;
    model: "deepseek-chat" | "anthropic" | "google";
    borderRadius: string;
  };
}

export default function ErrorState({
  requestId,
  errorMessage,
  themeRequest,
}: ErrorStateProps) {
  const router = useRouter();
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    try {
      setIsRetrying(true);
      toast.loading("Retrying theme generation...", {});

      // Use the regenerateTheme action to create a new theme request
      // Pass the requestId instead of themeId to ensure we're using the correct ID
      const result = await regenerateTheme(
        requestId, // Use requestId which is guaranteed to be a valid request ID
        themeRequest.description,
        {
          format: themeRequest.format,
          includeDarkMode: themeRequest.includeDarkMode,
          includeSidebar: themeRequest.includeSidebar,
          includeChart: themeRequest.includeChart,
          model: themeRequest.model,
          borderRadius: themeRequest.borderRadius as
            | "0"
            | "0.3"
            | "0.5"
            | "0.75"
            | "1",
        }
      );

      if (result.success && result.id) {
        toast.dismiss();
        toast.info("Theme generation started");
        // Navigate to the new theme page
        router.push(`/theme/${result.id}`);
      } else {
        toast.error(`Failed to retry: ${result.error || "Unknown error"}`);
        setIsRetrying(false);
      }
    } catch (error) {
      console.error("Error retrying theme generation:", error);
      toast.error("Failed to retry theme generation");
      setIsRetrying(false);
    }
  };

  const handleCreateNew = () => {
    router.push("/theme/create");
  };

  return (
    <div className="p-8 container mx-auto">
      <div className="bg-destructive/10 border border-destructive rounded-lg p-6">
        <h2 className="text-xl font-bold text-destructive mb-4">
          Theme Generation Failed
        </h2>
        <p className="mb-4">
          {errorMessage ||
            "There was an error generating your theme. Please try again."}
        </p>
        {requestId && (
          <p className="text-sm text-muted-foreground mb-6">
            Request ID: {requestId}
          </p>
        )}
        <div className="flex gap-4">
          <Button
            variant="destructive"
            onClick={handleRetry}
            disabled={isRetrying}
          >
            {isRetrying ? "Retrying..." : "Retry Generation"}
          </Button>
          <Button variant="outline" onClick={handleCreateNew}>
            Create New Theme
          </Button>
        </div>
      </div>
    </div>
  );
}
