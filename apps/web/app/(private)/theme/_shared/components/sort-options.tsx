"use client";

import { useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ArrowDownAZ, ArrowUpAZ, Clock } from "lucide-react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";

export interface SortOptionsProps {
  /**
   * Default sort option
   * @default "newest"
   */
  defaultSort?: "newest" | "oldest" | "name-asc" | "name-desc";
  
  /**
   * Available sort options
   * @default ["newest", "oldest", "name-asc", "name-desc"]
   */
  availableSortOptions?: Array<"newest" | "oldest" | "name-asc" | "name-desc">;
}

/**
 * Shared sort options component for theme pages
 * Handles sorting by date and name
 */
export default function SortOptions({
  defaultSort = "newest",
  availableSortOptions = ["newest", "oldest", "name-asc", "name-desc"],
}: SortOptionsProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [isPending, startTransition] = useTransition();

  // Handle sort change
  const handleSortChange = (value: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);

      if (value && value !== defaultSort) {
        params.set("sort", value);
      } else {
        params.delete("sort");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Map of sort options to their display components
  const sortOptionComponents = {
    newest: {
      label: "Newest first",
      icon: <Clock className="h-4 w-4 rotate-90" />,
    },
    oldest: {
      label: "Oldest first",
      icon: <Clock className="h-4 w-4 -rotate-90" />,
    },
    "name-asc": {
      label: "Name (A-Z)",
      icon: <ArrowDownAZ className="h-4 w-4" />,
    },
    "name-desc": {
      label: "Name (Z-A)",
      icon: <ArrowUpAZ className="h-4 w-4" />,
    },
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground hidden md:inline">
        Sort by:
      </span>
      <div className="relative">
        <Select
          defaultValue={searchParams.get("sort")?.toString() || defaultSort}
          onValueChange={handleSortChange}
          disabled={isPending}
        >
          <SelectTrigger disabled={isPending} className="w-[160px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            {availableSortOptions.map((option) => (
              <SelectItem
                key={option}
                value={option}
                className="flex items-center gap-2"
              >
                {sortOptionComponents[option].icon}
                <span>{sortOptionComponents[option].label}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
