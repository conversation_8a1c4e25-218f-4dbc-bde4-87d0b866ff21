"use client";

import { useEffect, useState, useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  ChartLineIcon,
  Loader2,
  Moon,
  PanelLeftIcon,
  SearchIcon,
  Star,
  X,
} from "lucide-react";
import { useDebouncedCallback } from "use-debounce";

import { Button } from "@chromify/ui/components/button";
import { Input } from "@chromify/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";
import { Separator } from "@chromify/ui/components/separator";
import { Toggle } from "@chromify/ui/components/toggle";
import { cn } from "@chromify/ui/lib/utils";

export interface SearchFilterProps {
  /**
   * Whether to show the favorites filter toggle
   * @default true
   */
  showFavoriteFilter?: boolean;
  
  /**
   * Custom filters to add to the filter bar
   */
  customFilters?: React.ReactNode;
  
  /**
   * Filter options to show
   * @default ["format", "darkMode", "sidebar", "chart"]
   */
  filterOptions?: Array<"format" | "darkMode" | "sidebar" | "chart">;
}

/**
 * Shared search and filter component for theme pages
 * Handles search input, format selection, and toggle filters
 */
export default function SearchFilter({
  showFavoriteFilter = true,
  customFilters,
  filterOptions = ["format", "darkMode", "sidebar", "chart"],
}: SearchFilterProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [isPending, startTransition] = useTransition();

  // Local state for search input
  const [searchValue, setSearchValue] = useState(
    searchParams.get("query")?.toString() || ""
  );

  // Get initial values from URL params
  const [mounted, setMounted] = useState(false);

  // Handle search input with debounce
  const handleSearch = useDebouncedCallback((term: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on new search

      if (term) {
        params.set("query", term);
      } else {
        params.delete("query");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  }, 300);

  // Handle clearing search input
  const handleClearSearch = () => {
    setSearchValue("");
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.delete("query");
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Handle clearing all filters
  const handleClearAllFilters = () => {
    setSearchValue("");
    startTransition(() => {
      const params = new URLSearchParams();
      // Preserve sort option if it exists
      const sortOption = searchParams.get("sort");
      if (sortOption) {
        params.set("sort", sortOption);
      }
      params.set("page", "1");
      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Check if any filters are active
  const hasActiveFilters =
    searchValue ||
    searchParams.has("format") ||
    (showFavoriteFilter && searchParams.has("favorite")) ||
    searchParams.has("darkMode") ||
    searchParams.has("sidebar") ||
    searchParams.has("chart");

  // Handle format selection
  const handleFormatChange = (value: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on filter change

      if (value && value !== "all") {
        params.set("format", value);
      } else {
        params.delete("format");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Handle toggle filters
  const handleToggleFilter = (filterName: string, isActive: boolean) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);
      params.set("page", "1"); // Reset to first page on filter change

      if (isActive) {
        params.set(filterName, "true");
      } else {
        params.delete(filterName);
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  // Set mounted state to handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Only render client-side to avoid hydration mismatch
  if (!mounted) return null;

  return (
    <div className="flex flex-col gap-4 w-full">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        {/* Search input with clear button */}
        <div className="relative flex-1">
          <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search themes..."
            className={cn("pl-10", searchValue && "pr-8")}
            value={searchValue}
            onChange={(e) => {
              const value = e.target.value;
              setSearchValue(value);
              handleSearch(value);
            }}
          />
          {searchValue && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={handleClearSearch}
              type="button"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Format filter */}
        {filterOptions.includes("format") && (
          <div className="relative w-full md:w-[180px]">
            <Select
              defaultValue={searchParams.get("format")?.toString() || "all"}
              onValueChange={handleFormatChange}
              disabled={isPending}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Formats</SelectItem>
                <SelectItem value="hsl">HSL (Tailwind v3)</SelectItem>
                <SelectItem value="oklch">OKLCH (Tailwind v4)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Toggle filters with Clear All button */}
      <div className="flex justify-between items-center">
        <div className="flex flex-wrap gap-2">
          {/* Favorites filter - only shown if showFavoriteFilter is true */}
          {showFavoriteFilter && (
            <Toggle
              variant={searchParams.has("favorite") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("favorite")}
              onPressedChange={(pressed) =>
                handleToggleFilter("favorite", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <Star className="h-4 w-4" />
              <span>Favorites</span>
            </Toggle>
          )}

          {/* Dark Mode filter */}
          {filterOptions.includes("darkMode") && (
            <Toggle
              variant={searchParams.has("darkMode") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("darkMode")}
              onPressedChange={(pressed) =>
                handleToggleFilter("darkMode", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <Moon className="h-4 w-4" />
              <span>Dark Mode</span>
            </Toggle>
          )}

          {/* Sidebar filter */}
          {filterOptions.includes("sidebar") && (
            <Toggle
              variant={searchParams.has("sidebar") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("sidebar")}
              onPressedChange={(pressed) =>
                handleToggleFilter("sidebar", pressed)
              }
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <PanelLeftIcon className="h-4 w-4" />
              <span>Sidebar</span>
            </Toggle>
          )}

          {/* Chart filter */}
          {filterOptions.includes("chart") && (
            <Toggle
              variant={searchParams.has("chart") ? "default" : "outline"}
              size="sm"
              pressed={searchParams.has("chart")}
              onPressedChange={(pressed) => handleToggleFilter("chart", pressed)}
              className={cn("gap-1 relative")}
              disabled={isPending}
            >
              <ChartLineIcon className="h-4 w-4" />
              <span>Charts</span>
            </Toggle>
          )}

          {/* Custom filters */}
          {customFilters}
        </div>

        {/* Clear All button - only shown when filters are active */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAllFilters}
            disabled={isPending}
            className="text-muted-foreground hover:text-foreground"
          >
            {isPending ? (
              <Loader2 className="h-3 w-3 mr-2 animate-spin" />
            ) : (
              <X className="h-3 w-3 mr-2" />
            )}
            Clear All
          </Button>
        )}
      </div>
      <Separator />
    </div>
  );
}
