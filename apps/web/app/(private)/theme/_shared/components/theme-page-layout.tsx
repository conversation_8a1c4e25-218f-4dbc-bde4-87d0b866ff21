import { ReactNode, Suspense } from "react";

import TitleMeta from "@/components/title-meta";
import ThemeCardsSkeleton from "./theme-cards-skeleton";

export interface ThemePageLayoutProps {
  /**
   * Page title
   */
  title: string;

  /**
   * Page description
   */
  description: string;

  /**
   * Search filter component
   */
  searchFilter: ReactNode;

  /**
   * Sort options component
   */
  sortOptions: ReactNode;

  /**
   * Theme list component to be wrapped in Suspense
   */
  themeList: ReactNode;

  /**
   * Suspense key for the theme list
   */
  suspenseKey: string;
}

/**
 * Shared layout component for theme pages
 * Provides consistent structure for theme listing pages
 */
export default function ThemePageLayout({
  title,
  description,
  searchFilter,
  sortOptions,
  themeList,
  suspenseKey,
}: ThemePageLayoutProps) {
  return (
    <>
      <TitleMeta title={title} description={description} />

      {/* Search and filter section */}
      <div className="mb-6">{searchFilter}</div>

      {/* Sort options */}
      <div className="flex justify-end mb-6">{sortOptions}</div>

      {/* Theme cards with suspense - key changes with filters to trigger re-render */}
      <Suspense key={suspenseKey} fallback={<ThemeCardsSkeleton />}>
        {themeList}
      </Suspense>
    </>
  );
}
