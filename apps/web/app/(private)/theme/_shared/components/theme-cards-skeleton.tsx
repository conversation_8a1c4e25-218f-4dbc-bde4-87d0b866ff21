import { Skeleton } from "@chromify/ui/components/skeleton";

/**
 * Skeleton loading component for theme cards
 * Used as a fallback in Suspense boundaries
 */
function ThemeCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array(6)
        .fill(null)
        .map((_, index) => (
          <div key={index} className="h-64 rounded-lg space-y-4 border p-4">
            <div className="flex justify-between">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-4 w-12" />
            </div>

            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-5 w-full" />
            <Skeleton className="h-5 w-5/6" />
            <Skeleton className="h-4 w-20" />
          </div>
        ))}
    </div>
  );
}

export default ThemeCardsSkeleton;
