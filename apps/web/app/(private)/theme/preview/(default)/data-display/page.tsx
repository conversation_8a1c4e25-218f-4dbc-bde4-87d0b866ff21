import TitleMeta from "@/components/title-meta";
import AreaChartStacked from "./_components/charts/chart-area-stacked";
import BarChartMultiple from "./_components/charts/chart-bar-multiple";
import { PieChartDemo } from "./_components/charts/pie-chart";

function Page() {
  return (
    <div className="flex flex-col space-y-8 max-w-none">
      <TitleMeta title="Data Display" description="Charts, Tables, and more" />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AreaChartStacked />
        <BarChartMultiple />
        <PieChartDemo />
      </div>
    </div>
  );
}

export default Page;
