"use client";

import * as React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { Checkbox } from "@chromify/ui/components/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@chromify/ui/components/form";
import { Input } from "@chromify/ui/components/input";
import { Label } from "@chromify/ui/components/label";
import {
  RadioGroup,
  RadioGroupItem,
} from "@chromify/ui/components/radio-group";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";
import { Slider } from "@chromify/ui/components/slider";
import { Switch } from "@chromify/ui/components/switch";
import { Textarea } from "@chromify/ui/components/textarea";
import TitleMeta from "@/components/title-meta";

const formSchema = z.object({
  username: z.string().min(2).max(50),
  email: z.string().email(),
  bio: z.string().max(500).optional(),
  notifications: z.boolean().default(false),
  theme: z.enum(["light", "dark", "system"]),
  marketingEmails: z.boolean().default(true),
  securityEmails: z.boolean().default(true),
  accountType: z.enum(["personal", "professional", "business"]),
  fontScale: z.number().min(0.5).max(2),
});

export default function InputControlsPreview() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema as z.ZodType<z.infer<typeof formSchema>>),
    defaultValues: {
      username: "",
      email: "",
      bio: "",
      notifications: false,
      theme: "system",
      marketingEmails: true,
      securityEmails: true,
      accountType: "personal",
      fontScale: 1,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    toast.info(`Submitted values ${JSON.stringify(values)}`);
  }

  return (
    <div className="flex flex-col space-y-6">
      <TitleMeta
        title="Input Controls"
        description="Preview how your color scheme affects input controls and form elements."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        {/* Left Column - Individual Components */}
        <div className="space-y-8">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div className="p-6 space-y-6">
              <h2 className="text-xl font-semibold">Individual Components</h2>

              {/* Text Input */}
              <div className="space-y-2">
                <Label htmlFor="simple-input">Text Input</Label>
                <Input id="simple-input" placeholder="Enter your text here" />
              </div>

              {/* Textarea */}
              <div className="space-y-2">
                <Label htmlFor="simple-textarea">Textarea</Label>
                <Textarea
                  id="simple-textarea"
                  placeholder="Enter your message"
                  rows={3}
                />
              </div>

              {/* Select */}
              <div className="space-y-2">
                <Label htmlFor="simple-select">Select</Label>
                <Select>
                  <SelectTrigger id="simple-select">
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Options</SelectLabel>
                      <SelectItem value="option1">Option 1</SelectItem>
                      <SelectItem value="option2">Option 2</SelectItem>
                      <SelectItem value="option3">Option 3</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              {/* Checkbox */}
              <div className="flex items-center space-x-2">
                <Checkbox id="simple-checkbox" />
                <Label htmlFor="simple-checkbox">
                  Accept terms and conditions
                </Label>
              </div>

              {/* Radio Group */}
              <div className="space-y-3">
                <Label>Notification Preference</Label>
                <RadioGroup defaultValue="email">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="radio-email" />
                    <Label htmlFor="radio-email">Email</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sms" id="radio-sms" />
                    <Label htmlFor="radio-sms">SMS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="push" id="radio-push" />
                    <Label htmlFor="radio-push">Push Notification</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Switch */}
              <div className="flex items-center justify-between">
                <Label htmlFor="simple-switch">Enable dark mode</Label>
                <Switch id="simple-switch" />
              </div>

              {/* Slider */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Label htmlFor="simple-slider">Volume</Label>
                  <span className="text-sm text-muted-foreground">75%</span>
                </div>
                <Slider
                  id="simple-slider"
                  defaultValue={[75]}
                  max={100}
                  step={1}
                />
              </div>
            </div>
          </div>

          {/* States Card */}
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div className="p-6 space-y-6">
              <h2 className="text-xl font-semibold">Component States</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="mb-2 block">Default</Label>
                    <Input placeholder="Default state" />
                  </div>
                  <div>
                    <Label className="mb-2 block">Focus</Label>
                    <Input
                      className="ring-2 ring-ring ring-offset-1"
                      placeholder="Focused state"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="mb-2 block">Disabled</Label>
                    <Input disabled placeholder="Disabled state" />
                  </div>
                  <div>
                    <Label className="mb-2 block">With Error</Label>
                    <Input
                      placeholder="Error state"
                      className="border-destructive focus-visible:ring-destructive"
                    />
                    <p className="text-xs text-destructive mt-1">
                      This field has an error
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Form Example */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-6">
              Complete Form Example
            </h2>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="johndoe" {...field} />
                      </FormControl>
                      <FormDescription>
                        This is your public display name.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="bio"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bio</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tell us about yourself"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Brief description for your profile. Max 500 characters.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="accountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="personal">Personal</SelectItem>
                          <SelectItem value="professional">
                            Professional
                          </SelectItem>
                          <SelectItem value="business">Business</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the type of account you want to create.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <div>
                    <h3 className="text-base font-medium mb-2">
                      Email Notifications
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Configure what notifications you receive via email.
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="marketingEmails"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Marketing emails</FormLabel>
                          <FormDescription>
                            Receive emails about new products and features.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="securityEmails"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Security emails</FormLabel>
                          <FormDescription>
                            Receive emails about your account security.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="fontScale"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Font Scale</FormLabel>
                      <div className="flex items-center space-x-3">
                        <FormLabel className="text-xs">A</FormLabel>
                        <FormControl>
                          <Slider
                            min={0.5}
                            max={2}
                            step={0.1}
                            defaultValue={[field.value]}
                            onValueChange={(vals) => field.onChange(vals[0])}
                          />
                        </FormControl>
                        <FormLabel className="text-lg">A</FormLabel>
                      </div>
                      <FormDescription>
                        Adjust the font size for the interface.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex gap-4 justify-end">
                  <Button variant="outline" type="button">
                    Cancel
                  </Button>
                  <Button type="submit">Save Changes</Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}
