"use client";

import * as React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>heck,
  CheckCircle2,
  Clock,
  InfoIcon,
  Loader2,
  Terminal,
  X,
  XCircle,
} from "lucide-react";
import { toast } from "sonner";

import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@chromify/ui/components/alert";
import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import { Progress } from "@chromify/ui/components/progress";
import { Skeleton } from "@chromify/ui/components/skeleton";
import TitleMeta from "@/components/title-meta";

export default function FeedbackPreview() {
  const [progress, setProgress] = React.useState(13);

  // Progress demo
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setProgress(66);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="flex flex-col space-y-8">
      <TitleMeta
        title="Feedback"
        description="Alerts, Toasts, Progress, Badges, and more"
      />

      {/* Alerts Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Alerts</h2>
        <div className="grid gap-6">
          {/* Default Alert */}
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              This is a default alert providing neutral information to the user.
            </AlertDescription>
          </Alert>

          {/* Success Alert */}
          <Alert
            variant="default"
            className="border-green-500 bg-green-50 dark:border-green-600 dark:bg-green-950 text-green-800 dark:text-green-300"
          >
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              Your changes have been saved successfully.
            </AlertDescription>
          </Alert>

          {/* Warning Alert */}
          <Alert
            variant="default"
            className="border-yellow-500 bg-yellow-50 dark:border-yellow-600 dark:bg-yellow-950 text-yellow-800 dark:text-yellow-300"
          >
            <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>
              Your storage is almost full. Consider upgrading your plan.
            </AlertDescription>
          </Alert>

          {/* Destructive Alert */}
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              There was an error processing your request. Please try again.
            </AlertDescription>
          </Alert>

          {/* Alert with Action */}
          <Alert>
            <Terminal className="h-4 w-4" />
            <AlertTitle>Update Available</AlertTitle>
            <AlertDescription className="flex items-center justify-between">
              <span>
                A new version is available. Update now for new features.
              </span>
              <Button variant="outline" size="sm" className="ml-4">
                Update
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Toast Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Toasts</h2>
        <div className="flex items-center gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Trigger Examples</h3>
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  toast.message("Default Toast");
                }}
              >
                Default Toast
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  toast.success(
                    "Success! Your action was completed successfully."
                  );
                }}
              >
                With Action
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  toast.info("Scheduled: Catch up");
                }}
              >
                Calendar Event
              </Button>

              <Button
                variant="destructive"
                onClick={() => {
                  toast.error("Error! There was a problem with your request.");
                }}
              >
                Error Toast
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Progress Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Progress</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <div>
              <div className="mb-2 flex justify-between text-sm">
                <div className="font-medium">Default Progress</div>
                <div className="text-muted-foreground">{progress}%</div>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            <div>
              <div className="mb-2 flex justify-between text-sm">
                <div className="font-medium">Indeterminate Progress</div>
              </div>
              <Progress className="h-2" />
            </div>

            <div>
              <div className="mb-2 flex justify-between text-sm">
                <div className="font-medium">Custom Height Progress</div>
                <div className="text-muted-foreground">45%</div>
              </div>
              <Progress value={45} className="h-4" />
            </div>
          </div>

          <div className="space-y-6">
            <div className="rounded-md border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Download Progress</h3>
                <Button variant="ghost" size="sm">
                  Cancel
                </Button>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="mb-1 flex justify-between text-sm">
                    <span>report-2023.pdf</span>
                    <span>88%</span>
                  </div>
                  <Progress value={88} className="h-2" />
                </div>
                <div>
                  <div className="mb-1 flex justify-between text-sm">
                    <span>presentation.pptx</span>
                    <span>34%</span>
                  </div>
                  <Progress value={34} className="h-2" />
                </div>
                <div>
                  <div className="mb-1 flex justify-between text-sm">
                    <span>data.xlsx</span>
                    <span>100%</span>
                  </div>
                  <Progress value={100} className="h-2" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Badges Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Badges</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Badge Variants</h3>
            <div className="flex flex-wrap gap-3">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>

            <h3 className="text-lg font-medium mt-6 mb-4">Sizes & Styles</h3>
            <div className="flex flex-wrap gap-3">
              <Badge className="text-xs">Extra Small</Badge>
              <Badge>Default Size</Badge>
              <Badge className="text-base">Larger</Badge>
              <Badge className="rounded-full">Rounded</Badge>
              <Badge className="px-5">Wide Badge</Badge>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Contextual Usage</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className="border-green-500 text-green-600 dark:text-green-400"
                >
                  <CheckCheck className="mr-1 h-3 w-3" /> Completed
                </Badge>
                <Badge
                  variant="outline"
                  className="border-yellow-500 text-yellow-600 dark:text-yellow-400"
                >
                  <Clock className="mr-1 h-3 w-3" /> Pending
                </Badge>
                <Badge
                  variant="outline"
                  className="border-red-500 text-red-600 dark:text-red-400"
                >
                  <X className="mr-1 h-3 w-3" /> Cancelled
                </Badge>
              </div>

              <div className="p-4 border rounded-md mt-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">User Profile</h4>
                  <Badge>New</Badge>
                </div>
                <div className="text-muted-foreground text-sm">
                  John Doe{" "}
                  <Badge variant="secondary" className="ml-2">
                    Premium
                  </Badge>
                </div>
                <div className="flex items-center justify-between mt-4">
                  <span className="text-sm text-muted-foreground">
                    View profile
                  </span>
                  <ArrowRight className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skeleton Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Skeletons</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Basic Skeletons</h3>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-[80%]" />
              <Skeleton className="h-4 w-[60%]" />
            </div>

            <div className="mt-6 space-y-2">
              <Skeleton className="h-12 w-full rounded-xl" />
              <div className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Loading Card Example</h3>
            <div className="rounded-md border p-6">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>

              <div className="mt-6 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-[80%]" />
              </div>

              <div className="mt-6 flex items-center justify-between">
                <Skeleton className="h-10 w-[120px]" />
                <div className="flex space-x-2">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <Skeleton className="h-10 w-10 rounded-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Loading Section */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-2xl font-semibold mb-6">Loading States</h2>
        <div className="flex gap-6">
          <div className="flex flex-col items-center gap-4">
            <h3 className="text-lg font-medium">Button Loading</h3>
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Please wait
            </Button>
          </div>

          <div className="flex flex-col items-center gap-4">
            <h3 className="text-lg font-medium">Loading Spinner</h3>
            <div className="flex items-center justify-center">
              <Loader2 className="size-6 animate-spin text-primary" />
            </div>
          </div>

          <div className="flex flex-col items-center gap-4">
            <h3 className="text-lg font-medium">Loading Text</h3>
            <div className="flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-primary" />
                <p className="text-sm text-muted-foreground">Loading data...</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
