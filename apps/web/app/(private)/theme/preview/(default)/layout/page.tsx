"use client";

import * as React from "react";
import { CalendarDays, ChevronsUpDown, CreditCard } from "lucide-react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@chromify/ui/components/accordion";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@chromify/ui/components/avatar";
import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { Checkbox } from "@chromify/ui/components/checkbox";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@chromify/ui/components/collapsible";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@chromify/ui/components/dialog";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@chromify/ui/components/hover-card";
import { Input } from "@chromify/ui/components/input";
import { Label } from "@chromify/ui/components/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@chromify/ui/components/popover";
import {
  RadioGroup,
  RadioGroupItem,
} from "@chromify/ui/components/radio-group";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@chromify/ui/components/sheet";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@chromify/ui/components/tabs";
import TitleMeta from "@/components/title-meta";

export default function LayoutComponentsPreview() {
  const [sheetSide, setSheetSide] = React.useState<
    "top" | "right" | "bottom" | "left"
  >("right");

  return (
    <div className="flex flex-col space-y-8">
      <TitleMeta
        title="Layout Components"
        description="Cards, Dialogs, Sheets, and more"
      />

      {/* Cards Section */}
      <section>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-2xl font-semibold mb-6">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Simple Card */}
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Choose what notifications you receive
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="email" />
                    <Label htmlFor="email">Email notifications</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sms" />
                    <Label htmlFor="sms">SMS notifications</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="push" />
                    <Label htmlFor="push">Push notifications</Label>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>

            {/* Interactive Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="space-y-1">
                  <CardTitle>Account Balance</CardTitle>
                  <CardDescription>
                    Your current account balance
                  </CardDescription>
                </div>
                <CreditCard className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$2,500.00</div>
                <p className="text-xs text-muted-foreground">
                  +20% from last month
                </p>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm">
                  Add Funds
                </Button>
                <Button size="sm">Withdraw</Button>
              </CardFooter>
            </Card>

            {/* Card with Image */}
            <Card className="overflow-hidden">
              <div className="h-32 bg-gradient-to-r from-primary to-primary/60" />
              <CardHeader>
                <CardTitle>Premium Plan</CardTitle>
                <CardDescription>Access all premium features</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm">
                  <li className="flex items-center">
                    <div className="mr-2 size-3 rounded-full bg-accent" />
                    Unlimited access
                  </li>
                  <li className="flex items-center">
                    <div className="mr-2 size-3 rounded-full bg-accent" />{" "}
                    Priority support
                  </li>
                  <li className="flex items-center">
                    <div className="mr-2 size-3 rounded-full bg-accent" /> No
                    advertisements
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Upgrade Now</Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Dialog and Sheet Section */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Dialog */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Dialog</h2>
          <div className="flex justify-center">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">Open Dialog</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Edit Profile</DialogTitle>
                  <DialogDescription>
                    Make changes to your profile here. Click save when you're
                    done.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input
                      id="name"
                      defaultValue="John Doe"
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="username" className="text-right">
                      Username
                    </Label>
                    <Input
                      id="username"
                      defaultValue="@johndoe"
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">Save changes</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Sheet */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Sheet</h2>
          <div className="flex flex-col items-center gap-4">
            <div className="flex flex-wrap justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSheetSide("top")}
              >
                Top
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSheetSide("right")}
              >
                Right
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSheetSide("bottom")}
              >
                Bottom
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSheetSide("left")}
              >
                Left
              </Button>
            </div>

            <Sheet>
              <SheetTrigger asChild>
                <Button>Open Sheet ({sheetSide})</Button>
              </SheetTrigger>
              <SheetContent side={sheetSide}>
                <SheetHeader>
                  <SheetTitle>Edit Profile</SheetTitle>
                  <SheetDescription>
                    Make changes to your profile here. Click save when you're
                    done.
                  </SheetDescription>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="sheet-name">Name</Label>
                    <Input id="sheet-name" defaultValue="John Doe" />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="sheet-username">Username</Label>
                    <Input id="sheet-username" defaultValue="@johndoe" />
                  </div>
                </div>
                <SheetFooter>
                  <Button type="submit">Save changes</Button>
                </SheetFooter>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </section>

      {/* Advanced Layout Components */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Accordion */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Accordion</h2>
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger>Is it accessible?</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-2">
              <AccordionTrigger>Is it styled?</AccordionTrigger>
              <AccordionContent>
                Yes. It comes with default styles that match your selected color
                scheme.
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="item-3">
              <AccordionTrigger>Is it animated?</AccordionTrigger>
              <AccordionContent>
                Yes. It's animated by default, but you can disable it if you
                prefer.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        {/* Popover */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Popover</h2>
          <div className="flex flex-col gap-4 items-center">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline">Open Popover</Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Dimensions</h4>
                    <p className="text-sm text-muted-foreground">
                      Set the dimensions for the layer.
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="width">Width</Label>
                      <Input
                        id="width"
                        defaultValue="100%"
                        className="col-span-2 h-8"
                      />
                    </div>
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="height">Height</Label>
                      <Input
                        id="height"
                        defaultValue="25px"
                        className="col-span-2 h-8"
                      />
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex gap-2">
                  <CalendarDays className="h-4 w-4" />
                  <span>Calendar</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Schedule</h4>
                    <p className="text-sm text-muted-foreground">
                      Set your availability for meetings.
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="date">Date</Label>
                      <Input
                        id="date"
                        defaultValue="2023-05-15"
                        className="col-span-2 h-8"
                      />
                    </div>
                    <div className="grid grid-cols-3 items-center gap-4">
                      <Label htmlFor="time">Time</Label>
                      <Input
                        id="time"
                        defaultValue="9:00 AM"
                        className="col-span-2 h-8"
                      />
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </section>

      {/* More Layout Components */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Tabs */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Tabs</h2>
          <Tabs defaultValue="account">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="password">Password</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="account" className="mt-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email-tabs">Email</Label>
                <Input
                  id="email-tabs"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="username-tabs">Username</Label>
                <Input id="username-tabs" placeholder="@johndoe" />
              </div>
            </TabsContent>
            <TabsContent value="password" className="mt-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current">Current Password</Label>
                <Input id="current" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new">New Password</Label>
                <Input id="new" type="password" />
              </div>
            </TabsContent>
            <TabsContent value="settings" className="mt-6 space-y-4">
              <div className="space-y-3">
                <Label>Theme</Label>
                <RadioGroup defaultValue="system">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="light" id="light" />
                    <Label htmlFor="light">Light</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="dark" id="dark" />
                    <Label htmlFor="dark">Dark</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="system" id="system" />
                    <Label htmlFor="system">System</Label>
                  </div>
                </RadioGroup>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Collapsible */}
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-6">Collapsible</h2>
          <Collapsible className="w-full space-y-2">
            <div className="flex items-center justify-between space-x-4 px-4">
              <h4 className="text-sm font-semibold">Advanced Settings</h4>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="w-9 p-0">
                  <ChevronsUpDown className="h-4 w-4" />
                  <span className="sr-only">Toggle</span>
                </Button>
              </CollapsibleTrigger>
            </div>
            <div className="rounded-md border px-4 py-3 font-mono text-sm">
              Maximum file size: 10MB
            </div>
            <CollapsibleContent className="space-y-2">
              <div className="rounded-md border px-4 py-3 font-mono text-sm">
                Allow JPEG, PNG, GIF, WebP formats
              </div>
              <div className="rounded-md border px-4 py-3 font-mono text-sm">
                Enable image compression
              </div>
              <div className="rounded-md border px-4 py-3 font-mono text-sm">
                Set default image quality: 85%
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </section>

      {/* Hover Card */}
      <section className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-6">Hover Card</h2>
        <div className="flex justify-center">
          <HoverCard>
            <HoverCardTrigger asChild>
              <Button variant="link" className="text-primary">
                @johndoe
              </Button>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="flex justify-between space-x-4">
                <Avatar>
                  <AvatarImage src="https://github.com/shadcn.png" />
                  <AvatarFallback>JD</AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <h4 className="text-sm font-semibold">John Doe</h4>
                  <p className="text-sm">
                    UI/UX Designer, passionate about creating beautiful and
                    functional interfaces.
                  </p>
                  <div className="flex items-center pt-2">
                    <CalendarDays className="mr-2 h-4 w-4 opacity-70" />
                    <span className="text-xs text-muted-foreground">
                      Joined December 2021
                    </span>
                  </div>
                </div>
              </div>
            </HoverCardContent>
          </HoverCard>
        </div>
      </section>
    </div>
  );
}
