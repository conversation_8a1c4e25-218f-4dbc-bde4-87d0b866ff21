import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { Star } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>Footer,
  CardHeader,
} from "@chromify/ui/components/card";
import { cn } from "@chromify/ui/lib/utils";
import ThemeCardConfig from "@/components/theme/card/card-config";
import ThemeCardOptions from "@/components/theme/card/card-options";
import SwatchPreview from "@/components/theme/swatch-preview";
import type { DBTheme } from "@/types/theme";
import ActionDropdown from "./action-dropdown";
import ActiveBadge from "./active-badge";

export function HistoryCard({ theme }: { theme: DBTheme }) {
  // Format the date
  const createdAt = new Date(theme.created_at);
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });

  return (
    <Card
      className={cn(
        "overflow-hidden isolate transition-all relative group",
        "border shadow-sm hover:border-primary/30 hover:shadow-md"
      )}
    >
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="flex gap-4">
            <SwatchPreview
              themeColors={{
                colors: theme.colors,
                dark_colors: theme.dark_colors,
              }}
            />
            <Link href={`/theme/${theme.request_id}`}>
              {/* <span className="absolute inset-0 z-10" /> */}
              <Button
                variant={"link"}
                className="px-0 text-base text-foreground"
              >
                {theme.name}
              </Button>
            </Link>
          </div>

          {/* Action dropdown */}
          <ActionDropdown theme={theme} />
        </div>
      </CardHeader>
      <CardContent className="space-y-4 -mt-5 flex-1">
        <ThemeCardOptions
          format={theme.format}
          borderRaduis={theme.theme_requests?.border_radius || "0.5"}
        />
        {/* Other options */}
        <ThemeCardConfig theme={theme} />
        <div className="mb-2 text-sm text-muted-foreground line-clamp-3">
          {theme.theme_requests?.user_prompt}
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <time className="text-xs block text-muted-foreground text-center">
          Created at {timeAgo}
        </time>
        <div className="flex items-center gap-3">
          {theme.is_favorite && (
            <Star className="h-3.5 w-3.5 text-yellow-500 fill-yellow-500" />
          )}
          <ActiveBadge themeName={theme.name} />
        </div>
      </CardFooter>
    </Card>
  );
}
