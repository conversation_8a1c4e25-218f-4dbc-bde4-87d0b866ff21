"use client";

import { MoreHorizontal } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import { cn } from "@chromify/ui/lib/utils";
import CopyButtonWithDropdown from "@/components/theme/copy-button/dropdown";
import DeleteDropdown from "@/components/theme/delete-button/dropdown";
import FavoriteDropdown from "@/components/theme/favi-button/dropdown";
import { generateThemeCss } from "@/lib/utils";
import type { DBTheme } from "@/types/theme";

function ActionDropdown({ theme }: { theme: DBTheme }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn("h-7 w-7 rounded-full transition-opacity relative")}
          onClick={(e) => e.stopPropagation()}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-32 space-y-0.5 z-50">
        <FavoriteDropdown themeId={theme.id} isFavorite={theme.is_favorite} />

        <CopyButtonWithDropdown
          css={generateThemeCss({
            format: theme.format,
            colors: theme.colors,
            dark_colors: theme.dark_colors,
          })}
        />

        <DropdownMenuSeparator />
        <DeleteDropdown
          themeId={theme.id}
          themeName={theme.name}
          isFavorite={theme.is_favorite}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default ActionDropdown;
