import { Skeleton } from "@chromify/ui/components/skeleton";

function ThemeCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array(6)
        .fill(null)
        .map((_, index) => (
          <div key={index}>
            <Skeleton className="h-64 w-full" />
          </div>
        ))}
    </div>
  );
}

export default ThemeCardsSkeleton;
