"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>bar } from "lucide-react";
import { Badge } from "@chromify/ui/components/badge";

interface ThemeVariantIconsProps {
  includeDarkMode: boolean;
  includeCharts?: boolean;
  includeSidebar?: boolean;
}

export default function ThemeVariantIcons({
  includeDarkMode,
  includeCharts,
  includeSidebar,
}: ThemeVariantIconsProps) {
  return (
    <div className="flex gap-2">
      {includeDarkMode && (
        <Badge variant="outline" className="flex items-center gap-1">
          <Moon className="h-3 w-3" />
          <span className="text-xs">Dark Mode</span>
        </Badge>
      )}
      {includeCharts && (
        <Badge variant="outline" className="flex items-center gap-1">
          <PieChart className="h-3 w-3" />
          <span className="text-xs">Charts</span>
        </Badge>
      )}
      {includeSidebar && (
        <Badge variant="outline" className="flex items-center gap-1">
          <Sidebar className="h-3 w-3" />
          <span className="text-xs">Sidebar</span>
        </Badge>
      )}
    </div>
  );
}
