"use client";

import { useTransition } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ArrowDownAZ, ArrowUpAZ, Clock } from "lucide-react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@chromify/ui/components/select";

export default function SortOptions() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  const [isPending, startTransition] = useTransition();

  // Handle sort change
  const handleSortChange = (value: string) => {
    startTransition(() => {
      const params = new URLSearchParams(searchParams);

      if (value && value !== "newest") {
        params.set("sort", value);
      } else {
        params.delete("sort");
      }

      replace(`${pathname}?${params.toString()}`);
    });
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground hidden md:inline">
        Sort by:
      </span>
      <div className="relative">
        <Select
          defaultValue={searchParams.get("sort")?.toString() || "newest"}
          onValueChange={handleSortChange}
          disabled={isPending}
        >
          <SelectTrigger disabled={isPending} className="w-[160px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest" className="flex items-center gap-2">
              <Clock className="h-4 w-4 rotate-90" />
              <span>Newest first</span>
            </SelectItem>
            <SelectItem value="oldest" className="flex items-center gap-2">
              <Clock className="h-4 w-4 -rotate-90" />
              <span>Oldest first</span>
            </SelectItem>
            <SelectItem value="name-asc" className="flex items-center gap-2">
              <ArrowDownAZ className="h-4 w-4" />
              <span>Name (A-Z)</span>
            </SelectItem>
            <SelectItem value="name-desc" className="flex items-center gap-2">
              <ArrowUpAZ className="h-4 w-4" />
              <span>Name (Z-A)</span>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
