"use client";

import { Badge } from "@chromify/ui/components/badge";
import { useThemeStore } from "@/store/theme-store";

function ActiveBadge({ themeName }: { themeName: string }) {
  const { currentTheme } = useThemeStore();
  const isCurrentTheme = themeName === currentTheme.name;
  if (!isCurrentTheme) return null;
  return <div className="flex items-center gap-4">{<Badge>Active</Badge>}</div>;
}

export default ActiveBadge;
