import type { Metadata } from "next";

import {
  SearchFilter,
  SortOptions,
  ThemePageLayout,
} from "../_shared/components";
import ThemeList from "./_components/theme-list";

type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export const metadata: Metadata = {
  title: "Theme History - Your Color Journey",
  description:
    "Browse your previously created color themes, filter by properties, and revisit your design history with Chromify.",
};

async function HistoryPage(props: { searchParams: SearchParams }) {
  const searchParams = await props.searchParams;
  const suspenseKey = `${searchParams.query || ""}-${searchParams.page || "1"}-${searchParams.sort || "newest"}-${JSON.stringify(
    {
      format: searchParams.format,
      favorite: searchParams.favorite,
      darkMode: searchParams.darkMode,
      sidebar: searchParams.sidebar,
      chart: searchParams.chart,
    }
  )}`;

  return (
    <ThemePageLayout
      title="Your Color Journey"
      description="Your design history, always at your fingertips."
      searchFilter={<SearchFilter />}
      sortOptions={<SortOptions />}
      themeList={<ThemeList searchParams={searchParams} />}
      suspenseKey={suspenseKey}
    />
  );
}

export default HistoryPage;
