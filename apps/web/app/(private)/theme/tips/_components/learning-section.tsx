import { <PERSON>rk<PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import {
  <PERSON>,
  CardContent,
  <PERSON>Footer,
  CardHeader,
} from "@chromify/ui/components/card";
import {
  Tabs,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@chromify/ui/components/tabs";
import FeaturedTheme from "../../../dashboard/_components/featured-theme";
import SectionHeader from "../../../dashboard/_components/section-header";
import TipCard from "./tip-card";

export function LearningSection() {
  // Tips & tricks with categories
  const tips = [
    {
      title: "Specific Prompts Work Best",
      description:
        "Be specific about colors, mood, and purpose in your prompts. For example, 'Create a calming blue theme for a meditation app' works better than 'Make a blue theme'. Include details about the intended use, audience, and emotional response you want to evoke.",
      iconName: "lightbulb" as const,
      category: "Prompts",
    },
    {
      title: "Use Reference Images",
      description:
        "Upload reference images to generate themes based on existing color palettes or designs you love. The AI will analyze the colors and create a cohesive theme that matches the visual style of your reference image.",
      iconName: "palette" as const,
      category: "Images",
    },
    {
      title: "Combine Format Options",
      description:
        "Enable both dark mode and sidebar options to create comprehensive themes for complex applications. This ensures your theme works consistently across different UI components and lighting conditions.",
      iconName: "bookOpen" as const,
      category: "Options",
    },
    {
      title: "Test in Real Components",
      description:
        "Always preview your theme in the component library to ensure good contrast and readability. Pay special attention to interactive elements like buttons, inputs, and links to make sure they're clearly distinguishable.",
      iconName: "code" as const,
      category: "Testing",
    },
  ];

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <SectionHeader
          title="Learning & Inspiration"
          description="Tips and featured content"
          icon="lightbulb"
        />
      </CardHeader>
      <CardContent className="pb-2">
        <Tabs defaultValue="tips" className="w-full">
          <TabsList className="mb-4 w-full justify-start">
            <TabsTrigger value="tips" className="text-sm">
              Tips & Tricks
            </TabsTrigger>
            <TabsTrigger value="featured" className="text-sm">
              Featured Theme
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tips" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {tips.map((tip) => (
                <TipCard
                  key={tip.title}
                  title={tip.title}
                  description={tip.description}
                  iconName={tip.iconName}
                  category={tip.category}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="featured">
            <div className="max-w-md mx-auto">
              <FeaturedTheme />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="pt-2 pb-3 flex justify-center border-t">
        <Button
          variant="link"
          size="sm"
          className="text-xs text-muted-foreground"
        >
          <Sparkles className="h-3 w-3 mr-1" />
          View all tips and tutorials
        </Button>
      </CardFooter>
    </Card>
  );
}

export default LearningSection;
