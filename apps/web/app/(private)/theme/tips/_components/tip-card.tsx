import { Book<PERSON><PERSON>, Code, Lightbulb, Palette, Sparkles } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";
import { cn } from "@chromify/ui/lib/utils";

type IconName = "lightbulb" | "sparkles" | "bookOpen" | "code" | "palette";

interface TipCardProps {
  title: string;
  description: string;
  iconName: IconName;
  className?: string;
  category?: string;
}

export function TipCard({
  title,
  description,
  iconName,
  className,
  category,
}: TipCardProps) {
  // Map icon name to component
  const getIcon = () => {
    switch (iconName) {
      case "lightbulb":
        return <Lightbulb className="h-4 w-4 text-primary" />;
      case "sparkles":
        return <Sparkles className="h-4 w-4 text-primary" />;
      case "bookOpen":
        return <BookOpen className="h-4 w-4 text-primary" />;
      case "code":
        return <Code className="h-4 w-4 text-primary" />;
      case "palette":
        return <Palette className="h-4 w-4 text-primary" />;
      default:
        return <Lightbulb className="h-4 w-4 text-primary" />;
    }
  };

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all border relative",
        "hover:shadow-md hover:border-primary/20",
        className
      )}
    >
      <CardHeader className="pb-2 relative">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">{getIcon()}</div>
          <CardTitle className="text-base">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <CardDescription>{description}</CardDescription>
      </CardContent>
      {category && (
        <Badge className="absolute bottom-4 right-2">{category}</Badge>
      )}
    </Card>
  );
}

export default TipCard;
