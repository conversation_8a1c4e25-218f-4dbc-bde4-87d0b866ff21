import { Suspense } from "react";

import { ChatsPageContent } from "@/components/chats/chats-page-content";
import { ChatsPageHeader } from "@/components/chats/chats-page-header";
import { ChatsPageSkeleton } from "@/components/chats/chats-page-skeleton";

export const metadata = {
  title: "Chat History",
  description: "View and manage your chat history",
};

type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export default async function ChatsPage(props: { searchParams: SearchParams }) {
  const searchParams = await props.searchParams;
  // Parse search params with defaults
  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 10;
  const sort = (searchParams.sort as string) || "newest";
  const query = (searchParams.query as string) || "";
  const pinned = searchParams.pinned === "true" ? true : undefined;
  const suspenseKey = `${page}-${pageSize}-${sort}-${query}-${pinned}`;

  return (
    <div className="flex flex-col h-full">
      <ChatsPageHeader />
      <Suspense key={suspenseKey} fallback={<ChatsPageSkeleton />}>
        <ChatsPageContent
          page={page}
          pageSize={pageSize}
          sort={sort}
          query={query}
          pinned={pinned}
        />
      </Suspense>
    </div>
  );
}
