"use client";

import Link from "next/link";
import {
  BarChart3,
  History,
  Lightbulb,
  LucideIcon,
  Share2,
  <PERSON><PERSON><PERSON>,
  Star,
} from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

type IconType =
  | "lightbulb"
  | "sparkles"
  | "barChart"
  | "history"
  | "star"
  | "share";

interface SectionHeaderClientProps {
  title: string;
  description?: string;
  icon?: LucideIcon | IconType;
  viewAllLink?: string;
  viewAllText?: string;
  className?: string;
}

export function SectionHeaderClient({
  title,
  description,
  icon,
  viewAllLink,
  viewAllText = "View All",
  className,
}: SectionHeaderClientProps) {
  // Get the appropriate icon component based on the icon type
  const getIconComponent = () => {
    if (!icon) return null;

    // If icon is already a component, use it directly
    if (typeof icon !== "string") {
      const Icon = icon;
      return <Icon className="h-5 w-5 text-primary" />;
    }

    // Otherwise, map the string to the appropriate component
    switch (icon) {
      case "lightbulb":
        return <Lightbulb className="h-5 w-5 text-primary" />;
      case "sparkles":
        return <Sparkles className="h-5 w-5 text-primary" />;
      case "barChart":
        return <BarChart3 className="h-5 w-5 text-primary" />;
      case "history":
        return <History className="h-5 w-5 text-primary" />;
      case "star":
        return <Star className="h-5 w-5 text-primary" />;
      case "share":
        return <Share2 className="h-5 w-5 text-primary" />;
      default:
        return null;
    }
  };
  return (
    <div className={cn("flex justify-between items-start mb-4", className)}>
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          {getIconComponent()}
          <h2 className="text-xl font-semibold">{title}</h2>
        </div>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      {viewAllLink && (
        <Button variant="link" size="sm" className="text-primary" asChild>
          <Link href={viewAllLink}>{viewAllText}</Link>
        </Button>
      )}
    </div>
  );
}

export default SectionHeaderClient;
