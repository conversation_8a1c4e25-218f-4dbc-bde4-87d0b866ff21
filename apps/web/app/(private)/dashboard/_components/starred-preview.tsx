import { Star } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@chromify/ui/components/card";
import { getStarredThemes } from "@/services/dashboard";
import SectionHeader from "./section-header";
import ThemeCard from "./theme-card";

export default async function StarredPreview() {
  const themes = await getStarredThemes(4);
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Starred Themes"
          description="Your favorite themes"
          icon="star"
          viewAllLink="/theme/stars"
        />
      </CardHeader>
      <CardContent>
        {themes.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {themes.map((theme) => (
              <ThemeCard key={theme.id} theme={theme} compact />
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Star className="h-10 w-10 mx-auto mb-3 text-muted-foreground opacity-50" />
            <h3 className="text-base font-medium mb-2">No starred themes</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Star themes to save them here
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
