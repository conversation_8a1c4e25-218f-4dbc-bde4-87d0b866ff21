import { Share2 } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@chromify/ui/components/card";
import { getPublishedThemes } from "@/services/dashboard";
import SectionHeader from "./section-header";
import ThemeCard from "./theme-card";

export default async function PublishedThemes() {
  const themes = await getPublishedThemes(4);
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Published Themes"
          description="Themes you've shared publicly"
          icon="share"
          viewAllLink="/market"
          viewAllText="View Market"
        />
      </CardHeader>
      <CardContent>
        {themes.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            {themes.map((theme) => (
              <ThemeCard key={theme.id} theme={theme} compact />
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Share2 className="h-10 w-10 mx-auto mb-3 text-muted-foreground opacity-50" />
            <h3 className="text-base font-medium mb-2">No published themes</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Share your themes with the community
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
