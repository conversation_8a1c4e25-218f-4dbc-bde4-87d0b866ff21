"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowRight, Spark<PERSON> } from "lucide-react";

import { Button } from "@chromify/ui/components/button";
import { cn } from "@chromify/ui/lib/utils";

interface PromptSuggestionProps {
  prompt: string;
  className?: string;
  category?: string;
}

export function PromptSuggestion({
  prompt,
  className,
  category,
}: PromptSuggestionProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    // Encode the prompt for URL
    const encodedPrompt = encodeURIComponent(prompt);
    router.push(`/theme/create?prompt=${encodedPrompt}`);
  };

  return (
    <Button
      variant="ghost"
      className={cn(
        "justify-between text-left font-normal text-sm relative overflow-hidden transition-all duration-200",
        "hover:shadow-md hover:border-card/20 hover:bg-accent/10 hover:text-foreground",
        className
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center gap-2 overflow-hidden">
        <Sparkles className="h-3.5 w-3.5 text-primary flex-shrink-0" />
        <span className="truncate">{prompt}</span>
      </div>
      {category && (
        <div className="absolute top-2.5 right-8 bg-muted text-muted-foreground text-[10px] px-1.5 py-0.5 rounded-full">
          {category}
        </div>
      )}
      <ArrowRight
        className={cn(
          "h-4 w-4 text-primary flex-shrink-0 transition-all duration-200",
          "opacity-70",
          isHovered && "opacity-100 translate-x-0.5"
        )}
      />
    </Button>
  );
}

export default PromptSuggestion;
