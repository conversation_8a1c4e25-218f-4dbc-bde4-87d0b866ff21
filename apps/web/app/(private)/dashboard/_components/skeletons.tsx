import { Card, CardContent, CardHeader } from "@chromify/ui/components/card";
import { Skeleton } from "@chromify/ui/components/skeleton";
import SectionHeader from "./section-header";

export function RecentActivitySkeleton() {
  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <SectionHeader
          title="Recent Activity"
          description="Your recently created themes"
          icon="history"
          viewAllLink="/theme/history"
        />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col space-y-3">
              <div className="flex justify-between">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-20" />
              </div>
              <Skeleton className="h-24 w-full" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function StarredSkeleton() {
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Starred Themes"
          description="Your favorite themes"
          icon="star"
          viewAllLink="/theme/stars"
        />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col space-y-2">
              <div className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function PublishedSkeleton() {
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Published Themes"
          description="Themes you've shared publicly"
          icon="share"
          viewAllLink="/market"
          viewAllText="View Market"
        />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col space-y-2">
              <div className="flex justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-16 w-full" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function StatsSkeleton() {
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Stats & Usage"
          description="Your account activity"
          icon="barChart"
        />
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex flex-col items-center">
                <Skeleton className="h-8 w-12 mb-1" />
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-8 w-full mt-2" />
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-3 bg-muted/30 p-3 rounded-lg">
          <div className="flex justify-between items-center">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-16" />
          </div>
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>

        <div className="space-y-3 pt-2 border-t">
          <div className="flex justify-between items-center">
            <Skeleton className="h-5 w-32" />
          </div>
          <Skeleton className="h-4 w-full" />
        </div>
      </CardContent>
    </Card>
  );
}
