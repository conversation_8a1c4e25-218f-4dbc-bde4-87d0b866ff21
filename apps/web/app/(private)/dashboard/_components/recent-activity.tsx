import Link from "next/link";
import { History } from "lucide-react";

import { But<PERSON> } from "@chromify/ui/components/button";
import { Card, CardContent, CardHeader } from "@chromify/ui/components/card";
import { getRecentThemes } from "@/services/dashboard";
import SectionHeader from "./section-header";
import ThemeCard from "./theme-card";

export default async function RecentActivity() {
  const themes = await getRecentThemes();
  return (
    <Card>
      <CardHeader>
        <SectionHeader
          title="Recent Activity"
          description="Your recently created themes"
          icon="history"
          viewAllLink="/theme/history"
        />
      </CardHeader>
      <CardContent>
        {themes.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            {themes.map((theme) => (
              <ThemeCard key={theme.id} theme={theme} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 className="text-lg font-medium mb-2">No themes yet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Create your first theme to see it here
            </p>
            <Button asChild>
              <Link href="/theme/create">Create Theme</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
