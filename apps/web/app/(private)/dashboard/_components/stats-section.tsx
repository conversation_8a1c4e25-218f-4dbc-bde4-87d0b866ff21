import { Database, HelpCircle, <PERSON>rk<PERSON>, Star } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@chromify/ui/components/card";
import { Progress } from "@chromify/ui/components/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { getThemeHistoryData, getThemeStats } from "@/services/dashboard";
import { getStorageLimits, getUserProfile } from "@/services/user";
import SectionHeader from "./section-header";

// Mini sparkline chart component
function MiniSparkline({ data, color }: { data: number[]; color: string }) {
  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min || 1;

  return (
    <div className="flex items-end h-8 gap-[2px]">
      {data.map((value, i) => {
        const height = ((value - min) / range) * 100;
        return (
          <div
            key={i}
            className="w-1 rounded-sm transition-all hover:opacity-80"
            style={{
              height: `${Math.max(10, height)}%`,
              backgroundColor: color,
              opacity: 0.2 + (height / 100) * 0.8,
            }}
          />
        );
      })}
    </div>
  );
}

export default async function StatsSection() {
  // Ensure user profile exists and fetch all data in parallel for better performance
  const [statsData, historyData, userProfileResult] = await Promise.all([
    getThemeStats(),
    getThemeHistoryData(10), // Get data for the last 10 days
    getUserProfile(),
  ]);

  const { totalThemes, totalStarred, totalPublished } = statsData;
  const { themeCreationData, themeViewsData } = historyData;

  // Get subscription information
  const userProfile = userProfileResult;
  const subscriptionTier = userProfile?.subscription_tier || "free";
  const { storageLimit, nextPlan } = getStorageLimits(subscriptionTier);

  // Calculate storage usage
  const storageUsed = Math.min(totalThemes, storageLimit);
  const storagePercentage = (storageUsed / storageLimit) * 100;

  // Format current plan name with proper capitalization
  const currentPlan =
    subscriptionTier.charAt(0).toUpperCase() + subscriptionTier.slice(1);
  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <SectionHeader
          title="Stats & Usage"
          description="Your account activity"
          icon="barChart"
        />
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Theme Counts with Sparklines */}
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col items-center justify-center bg-accent/20 p-2 rounded-md">
              <span className="text-2xl font-bold">{totalThemes}</span>
              <span className="text-xs text-muted-foreground">Total</span>
              <div className="w-full mt-2">
                <MiniSparkline
                  data={themeCreationData}
                  color="var(--primary)"
                />
              </div>
            </div>
            <div className="flex flex-col items-center justify-center bg-accent/20 p-2 rounded-md">
              <span className="text-2xl font-bold">{totalStarred}</span>
              <span className="text-xs text-muted-foreground">Starred</span>
              <div className="w-full mt-2">
                <MiniSparkline
                  data={[...themeCreationData].reverse()}
                  color="var(--primary)"
                />
              </div>
            </div>
            <div className="flex flex-col items-center justify-center bg-accent/20 p-2 rounded-md">
              <span className="text-2xl font-bold">{totalPublished}</span>
              <span className="text-xs text-muted-foreground">Published</span>
              <div className="w-full mt-2">
                <MiniSparkline data={themeViewsData} color="var(--primary)" />
              </div>
            </div>
          </div>
        </div>

        {/* Storage/Quota Information */}
        <div className="space-y-3 bg-accent/20 p-3 rounded-lg">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Storage</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3.5 w-3.5 text-muted-foreground/70" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">
                      {currentPlan === "Free"
                        ? "Free accounts can store up to 100 themes. Upgrade to Pro for more storage."
                        : currentPlan === "Pro"
                          ? "Pro accounts can store up to 1,000 themes. Upgrade to Enterprise for more storage."
                          : "Enterprise accounts can store up to 10,000 themes."}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <Badge variant="outline" className="text-xs font-medium">
              {storageUsed} / {storageLimit}
            </Badge>
          </div>
          <div className="relative pt-1">
            <Progress
              value={storagePercentage}
              className="h-3 rounded-md"
              style={{ backgroundColor: "var(--muted)" }}
            />
            <span
              className="absolute text-[10px] font-medium text-primary-foreground"
              style={{
                top: "50%",
                left: `${Math.min(Math.max(storagePercentage - 5, 3), 95)}%`,
                transform: "translateY(-50%)",
                display: storagePercentage > 10 ? "block" : "none",
              }}
            >
              {Math.round(storagePercentage)}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            {storagePercentage >= 80
              ? currentPlan === "Enterprise"
                ? "You're approaching your storage limit. Contact support for options."
                : `You're approaching your storage limit. Consider upgrading to ${nextPlan}.`
              : "You have plenty of storage available."}
          </p>
        </div>

        {/* Plan Information */}
        <div className="space-y-3 py-2 border-t">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Current Plan:</span>
              <Badge variant="secondary" className="font-medium">
                {currentPlan}
              </Badge>
            </div>
          </div>
          <p className="text-xs text-muted-foreground">
            {currentPlan === "Enterprise"
              ? "You're on our highest tier with all premium features unlocked."
              : `Upgrade to ${nextPlan} for ${currentPlan === "Free" ? "more themes" : "unlimited themes"},
                 advanced features, and priority support.`}
          </p>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-3 pb-3">
        {currentPlan !== "Enterprise" && (
          <Button className="w-full" size="sm" variant="outline">
            <Sparkles className="mr-2 h-3.5 w-3.5" />
            Upgrade to {nextPlan}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
