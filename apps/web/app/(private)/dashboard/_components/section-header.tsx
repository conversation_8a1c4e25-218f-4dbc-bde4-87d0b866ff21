import { LucideIcon } from "lucide-react";

import SectionHeaderClient from "./section-header-client";

type IconType =
  | "lightbulb"
  | "sparkles"
  | "barChart"
  | "history"
  | "star"
  | "share";

interface SectionHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon | IconType;
  viewAllLink?: string;
  viewAllText?: string;
  className?: string;
}

export function SectionHeader({
  title,
  description,
  icon,
  viewAllLink,
  viewAllText = "View All",
  className,
}: SectionHeaderProps) {
  return (
    <SectionHeaderClient
      title={title}
      description={description}
      icon={icon}
      viewAllLink={viewAllLink}
      viewAllText={viewAllText}
      className={className}
    />
  );
}

export default SectionHeader;
