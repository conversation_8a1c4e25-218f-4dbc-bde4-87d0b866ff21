import Link from "next/link";
import { <PERSON><PERSON>, Plus, Sparkles } from "lucide-react";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { Card, CardContent } from "@chromify/ui/components/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import SectionHeader from "./section-header";

export function ActionSection() {
  return (
    <Card className="w-full overflow-hidden">
      <CardContent className="flex flex-col md:flex-row md:justify-between md:items-center">
        <SectionHeader
          title="Create New Theme"
          description="Start from scratch or use a template"
          icon="sparkles"
        />
        <div className="flex gap-2">
          <Button size="lg" className="flex-1" asChild>
            <Link href="/theme/create">
              <Sparkles className="mr-2 h-4 w-4" />
              Create New Theme
            </Link>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-10 w-10">
                <Plus className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href="/theme/create">
                  <Sparkles className="mr-2 h-4 w-4" />
                  <span>From Scratch</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/theme/create?template=true">
                  <Palette className="mr-2 h-4 w-4" />
                  <span>From Template</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
}

export default ActionSection;
