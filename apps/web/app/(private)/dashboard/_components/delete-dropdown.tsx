"use client";

import { useState } from "react";
import { Trash } from "lucide-react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@chromify/ui/components/alert-dialog";
import { DropdownMenuItem } from "@chromify/ui/components/dropdown-menu";
import { deleteTheme } from "@/lib/actions/theme";
import { useThemeStore } from "@/store/theme-store";

interface DeleteButtonProps {
  themeId: string;
  onDeleted?: () => void; // Optional callback for after successful deletion
  themeName: string;
  isFavorite: boolean;
}

function DeleteDropdown({
  themeId,
  onDeleted,
  themeName,
  isFavorite,
}: DeleteButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { removeCustomTheme } = useThemeStore();

  const handleDelete = async () => {
    setIsLoading(true);

    try {
      const result = await deleteTheme(themeId);

      if (result.success) {
        toast.success("The color scheme has been successfully deleted");

        // Update the store
        removeCustomTheme(themeId);

        // Call the onDeleted callback if provided
        if (onDeleted) {
          onDeleted();
        }

        // Close the dialog after everything is done
        setIsOpen(false);
      } else {
        toast.error("Error deleting color scheme");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Error deleting color scheme");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DropdownMenuItem
        onSelect={(e) => {
          e.preventDefault();
          setIsOpen(true);
        }}
        className="space-x-2"
      >
        <Trash className="h-4 w-4 text-destructive" />
        <span className="text-xs">Delete</span>
      </DropdownMenuItem>

      <AlertDialog
        open={isOpen}
        onOpenChange={(open) => {
          // Prevent closing the dialog while deletion is in progress
          if (isLoading && !open) return;
          setIsOpen(open);
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete{" "}
              <span className="font-medium text-primary">{themeName}</span> and
              remove all associated data.
              {isFavorite && (
                <span className="text-destructive font-medium block mt-2">
                  This is one of your favorite color schemes.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default DeleteDropdown;
