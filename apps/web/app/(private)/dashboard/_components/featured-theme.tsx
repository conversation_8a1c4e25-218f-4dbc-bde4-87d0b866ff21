"use client";

import Link from "next/link";
import { Award, ExternalLink, Star } from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@chromify/ui/components/card";

export function FeaturedTheme() {
  // This is a fake featured theme for demonstration
  const featuredTheme = {
    name: "Midnight Gradient",
    description:
      "A stunning dark theme with subtle purple and blue gradients, perfect for modern applications.",
    author: "Chromify Team",
    stars: 128,
    colors: {
      primary: "#8B5CF6",
      secondary: "#3B82F6",
      accent: "#10B981",
      background: "#0F172A",
      foreground: "#F8FAFC",
      card: "#1E293B",
      "card-foreground": "#F1F5F9",
      border: "#334155",
    },
  };

  // Create a preview style object for the card
  const previewStyle = {
    backgroundColor: featuredTheme.colors.background,
    color: featuredTheme.colors.foreground,
    borderColor: featuredTheme.colors.border,
  };

  // Main colors to display
  const mainColors = [
    { name: "primary", color: featuredTheme.colors.primary },
    { name: "secondary", color: featuredTheme.colors.secondary },
    { name: "accent", color: featuredTheme.colors.accent },
    { name: "background", color: featuredTheme.colors.background },
  ];

  return (
    <Card className="overflow-hidden border-primary/20">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Award className="h-4 w-4 text-primary" />
          </div>
          <CardTitle className="text-base">Featured Theme</CardTitle>
        </div>
        <CardDescription className="text-xs flex items-center justify-between">
          <span>Trending in the community</span>
          <Badge variant="outline" className="text-[10px] font-normal">
            <Star className="h-3 w-3 mr-1 fill-primary text-primary" />
            {featuredTheme.stars}
          </Badge>
        </CardDescription>
      </CardHeader>

      {/* Theme preview card */}
      <CardContent className="pb-2">
        <div className="space-y-3">
          <div
            className="rounded-md border p-3 transition-colors"
            style={previewStyle}
          >
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium text-sm">{featuredTheme.name}</h3>
              <Badge
                className="text-[10px]"
                style={{
                  backgroundColor: featuredTheme.colors.primary,
                  color: "white",
                }}
              >
                Preview
              </Badge>
            </div>
            <p
              className="text-xs line-clamp-2 mb-2"
              style={{ color: `${featuredTheme.colors.foreground}99` }}
            >
              {featuredTheme.description}
            </p>

            {/* Color grid */}
            <div className="grid grid-cols-4 gap-1 mt-3">
              {mainColors.map((colorItem) => (
                <div
                  key={colorItem.name}
                  className="flex flex-col items-center"
                >
                  <div
                    className="h-8 w-8 rounded-md border shadow-sm transition-transform hover:scale-110"
                    style={{
                      backgroundColor: colorItem.color,
                      borderColor: featuredTheme.colors.border,
                    }}
                  />
                  <span
                    className="text-[9px] mt-1 capitalize truncate w-full text-center"
                    style={{ color: featuredTheme.colors.foreground }}
                  >
                    {colorItem.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">
              By {featuredTheme.author}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-2">
        <Button size="sm" className="w-full text-xs gap-1" asChild>
          <Link href="/market">
            <span>View in Market</span>
            <ExternalLink className="h-3 w-3" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

export default FeaturedTheme;
