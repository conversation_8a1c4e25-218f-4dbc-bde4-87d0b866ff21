"use client";

import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import {
  ChartLineIcon,
  ExternalLink,
  Moon,
  MoreHorizontal,
  PanelLeftIcon,
  Star,
  Sun,
} from "lucide-react";

import { Badge } from "@chromify/ui/components/badge";
import { Button } from "@chromify/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@chromify/ui/components/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@chromify/ui/components/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@chromify/ui/components/tooltip";
import { cn } from "@chromify/ui/lib/utils";
import CopyButtonWithDropdown from "@/components/theme/copy-button/dropdown";
import { generateThemeCss } from "@/lib/utils";
import { useThemeStore } from "@/store/theme-store";
import type { DBTheme } from "@/types/theme";
import DeleteDropdown from "./delete-dropdown";

interface ThemeCardProps {
  theme: DBTheme;
  compact?: boolean;
}

export function ThemeCard({ theme, compact = false }: ThemeCardProps) {
  const { mode: currentMode } = useThemeStore();
  const hasDarkMode = !!theme.dark_colors;
  // Get the colors based on the current mode (always use light mode for preview)
  const colors =
    currentMode === "dark" ? theme.dark_colors || theme.colors : theme.colors;

  // Format the date
  const createdAt = new Date(theme.created_at);
  const timeAgo = formatDistanceToNow(createdAt, { addSuffix: true });

  // Check if the theme has sidebar or chart colors
  const hasSidebar = !!colors.sidebar;
  const hasCharts = !!colors["chart-1"];

  // Get main colors for display
  const mainColors = [
    { name: "Primary", color: colors.primary },
    { name: "Secondary", color: colors.secondary },
    { name: "Accent", color: colors.accent },
    { name: "Card", color: colors.card },
  ];

  return (
    <Card
      className={cn(
        "overflow-hidden transition-all relative group border",
        "hover:shadow-md hover:border-primary/20",
        // "hover:shadow-md hoveborder-primary/20",
        compact ? "p-2 py-3" : "p-2 py-4"
      )}
    >
      <CardHeader className={cn("relative", compact && "p-2")}>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            {theme.is_favorite && (
              <Star className="h-3.5 w-3.5 text-yellow-500 fill-yellow-500" />
            )}
            <div className="flex flex-col space-y-2">
              <Button
                asChild
                variant={"link"}
                className={cn(
                  "h-auto p-0 text-foreground font-medium",
                  compact ? "text-sm" : "text-base"
                )}
              >
                <Link href={`/theme/${theme.request_id}`}>{theme.name}</Link>
              </Button>
              {!compact && (
                <Badge variant="outline" className="text-xs">
                  {theme.format === "hsl" ? "Tailwind v3" : "Tailwind v4"}
                </Badge>
              )}
            </div>
          </div>
          {/* Quick actions dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-7 w-7 rounded-full",

                  "opacity-0 group-hover:opacity-100 transition-opacity"
                )}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <CopyButtonWithDropdown
                css={generateThemeCss({
                  format: theme.format,
                  colors,
                  dark_colors: theme.dark_colors,
                })}
              />

              <DropdownMenuItem className="space-x-2">
                <ExternalLink className="h-4 w-4" />
                <span className="text-xs">
                  <Link href={`/theme/${theme.request_id}`}>View Details</Link>
                </span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DeleteDropdown
                themeId={theme.id}
                themeName={theme.name}
                isFavorite={theme.is_favorite}
              />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className={cn("space-y-2 -mt-6 flex-1", compact && "p-2")}>
        <div className="flex gap-3 items-center border-t border-b py-1.5 my-1.5 border-border/50">
          <Sun className="size-4 text-primary" />
          <Moon
            className={cn(
              "size-4",
              hasDarkMode ? "text-primary" : "text-muted-foreground/50"
            )}
          />
          <ChartLineIcon
            className={cn(
              "size-4",
              hasCharts ? "text-primary" : "text-muted-foreground/50"
            )}
          />
          <PanelLeftIcon
            className={cn(
              "size-4",
              hasSidebar ? "text-primary" : "text-muted-foreground/50"
            )}
          />
        </div>
        {/*color swatch */}
        <div className="relative mt-4">
          <div className="flex gap-1">
            {mainColors.map((colorItem, index) => (
              <TooltipProvider key={index}>
                <Tooltip delayDuration={300}>
                  <TooltipTrigger asChild>
                    <div
                      className={cn(
                        "h-7 w-7 rounded-md border shadow-sm transition-transform",
                        "transform hover:scale-110"
                      )}
                      style={{ backgroundColor: colorItem.color }}
                    />
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="text-xs">
                    <p>
                      {colorItem.name}: {colorItem.color}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>
        {!compact && (
          <div className="mb-2 text-sm text-muted-foreground line-clamp-2">
            {theme.theme_description}
          </div>
        )}
      </CardContent>

      <CardFooter className={cn("space-x-2", compact && "p-2")}>
        <time className="text-xs block text-muted-foreground">{timeAgo}</time>
        <div className="flex items-center gap-1">
          {theme.is_public && (
            <Badge variant="outline" className="text-xs mr-1">
              Public
            </Badge>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

export default ThemeCard;
