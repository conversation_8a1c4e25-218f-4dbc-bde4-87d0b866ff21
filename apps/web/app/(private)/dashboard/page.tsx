import { Suspense } from "react";
import type { Metadata } from "next";

import TitleMeta from "@/components/title-meta";
import ActionSection from "./_components/action-section";
import PublishedThemes from "./_components/published-themes";
import RecentActivity from "./_components/recent-activity";
import {
  PublishedSkeleton,
  RecentActivitySkeleton,
  StarredSkeleton,
  StatsSkeleton,
} from "./_components/skeletons";
import StarredPreview from "./_components/starred-preview";
import StatsSection from "./_components/stats-section";

export const metadata: Metadata = {
  title: "Dashboard - Your Color Theme Hub",
  description:
    "Access your personalized Chromify dashboard to create new themes, view statistics, and manage your favorite color palettes.",
};

export default function DashboardPage() {
  return (
    <>
      <TitleMeta
        title="Dashboard"
        description="Access your personalized Chromify dashboard to create new themes, view statistics, and manage your favorite color palettes."
      />

      {/* Main Content Area - Reordered for better priority */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="md:col-span-full">
          <ActionSection />
        </div>

        <div className="space-y-6 lg:col-span-3 grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Recent activity and stats (now 2/3 width) */}
          <Suspense fallback={<RecentActivitySkeleton />}>
            <div className="space-y-6 lg:col-span-2">
              <RecentActivity />
            </div>
          </Suspense>
          <Suspense fallback={<StatsSkeleton />}>
            <div className="space-y-6 lg:col-span-1">
              <StatsSection />
            </div>
          </Suspense>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 col-span-full gap-6">
          <Suspense fallback={<StarredSkeleton />}>
            <StarredPreview />
          </Suspense>
          <Suspense fallback={<PublishedSkeleton />}>
            <PublishedThemes />
          </Suspense>
        </div>
      </div>
    </>
  );
}
