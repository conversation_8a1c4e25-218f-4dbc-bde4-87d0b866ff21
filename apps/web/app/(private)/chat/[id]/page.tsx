import { Message } from "ai";

import Chat from "@/components/chat";
import { loadChatMessages } from "@/services/chat";

async function ChatPage(props: { params: Promise<{ id: string }> }) {
  const { id } = await props.params;
  const messages = await loadChatMessages(id);
  if (!messages) {
    return <div>Chat not found</div>;
  }
  return (
    <div className="w-full">
      <Chat id={id} initialMessages={messages as unknown as Message[]} />
    </div>
  );
}

export default ChatPage;
