"use client";

import { <PERSON><PERSON> } from "@chromify/ui/components/button";
import { Card, CardContent, CardHeader } from "@chromify/ui/components/card";
import { Checkbox } from "@chromify/ui/components/checkbox";
import { Label } from "@chromify/ui/components/label";
import { cn } from "@chromify/ui/lib/utils";
import {
  ThemeSvgDark,
  ThemeSvglight,
  ThemeSvgSystem,
} from "@/components/svgs/theme";
import { useTheme } from "@/components/theme-provider";
import { useThemeStore } from "@/store/theme-store";

function ThemeSelect() {
  // Updated to use new theme state properties
  const { currentTheme, mode, setMode } = useThemeStore();
  const { resetToDefaultTheme } = useTheme();

  const hasCustomThemes =
    currentTheme.name !== "Default Light" &&
    currentTheme.name !== "Default Dark";
  return (
    <Card>
      <CardHeader className="text-xl">Appearance</CardHeader>
      <CardContent className="text-muted-foreground text-sm">
        <div className="grid grid-cols-6 gap-6">
          <div className="col-span-2 space-y-4">
            <div>Theme mode</div>
            <p>
              Choose how Chromify looks to you. Select a single theme, or sync
              with your system.
            </p>
          </div>
          <div className="col-start-3 col-span-full space-y-4">
            <p>Chromify will use your selected theme</p>
            <div className="grid grid-cols-2 gap-4">
              {/* Dark mode */}
              <div
                className={cn(
                  "p-2 border rounded-md cursor-pointer",
                  mode === "dark" && "border-primary"
                )}
                onClick={() => {
                  setMode("dark");
                }}
              >
                <div className="flex flex-col space-y-2">
                  <ThemeSvgDark />
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="dark-mode"
                      checked={mode === "dark"}
                      onCheckedChange={() => {
                        setMode("dark");
                      }}
                    />
                    <Label
                      htmlFor="dark-mode"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Dark
                    </Label>
                  </div>
                </div>
              </div>

              {/* Light mode */}
              <div
                className={cn(
                  "p-2 border rounded-md cursor-pointer",
                  mode === "light" && "border-primary"
                )}
                onClick={() => {
                  setMode("light");
                }}
              >
                <div className="flex flex-col space-y-2">
                  <ThemeSvglight />
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="light-mode"
                      checked={mode === "light"}
                      onCheckedChange={() => {
                        setMode("light");
                      }}
                    />
                    <Label
                      htmlFor="light-mode"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Light
                    </Label>
                  </div>
                </div>
              </div>

              {/* System mode */}
              <div
                className={cn(
                  "p-2 border rounded-md cursor-pointer transition-all",
                  mode === "system" && "border-primary"
                )}
                onClick={() => setMode("system")}
              >
                <div className="flex flex-col space-y-2">
                  <ThemeSvgSystem />
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="system-mode"
                      checked={mode === "system"}
                      onCheckedChange={() => setMode("system")}
                    />
                    <Label
                      htmlFor="system-mode"
                      className="text-sm font-medium cursor-pointer"
                    >
                      System
                    </Label>
                  </div>
                </div>
              </div>

              {hasCustomThemes && (
                <div className="p-2 border rounded-md flex flex-col gap-4 items-center justify-center">
                  <p>You're using a custom theme</p>
                  <Button onClick={resetToDefaultTheme}>
                    Reset to default
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ThemeSelect;
