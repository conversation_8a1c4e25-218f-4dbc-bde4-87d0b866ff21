import { currentUser } from "@clerk/nextjs/server";

import { <PERSON>, CardContent, CardHeader } from "@chromify/ui/components/card";

async function AccountInfo() {
  const user = await currentUser();
  return (
    <Card>
      <CardHeader className="text-xl">Account Information</CardHeader>

      <CardContent className="text-muted-foreground space-y-4">
        <div className="grid grid-cols-6 gap-6 text-sm">
          <div className="col-span-2">Email</div>
          <div className="col-start-3 col-span-full">
            {user?.emailAddresses[0]?.emailAddress}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default AccountInfo;
