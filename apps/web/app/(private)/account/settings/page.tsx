import type { Metada<PERSON> } from "next";

import TitleMeta from "@/components/title-meta";
import AccountInfo from "./_components/acccount-info";
import ThemeSelect from "./_components/theme-select";

export const metadata: Metadata = {
  title: "Account Settings - Customize Your Experience",
  description:
    "Manage your Chromify account settings, update your profile information, and customize your theme preferences.",
};

function SettingsPage() {
  return (
    <div>
      <TitleMeta
        title="Account Settings"
        description="Manage your Chromify account settings, update your profile information, and customize your theme preferences."
      />
      <div className="space-y-10">
        <AccountInfo />
        <ThemeSelect />
      </div>
    </div>
  );
}

export default SettingsPage;
