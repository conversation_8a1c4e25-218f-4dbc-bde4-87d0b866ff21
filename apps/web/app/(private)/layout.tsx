import type { <PERSON>ada<PERSON> } from "next";
import { cookies } from "next/headers";

import { SidebarInset, SidebarProvider } from "@chromify/ui/components/sidebar";
import { AppSidebar } from "@/components/sidebar/app-sidebar";
import { MobileMenuTrigger } from "@/components/sidebar/mobile-menu-trigger";

export const metadata: Metadata = {
  title: "Dashboard - Manage Your Color Themes",
  description:
    "Access your personalized Chromify dashboard to create, manage, and explore AI-generated color themes for your web development projects.",
};

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";
  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <AppSidebar />
      <SidebarInset>
        <MobileMenuTrigger />
        <main className="container mx-auto py-8">{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
}
