import { z } from "zod";

export const ColorFormatSchema = z.enum(["hsl", "oklch"]);
export const ThemeModeSchema = z.enum(["light", "dark"]);
export const HueCategory = z.enum([
  "red",
  "orange",
  "amber",
  "yellow",
  "lime",
  "green",
  "emerald",
  "teal",
  "cyan",
  "sky",
  "blue",
  "indigo",
  "violet",
  "purple",
  "fuchsia",
  "pink",
  "rose",
  "gray", // For grayscale themes
  "slate", // For slate-based neutrals
  "stone", // For warmer neutrals
]);

/**
 * Schema for colors in a theme
 */
export const ColorSchema = z.object({
  background: z.string().describe("Main background color of the UI"),
  foreground: z
    .string()
    .describe("Main text color that contrasts with background"),
  card: z.string().describe("Background color for card elements"),
  "card-foreground": z.string().describe("Text color for card elements"),
  popover: z
    .string()
    .describe("Background color for popover/dropdown elements"),
  "popover-foreground": z
    .string()
    .describe("Text color for popover/dropdown elements"),
  primary: z.string().describe("Primary brand/action color"),
  "primary-foreground": z
    .string()
    .describe("Text color that contrasts with primary color"),
  secondary: z.string().describe("Secondary color for UI elements"),
  "secondary-foreground": z
    .string()
    .describe("Text color that contrasts with secondary color"),
  muted: z
    .string()
    .describe("Subdued background color for less prominent elements"),
  "muted-foreground": z
    .string()
    .describe("Subdued text color for less prominent elements"),
  accent: z.string().describe("Accent color for highlighting UI elements"),
  "accent-foreground": z
    .string()
    .describe("Text color that contrasts with accent color"),
  destructive: z.string().describe("Color for destructive actions like delete"),
  border: z.string().describe("Color used for borders in the UI"),
  input: z.string().describe("Border color for input elements"),
  ring: z.string().describe("Focus ring color for interactive elements"),
  radius: z.string().describe("Border radius value from user input"),

  // Optional sidebar colors
  sidebar: z.string().optional().describe("Background color for sidebar"),
  "sidebar-foreground": z
    .string()
    .optional()
    .describe("Text color for sidebar"),
  "sidebar-primary": z
    .string()
    .optional()
    .describe("Primary color for sidebar elements"),
  "sidebar-primary-foreground": z
    .string()
    .optional()
    .describe("Text color that contrasts with sidebar-primary"),
  "sidebar-accent": z
    .string()
    .optional()
    .describe("Accent color for sidebar elements"),
  "sidebar-accent-foreground": z
    .string()
    .optional()
    .describe("Text color that contrasts with sidebar-accent"),
  "sidebar-border": z
    .string()
    .optional()
    .describe("Border color for sidebar elements"),
  "sidebar-ring": z
    .string()
    .optional()
    .describe("Focus ring color for interactive sidebar elements"),

  // Optional chart colors
  "chart-1": z.string().optional().describe("First data visualization color"),
  "chart-2": z.string().optional().describe("Second data visualization color"),
  "chart-3": z.string().optional().describe("Third data visualization color"),
  "chart-4": z.string().optional().describe("Fourth data visualization color"),
  "chart-5": z.string().optional().describe("Fifth data visualization color"),
});
/**
 * Schema for dark mode colors in a theme
 */
export const dark_colorschema = ColorSchema.omit({ radius: true }).describe(
  "Dark mode colors in a theme, omitting radius"
);

/**
 * Schema for a color theme
 */
export const ThemeSchema = z.object({
  name: z
    .string()
    .describe("The name of the theme - be creative but descriptive"),
  description: z
    .string()
    .describe(
      "A description of the theme. Be sure to explain color theory choices"
    ),
  format: z
    .enum(["hsl", "oklch"])
    .describe("Color format specification - must be exactly 'hsl' or 'oklch'"),
  colors: ColorSchema.describe("The set of light mode colors for the theme"),
  dark_colors: dark_colorschema
    .optional()
    .describe(
      "Optional dark mode colors for the theme - omits radius property"
    ),
  analysis: z
    .array(z.string())
    .describe(
      "Report on the characters of the theme. Use language that users can understand, do not use jargon."
    ),
  primaryHue: HueCategory.describe(
    "The primary color category that best represents this theme based on Tailwind color categories"
  ),
});

/**
 * Schema for base colors
 */

export const BaseColorSchema = z.object({
  colors: z.object({
    background: z.string().describe("Main background color of the UI"),
    foreground: z
      .string()
      .describe("Main text color that contrasts with background"),
    primary: z.string().describe("Primary brand/action color"),
    "primary-foreground": z
      .string()
      .describe("Text color that contrasts with primary color"),
  }),
  dark_colors: z
    .object({
      background: z.string().describe("Main background color of the UI"),
      foreground: z
        .string()
        .describe("Main text color that contrasts with background"),
      primary: z.string().describe("Primary brand/action color"),
      "primary-foreground": z
        .string()
        .describe("Text color that contrasts with primary color"),
    })
    .optional(),
  borderRadius: z.string().describe("Radius for rounded corners"),
  includeDarkMode: z.boolean().describe("Whether to include dark mode colors"),
  includeSidebar: z.boolean().describe("Whether to include sidebar colors"),
  includeChart: z.boolean().describe("Whether to include chart colors"),
  format: ColorFormatSchema,
  description: z.string().describe("The theme description from user"),
});

/**
 * Schema for the color theme generation API request
 */
export const ThemeRequestSchema = z.object({
  description: z.string(), // Note: This field is stored as user_prompt in the database
  format: ColorFormatSchema,
  includeDarkMode: z.boolean(),
  includeSidebar: z.boolean(),
  includeChart: z.boolean(),
  imageData: z.string().optional().nullable(),
  model: z.enum(["deepseek-chat", "anthropic", "google"]),
  borderRadius: z.enum(["0", "0.3", "0.5", "0.75", "1"]),
});

/**
 * Schema for theme form validation
 */
export const ThemeFormSchema = z
  .object({
    description: z.string().min(0), // Note: This field is stored as user_prompt in the database
    format: ColorFormatSchema,
    includeDarkMode: z.boolean(),
    includeSidebar: z.boolean(),
    includeChart: z.boolean(),
    imageData: z.string().optional(),
    borderRadius: z.enum(["0", "0.3", "0.5", "0.75", "1"]),
  })
  .refine(
    (data) => {
      // Either description or imageData must be provided
      return !!data.description || !!data.imageData;
    },
    {
      message: "You must provide either a description or an image",
      path: ["description"],
    }
  );

export type ThemeFormValues = z.infer<typeof ThemeFormSchema>;
export type ThemeGenerationRequest = z.infer<typeof ThemeRequestSchema>;
export type AITheme = z.infer<typeof ThemeSchema>;
export type AIColor = z.infer<typeof ColorSchema | typeof dark_colorschema>;
export type AIColorFormat = z.infer<typeof ColorFormatSchema>;
export type AIThemeMode = z.infer<typeof ThemeModeSchema>;
export type BaseColors = z.infer<typeof BaseColorSchema>;
export type APIError = {
  error: string;
  details: string | z.ZodError["errors"];
};
