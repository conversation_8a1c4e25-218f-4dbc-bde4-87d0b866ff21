import { z } from "zod";

// Project Industry options
export const ProjectIndustrySchema = z.enum([
  "technology",
  "finance",
  "healthcare",
  "education",
  "retail",
  "entertainment",
  "other",
] as const);

// Project Type options
export const ProjectTypeSchema = z.enum([
  "website",
  "app",
  "dashboard",
  "ecommerce",
  "portfolio",
  "other",
] as const);

// Project Tone options
export const ProjectToneSchema = z.enum([
  "professional",
  "creative",
  "friendly",
  "minimal",
  "bold",
] as const);

// Technical Framework options
export const TechnicalFrameworkSchema = z.array(
  z.enum(["tailwind", "nextjs", "shadcn", "none"] as const)
);

// Format Preference options
export const FormatPreferenceSchema = z.enum([
  "hsl",
  "oklch",
  "unknown",
] as const);

// Onboarding Form Schema
export const OnboardingFormSchema = z.object({
  // Step 1: Basic User Information
  username: z.string().optional(),

  // Step 2: Project Context
  projectType: ProjectTypeSchema,
  projectTypeOther: z.string().optional(),
  projectIndustry: ProjectIndustrySchema,
  projectIndustryOther: z.string().optional(),

  // Step 3: Design Needs
  colorVision: z.union([
    z.string().min(3, "Please describe your color vision").max(500),
    z.literal(""),
  ]),
  projectTone: ProjectToneSchema,
  darkMode: z.boolean(), // Remove .default() here

  // Step 4: Technical Details (Optional)
  technicalFrameworks: TechnicalFrameworkSchema,
  formatPreference: FormatPreferenceSchema,
});

// Type for the form values
export type OnboardingFormValues = z.infer<typeof OnboardingFormSchema>;

// Type for the form steps
export enum FormStep {
  UserInfo = 0,
  ProjectContext = 1,
  DesignNeeds = 2,
  TechnicalDetails = 3,
}

// Export enum value types
export type ProjectIndustry = z.infer<typeof ProjectIndustrySchema>;
export type ProjectType = z.infer<typeof ProjectTypeSchema>;
export type ProjectTone = z.infer<typeof ProjectToneSchema>;
export type TechnicalFramework = z.infer<
  typeof TechnicalFrameworkSchema
>[number];
export type FormatPreference = z.infer<typeof FormatPreferenceSchema>;
