{"name": "chromify-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build -- --turbopack", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md,js,jsx,json,css,scss}\""}, "devDependencies": {"@chromify/eslint-config": "workspace:*", "@chromify/typescript-config": "workspace:*", "@ianvs/prettier-plugin-sort-imports": "^4.1.1", "prettier": "^3.2.5", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}, "dependencies": {"install": "^0.13.0", "sugar-high": "^0.9.3"}}