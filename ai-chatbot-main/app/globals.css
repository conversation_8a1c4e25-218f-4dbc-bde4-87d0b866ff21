@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --card: oklch(0.99 0.005 240);
  --ring: oklch(0.8 0.1 130);
  --input: oklch(0.9 0.01 240);
  --muted: oklch(0.92 0.01 240);
  --accent: oklch(0.92 0.04 130);
  --border: oklch(0.9 0.01 240);
  --radius: 0.5rem;
  --chart-1: oklch(0.8 0.15 130);
  --chart-2: oklch(0.75 0.14 180);
  --chart-3: oklch(0.7 0.13 230);
  --chart-4: oklch(0.65 0.12 280);
  --chart-5: oklch(0.7 0.13 30);
  --popover: oklch(0.98 0.01 240);
  --primary: oklch(0.8 0.15 130);
  --sidebar: oklch(0.96 0.01 240);
  --secondary: oklch(0.94 0.02 240);
  --background: oklch(0.98 0.01 240);
  --foreground: oklch(0.25 0.02 240);
  --destructive: oklch(0.7 0.15 30);
  --sidebar-ring: oklch(0.8 0.1 130);
  --sidebar-accent: oklch(0.92 0.04 130);
  --sidebar-border: oklch(0.88 0.01 240);
  --card-foreground: oklch(0.25 0.02 240);
  --sidebar-primary: oklch(0.8 0.15 130);
  --muted-foreground: oklch(0.5 0.02 240);
  --accent-foreground: oklch(0.3 0.02 240);
  --popover-foreground: oklch(0.25 0.02 240);
  --primary-foreground: oklch(0.98 0.005 240);
  --sidebar-foreground: oklch(0.3 0.02 240);
  --secondary-foreground: oklch(0.3 0.02 240);
  --sidebar-accent-foreground: oklch(0.3 0.02 240);
  --sidebar-primary-foreground: oklch(0.98 0.005 240);
}

.dark {
  --card: oklch(0.22 0.01 240);
  --ring: oklch(0.75 0.18 130);
  --input: oklch(0.3 0.02 240);
  --muted: oklch(0.28 0.02 240);
  --accent: oklch(0.3 0.05 130);
  --border: oklch(0.3 0.02 240);
  --chart-1: oklch(0.75 0.18 130);
  --chart-2: oklch(0.7 0.16 180);
  --chart-3: oklch(0.65 0.15 230);
  --chart-4: oklch(0.6 0.14 280);
  --chart-5: oklch(0.65 0.15 30);
  --popover: oklch(0.2 0.01 240);
  --primary: oklch(0.75 0.18 130);
  --sidebar: oklch(0.18 0.01 240);
  --secondary: oklch(0.25 0.02 240);
  --background: oklch(0.2 0.01 240);
  --foreground: oklch(0.9 0.01 240);
  --destructive: oklch(0.7 0.15 30);
  --sidebar-ring: oklch(0.75 0.18 130);
  --sidebar-accent: oklch(0.3 0.05 130);
  --sidebar-border: oklch(0.25 0.02 240);
  --card-foreground: oklch(0.9 0.01 240);
  --sidebar-primary: oklch(0.75 0.18 130);
  --muted-foreground: oklch(0.7 0.01 240);
  --accent-foreground: oklch(0.9 0.01 240);
  --popover-foreground: oklch(0.9 0.01 240);
  --primary-foreground: oklch(0.15 0.01 240);
  --sidebar-foreground: oklch(0.85 0.01 240);
  --secondary-foreground: oklch(0.9 0.01 240);
  --sidebar-accent-foreground: oklch(0.9 0.01 240);
  --sidebar-primary-foreground: oklch(0.15 0.01 240);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: var(--font-playfair);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  button {
    @apply cursor-pointer;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Custom thin scrollbar */
  .scrollbar-thin::-webkit-scrollbar {
    height: 4px;
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }

  /* Markdown content styles */

  .markdown-content a {
    @apply text-primary hover:underline;
  }

  .markdown-content img {
    @apply my-4 max-w-full rounded-md;
  }

  .markdown-content hr {
    @apply my-4 border-t border-border;
  }

  .markdown-content code:not(pre code) {
    @apply rounded bg-muted px-1 py-0.5 font-mono text-sm;
  }

  /* Custom animations */
  @keyframes subtle-zoom {
    0% {
      transform: scale(1.05);
    }
    50% {
      transform: scale(1.08);
    }
    100% {
      transform: scale(1.05);
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-delay {
    0%,
    30% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-subtle-zoom {
    animation: subtle-zoom 20s infinite ease-in-out;
  }

  .animate-fade-in {
    animation: fade-in 1s ease-out forwards;
  }

  .animate-fade-in-delay {
    animation: fade-in-delay 1.5s ease-out forwards;
  }

  .delay-300 {
    animation-delay: 300ms;
  }
}

@utility container {
  padding-inline: 1rem;
  margin-left: auto;
  margin-right: auto;

  @variant sm {
    padding-inline: 1.5rem;
  }

  @variant md {
    padding-inline: 2rem;
  }

  @variant lg {
    padding-inline: 2.5rem;
  }

  @variant xl {
    padding-inline: 3rem;
  }
}
