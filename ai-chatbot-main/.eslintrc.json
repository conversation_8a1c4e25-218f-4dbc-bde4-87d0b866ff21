{"extends": ["next/core-web-vitals", "plugin:import/recommended", "plugin:import/typescript", "prettier", "plugin:tailwindcss/recommended"], "plugins": ["tailwindcss"], "rules": {"tailwindcss/no-custom-classname": "off", "tailwindcss/classnames-order": "off"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}, "ignorePatterns": ["**/components/ui/**"]}